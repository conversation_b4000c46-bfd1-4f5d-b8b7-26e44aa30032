{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\domain\\base-aggregate-root.ts", "mappings": ";;;AAAA,+CAA2C;AAI3C;;;;;;;;;;;;;GAaG;AACH,MAAsB,iBAAqB,SAAQ,wBAAa;IAG9D,YAAY,KAAQ,EAAE,EAAmB;QACvC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAHX,kBAAa,GAAsB,EAAE,CAAC;IAI9C,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,iDAAiD;IACtF,CAAC;IAED;;;;OAIG;IACO,cAAc,CAAC,WAA4B;QACnD,sDAAsD;QACtD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAC3C,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CACnD,CAAC;QAEF,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACO,iBAAiB,CAAC,WAA4B;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CACxC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CACnD,CAAC;QAEF,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,WAAW;QAChB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QAC1B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACjC,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,oBAAoB;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,oBAAoB;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACI,eAAe,CACpB,SAAyC;QAEzC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAC9B,KAAK,CAAC,EAAE,CAAC,KAAK,YAAY,SAAS,CACxB,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CACpB,SAAyC;QAEzC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,YAAY,SAAS,CAAC,CAAC;IACtE,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,cAAc;QAMnB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;YAChC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,KAAK,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACO,kBAAkB;QAC1B,qDAAqD;QACrD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;;OAGG;IACO,kBAAkB;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC7C,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;gBACjC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE;gBACjC,UAAU,EAAE,KAAK,CAAC,UAAU;gBAC5B,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC;YACH,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,KAAK;QACV,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC;QAC3B,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACtB,MAAM,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,MAA6B;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,MAAM,CAAC,UAAU,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;CACF;AAzMD,8CAyMC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\domain\\base-aggregate-root.ts"], "sourcesContent": ["import { BaseEntity } from './base-entity';\r\nimport { BaseDomainEvent } from './base-domain-event';\r\nimport { UniqueEntityId } from '../value-objects/unique-entity-id.value-object';\r\n\r\n/**\r\n * Base Aggregate Root\r\n * \r\n * Abstract base class for all aggregate roots in the domain layer.\r\n * Provides event handling capabilities and ensures domain invariants.\r\n * \r\n * Key responsibilities:\r\n * - Manage domain events within the aggregate boundary\r\n * - Ensure consistency and invariants across the aggregate\r\n * - Provide event publishing capabilities\r\n * - Handle aggregate lifecycle management\r\n * \r\n * @template T The props type for the aggregate root\r\n */\r\nexport abstract class BaseAggregateRoot<T> extends BaseEntity<T> {\r\n  private _domainEvents: BaseDomainEvent[] = [];\r\n\r\n  constructor(props: T, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  /**\r\n   * Get all domain events for this aggregate\r\n   */\r\n  get domainEvents(): BaseDomainEvent[] {\r\n    return this._domainEvents.slice(); // Return a copy to prevent external modification\r\n  }\r\n\r\n  /**\r\n   * Add a domain event to the aggregate\r\n   * \r\n   * @param domainEvent The domain event to add\r\n   */\r\n  protected addDomainEvent(domainEvent: BaseDomainEvent): void {\r\n    // Check if event already exists to prevent duplicates\r\n    const existingEvent = this._domainEvents.find(\r\n      event => event.eventId.equals(domainEvent.eventId)\r\n    );\r\n\r\n    if (!existingEvent) {\r\n      this._domainEvents.push(domainEvent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a specific domain event\r\n   * \r\n   * @param domainEvent The domain event to remove\r\n   */\r\n  protected removeDomainEvent(domainEvent: BaseDomainEvent): void {\r\n    const index = this._domainEvents.findIndex(\r\n      event => event.eventId.equals(domainEvent.eventId)\r\n    );\r\n\r\n    if (index !== -1) {\r\n      this._domainEvents.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all domain events\r\n   * This is typically called after events have been published\r\n   */\r\n  public clearEvents(): void {\r\n    this._domainEvents = [];\r\n  }\r\n\r\n  /**\r\n   * Mark events as dispatched\r\n   * This is called by the event dispatcher after successful publishing\r\n   */\r\n  public markEventsForDispatch(): void {\r\n    this._domainEvents.forEach(event => {\r\n      event.markAsDispatched();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Check if the aggregate has any unpublished events\r\n   */\r\n  public hasUnpublishedEvents(): boolean {\r\n    return this._domainEvents.some(event => !event.isDispatched);\r\n  }\r\n\r\n  /**\r\n   * Get only unpublished events\r\n   */\r\n  public getUnpublishedEvents(): BaseDomainEvent[] {\r\n    return this._domainEvents.filter(event => !event.isDispatched);\r\n  }\r\n\r\n  /**\r\n   * Get events by type\r\n   * \r\n   * @param eventType The type of events to retrieve\r\n   */\r\n  public getEventsByType<TEvent extends BaseDomainEvent>(\r\n    eventType: new (...args: any[]) => TEvent\r\n  ): TEvent[] {\r\n    return this._domainEvents.filter(\r\n      event => event instanceof eventType\r\n    ) as TEvent[];\r\n  }\r\n\r\n  /**\r\n   * Check if aggregate has events of a specific type\r\n   * \r\n   * @param eventType The type of events to check for\r\n   */\r\n  public hasEventsOfType<TEvent extends BaseDomainEvent>(\r\n    eventType: new (...args: any[]) => TEvent\r\n  ): boolean {\r\n    return this._domainEvents.some(event => event instanceof eventType);\r\n  }\r\n\r\n  /**\r\n   * Get the aggregate version based on the number of events\r\n   * This can be used for optimistic concurrency control\r\n   */\r\n  public getVersion(): number {\r\n    return this._domainEvents.length;\r\n  }\r\n\r\n  /**\r\n   * Create a snapshot of the aggregate state\r\n   * Useful for event sourcing scenarios\r\n   */\r\n  public createSnapshot(): {\r\n    aggregateId: string;\r\n    version: number;\r\n    state: T;\r\n    timestamp: Date;\r\n  } {\r\n    return {\r\n      aggregateId: this._id.toString(),\r\n      version: this.getVersion(),\r\n      state: { ...this.props },\r\n      timestamp: new Date(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate aggregate invariants\r\n   * Override in concrete implementations to add specific business rules\r\n   */\r\n  protected validateInvariants(): void {\r\n    // Base implementation - override in concrete classes\r\n    if (!this._id) {\r\n      throw new Error('Aggregate must have a valid ID');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Apply business rules and validate state\r\n   * Called before any state changes\r\n   */\r\n  protected applyBusinessRules(): void {\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Convert aggregate to JSON representation\r\n   * Includes domain events for debugging purposes\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      domainEvents: this._domainEvents.map(event => ({\r\n        eventType: event.constructor.name,\r\n        eventId: event.eventId.toString(),\r\n        occurredOn: event.occurredOn,\r\n        isDispatched: event.isDispatched,\r\n      })),\r\n      version: this.getVersion(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a deep copy of the aggregate\r\n   * Useful for testing and state management\r\n   */\r\n  public clone(): this {\r\n    const clonedProps = JSON.parse(JSON.stringify(this.props));\r\n    const cloned = Object.create(Object.getPrototypeOf(this));\r\n    cloned.props = clonedProps;\r\n    cloned._id = this._id;\r\n    cloned._domainEvents = [...this._domainEvents];\r\n    return cloned;\r\n  }\r\n\r\n  /**\r\n   * Compare two aggregates for equality\r\n   * Aggregates are equal if they have the same ID and version\r\n   */\r\n  public equals(object?: BaseAggregateRoot<T>): boolean {\r\n    if (!super.equals(object)) {\r\n      return false;\r\n    }\r\n\r\n    return this.getVersion() === object.getVersion();\r\n  }\r\n\r\n  /**\r\n   * Get domain events (alias for domainEvents getter)\r\n   */\r\n  public getDomainEvents(): BaseDomainEvent[] {\r\n    return this.domainEvents;\r\n  }\r\n\r\n  /**\r\n   * Clear domain events (alias for clearEvents)\r\n   */\r\n  public clearDomainEvents(): void {\r\n    this.clearEvents();\r\n  }\r\n}\r\n"], "version": 3}