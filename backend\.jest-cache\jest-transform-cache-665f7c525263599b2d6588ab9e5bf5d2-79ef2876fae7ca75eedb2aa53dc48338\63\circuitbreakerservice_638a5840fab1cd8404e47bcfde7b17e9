eb5baafb1826c053c0c5129297719ef5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AICircuitBreakerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AICircuitBreakerService = void 0;
const common_1 = require("@nestjs/common");
const circuit_breaker_1 = require("@/shared-kernel/patterns/circuit-breaker");
/**
 * AI-specific circuit breaker service for managing AI provider resilience
 */
let AICircuitBreakerService = AICircuitBreakerService_1 = class AICircuitBreakerService {
    constructor() {
        this.logger = new common_1.Logger(AICircuitBreakerService_1.name);
        this.circuitBreakers = new Map();
        this.configs = new Map();
    }
    /**
     * Register a circuit breaker for an AI provider
     */
    registerProvider(providerId, providerType, config) {
        const options = {
            failureThreshold: config.failureThreshold,
            recoveryTimeout: config.recoveryTimeout,
            monitoringPeriod: config.monitoringPeriod,
            halfOpenMaxCalls: config.halfOpenMaxCalls,
            onStateChange: (state) => {
                this.logger.log(`Circuit breaker for ${providerType} provider ${providerId} changed to ${state}`);
            },
        };
        const circuitBreaker = new circuit_breaker_1.CircuitBreaker(options);
        this.circuitBreakers.set(providerId, circuitBreaker);
        this.configs.set(providerId, config);
        this.logger.log(`Registered circuit breaker for ${providerType} provider ${providerId}`);
    }
    /**
     * Execute an AI operation with circuit breaker protection
     */
    async executeWithProtection(providerId, operation, fallbackFn) {
        const circuitBreaker = this.circuitBreakers.get(providerId);
        if (!circuitBreaker) {
            throw new Error(`Circuit breaker not registered for provider: ${providerId}`);
        }
        const config = this.configs.get(providerId);
        try {
            return await circuitBreaker.execute(operation);
        }
        catch (error) {
            this.logger.error(`Circuit breaker execution failed for provider ${providerId}`, error);
            // Try fallback if enabled and available
            if (config?.enableFallback && fallbackFn) {
                this.logger.warn(`Executing fallback for provider ${providerId} due to circuit breaker`);
                return await fallbackFn();
            }
            // Return cached fallback response if available
            if (config?.enableFallback && config.fallbackResponse) {
                this.logger.warn(`Returning cached fallback response for provider ${providerId}`);
                return config.fallbackResponse;
            }
            throw error;
        }
    }
    /**
     * Get circuit breaker metrics for a specific provider
     */
    getProviderMetrics(providerId) {
        const circuitBreaker = this.circuitBreakers.get(providerId);
        if (!circuitBreaker) {
            return null;
        }
        const metrics = circuitBreaker.getMetrics();
        return {
            ...metrics,
            providerId,
            providerType: this.getProviderType(providerId),
        };
    }
    /**
     * Get metrics for all registered providers
     */
    getAllProviderMetrics() {
        const metrics = [];
        for (const [providerId, circuitBreaker] of this.circuitBreakers) {
            const providerMetrics = circuitBreaker.getMetrics();
            metrics.push({
                ...providerMetrics,
                providerId,
                providerType: this.getProviderType(providerId),
            });
        }
        return metrics;
    }
    /**
     * Check if a provider is available (circuit breaker is closed or half-open)
     */
    isProviderAvailable(providerId) {
        const circuitBreaker = this.circuitBreakers.get(providerId);
        if (!circuitBreaker) {
            return false;
        }
        const metrics = circuitBreaker.getMetrics();
        return metrics.state !== circuit_breaker_1.CircuitBreakerState.OPEN;
    }
    /**
     * Get all available providers
     */
    getAvailableProviders() {
        const availableProviders = [];
        for (const [providerId] of this.circuitBreakers) {
            if (this.isProviderAvailable(providerId)) {
                availableProviders.push(providerId);
            }
        }
        return availableProviders;
    }
    /**
     * Reset circuit breaker for a specific provider
     */
    resetProvider(providerId) {
        const circuitBreaker = this.circuitBreakers.get(providerId);
        if (circuitBreaker) {
            circuitBreaker.reset();
            this.logger.log(`Reset circuit breaker for provider ${providerId}`);
        }
    }
    /**
     * Force open circuit breaker for a specific provider
     */
    forceOpenProvider(providerId) {
        const circuitBreaker = this.circuitBreakers.get(providerId);
        if (circuitBreaker) {
            circuitBreaker.forceOpen();
            this.logger.warn(`Forced open circuit breaker for provider ${providerId}`);
        }
    }
    /**
     * Reset all circuit breakers
     */
    resetAllProviders() {
        for (const [providerId, circuitBreaker] of this.circuitBreakers) {
            circuitBreaker.reset();
            this.logger.log(`Reset circuit breaker for provider ${providerId}`);
        }
    }
    /**
     * Update configuration for a provider
     */
    updateProviderConfig(providerId, config) {
        const existingConfig = this.configs.get(providerId);
        if (existingConfig) {
            const updatedConfig = { ...existingConfig, ...config };
            this.configs.set(providerId, updatedConfig);
            // Re-register with new config
            const providerType = this.getProviderType(providerId);
            this.registerProvider(providerId, providerType, updatedConfig);
            this.logger.log(`Updated configuration for provider ${providerId}`);
        }
    }
    /**
     * Remove a provider's circuit breaker
     */
    unregisterProvider(providerId) {
        this.circuitBreakers.delete(providerId);
        this.configs.delete(providerId);
        this.logger.log(`Unregistered circuit breaker for provider ${providerId}`);
    }
    /**
     * Get health status of all providers
     */
    getHealthStatus() {
        const status = {};
        for (const [providerId] of this.circuitBreakers) {
            const available = this.isProviderAvailable(providerId);
            const metrics = this.getProviderMetrics(providerId);
            status[providerId] = {
                available,
                metrics,
            };
        }
        return status;
    }
    getProviderType(providerId) {
        // Extract provider type from provider ID (e.g., "openai-gpt4" -> "openai")
        return providerId.split('-')[0] || 'unknown';
    }
};
exports.AICircuitBreakerService = AICircuitBreakerService;
exports.AICircuitBreakerService = AICircuitBreakerService = AICircuitBreakerService_1 = __decorate([
    (0, common_1.Injectable)()
], AICircuitBreakerService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxhaVxcYXBwbGljYXRpb25cXHNlcnZpY2VzXFxyZXNpbGllbmNlXFxjaXJjdWl0LWJyZWFrZXIuc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUEsMkNBQW9EO0FBQ3BELDhFQUE2STtBQWtCN0k7O0dBRUc7QUFFSSxJQUFNLHVCQUF1QiwrQkFBN0IsTUFBTSx1QkFBdUI7SUFBN0I7UUFDWSxXQUFNLEdBQUcsSUFBSSxlQUFNLENBQUMseUJBQXVCLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDbEQsb0JBQWUsR0FBRyxJQUFJLEdBQUcsRUFBMEIsQ0FBQztRQUNwRCxZQUFPLEdBQUcsSUFBSSxHQUFHLEVBQWtDLENBQUM7SUEwTnZFLENBQUM7SUF4TkM7O09BRUc7SUFDSCxnQkFBZ0IsQ0FDZCxVQUFrQixFQUNsQixZQUFvQixFQUNwQixNQUE4QjtRQUU5QixNQUFNLE9BQU8sR0FBMEI7WUFDckMsZ0JBQWdCLEVBQUUsTUFBTSxDQUFDLGdCQUFnQjtZQUN6QyxlQUFlLEVBQUUsTUFBTSxDQUFDLGVBQWU7WUFDdkMsZ0JBQWdCLEVBQUUsTUFBTSxDQUFDLGdCQUFnQjtZQUN6QyxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsZ0JBQWdCO1lBQ3pDLGFBQWEsRUFBRSxDQUFDLEtBQTBCLEVBQUUsRUFBRTtnQkFDNUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQ2IsdUJBQXVCLFlBQVksYUFBYSxVQUFVLGVBQWUsS0FBSyxFQUFFLENBQ2pGLENBQUM7WUFDSixDQUFDO1NBQ0YsQ0FBQztRQUVGLE1BQU0sY0FBYyxHQUFHLElBQUksZ0NBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNuRCxJQUFJLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUUsY0FBYyxDQUFDLENBQUM7UUFDckQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBRXJDLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUNiLGtDQUFrQyxZQUFZLGFBQWEsVUFBVSxFQUFFLENBQ3hFLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMscUJBQXFCLENBQ3pCLFVBQWtCLEVBQ2xCLFNBQTJCLEVBQzNCLFVBQTZCO1FBRTdCLE1BQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQzVELElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQztZQUNwQixNQUFNLElBQUksS0FBSyxDQUFDLGdEQUFnRCxVQUFVLEVBQUUsQ0FBQyxDQUFDO1FBQ2hGLENBQUM7UUFFRCxNQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUU1QyxJQUFJLENBQUM7WUFDSCxPQUFPLE1BQU0sY0FBYyxDQUFDLE9BQU8sQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUNqRCxDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUNmLGlEQUFpRCxVQUFVLEVBQUUsRUFDN0QsS0FBSyxDQUNOLENBQUM7WUFFRix3Q0FBd0M7WUFDeEMsSUFBSSxNQUFNLEVBQUUsY0FBYyxJQUFJLFVBQVUsRUFBRSxDQUFDO2dCQUN6QyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FDZCxtQ0FBbUMsVUFBVSx5QkFBeUIsQ0FDdkUsQ0FBQztnQkFDRixPQUFPLE1BQU0sVUFBVSxFQUFFLENBQUM7WUFDNUIsQ0FBQztZQUVELCtDQUErQztZQUMvQyxJQUFJLE1BQU0sRUFBRSxjQUFjLElBQUksTUFBTSxDQUFDLGdCQUFnQixFQUFFLENBQUM7Z0JBQ3RELElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUNkLG1EQUFtRCxVQUFVLEVBQUUsQ0FDaEUsQ0FBQztnQkFDRixPQUFPLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQztZQUNqQyxDQUFDO1lBRUQsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0gsa0JBQWtCLENBQUMsVUFBa0I7UUFDbkMsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDNUQsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFDO1lBQ3BCLE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUVELE1BQU0sT0FBTyxHQUFHLGNBQWMsQ0FBQyxVQUFVLEVBQUUsQ0FBQztRQUM1QyxPQUFPO1lBQ0wsR0FBRyxPQUFPO1lBQ1YsVUFBVTtZQUNWLFlBQVksRUFBRSxJQUFJLENBQUMsZUFBZSxDQUFDLFVBQVUsQ0FBQztTQUMvQyxDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0gscUJBQXFCO1FBQ25CLE1BQU0sT0FBTyxHQUFzQyxFQUFFLENBQUM7UUFFdEQsS0FBSyxNQUFNLENBQUMsVUFBVSxFQUFFLGNBQWMsQ0FBQyxJQUFJLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztZQUNoRSxNQUFNLGVBQWUsR0FBRyxjQUFjLENBQUMsVUFBVSxFQUFFLENBQUM7WUFDcEQsT0FBTyxDQUFDLElBQUksQ0FBQztnQkFDWCxHQUFHLGVBQWU7Z0JBQ2xCLFVBQVU7Z0JBQ1YsWUFBWSxFQUFFLElBQUksQ0FBQyxlQUFlLENBQUMsVUFBVSxDQUFDO2FBQy9DLENBQUMsQ0FBQztRQUNMLENBQUM7UUFFRCxPQUFPLE9BQU8sQ0FBQztJQUNqQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxtQkFBbUIsQ0FBQyxVQUFrQjtRQUNwQyxNQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUM1RCxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUM7WUFDcEIsT0FBTyxLQUFLLENBQUM7UUFDZixDQUFDO1FBRUQsTUFBTSxPQUFPLEdBQUcsY0FBYyxDQUFDLFVBQVUsRUFBRSxDQUFDO1FBQzVDLE9BQU8sT0FBTyxDQUFDLEtBQUssS0FBSyxxQ0FBbUIsQ0FBQyxJQUFJLENBQUM7SUFDcEQsQ0FBQztJQUVEOztPQUVHO0lBQ0gscUJBQXFCO1FBQ25CLE1BQU0sa0JBQWtCLEdBQWEsRUFBRSxDQUFDO1FBRXhDLEtBQUssTUFBTSxDQUFDLFVBQVUsQ0FBQyxJQUFJLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztZQUNoRCxJQUFJLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDO2dCQUN6QyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7WUFDdEMsQ0FBQztRQUNILENBQUM7UUFFRCxPQUFPLGtCQUFrQixDQUFDO0lBQzVCLENBQUM7SUFFRDs7T0FFRztJQUNILGFBQWEsQ0FBQyxVQUFrQjtRQUM5QixNQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUM1RCxJQUFJLGNBQWMsRUFBRSxDQUFDO1lBQ25CLGNBQWMsQ0FBQyxLQUFLLEVBQUUsQ0FBQztZQUN2QixJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxzQ0FBc0MsVUFBVSxFQUFFLENBQUMsQ0FBQztRQUN0RSxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0gsaUJBQWlCLENBQUMsVUFBa0I7UUFDbEMsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDNUQsSUFBSSxjQUFjLEVBQUUsQ0FBQztZQUNuQixjQUFjLENBQUMsU0FBUyxFQUFFLENBQUM7WUFDM0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsNENBQTRDLFVBQVUsRUFBRSxDQUFDLENBQUM7UUFDN0UsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILGlCQUFpQjtRQUNmLEtBQUssTUFBTSxDQUFDLFVBQVUsRUFBRSxjQUFjLENBQUMsSUFBSSxJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7WUFDaEUsY0FBYyxDQUFDLEtBQUssRUFBRSxDQUFDO1lBQ3ZCLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLHNDQUFzQyxVQUFVLEVBQUUsQ0FBQyxDQUFDO1FBQ3RFLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxvQkFBb0IsQ0FBQyxVQUFrQixFQUFFLE1BQXVDO1FBQzlFLE1BQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3BELElBQUksY0FBYyxFQUFFLENBQUM7WUFDbkIsTUFBTSxhQUFhLEdBQUcsRUFBRSxHQUFHLGNBQWMsRUFBRSxHQUFHLE1BQU0sRUFBRSxDQUFDO1lBQ3ZELElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsRUFBRSxhQUFhLENBQUMsQ0FBQztZQUU1Qyw4QkFBOEI7WUFDOUIsTUFBTSxZQUFZLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUN0RCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsVUFBVSxFQUFFLFlBQVksRUFBRSxhQUFhLENBQUMsQ0FBQztZQUUvRCxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxzQ0FBc0MsVUFBVSxFQUFFLENBQUMsQ0FBQztRQUN0RSxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0gsa0JBQWtCLENBQUMsVUFBa0I7UUFDbkMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDeEMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDaEMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsNkNBQTZDLFVBQVUsRUFBRSxDQUFDLENBQUM7SUFDN0UsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZUFBZTtRQUNiLE1BQU0sTUFBTSxHQUFxRixFQUFFLENBQUM7UUFFcEcsS0FBSyxNQUFNLENBQUMsVUFBVSxDQUFDLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO1lBQ2hELE1BQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUN2RCxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsVUFBVSxDQUFFLENBQUM7WUFFckQsTUFBTSxDQUFDLFVBQVUsQ0FBQyxHQUFHO2dCQUNuQixTQUFTO2dCQUNULE9BQU87YUFDUixDQUFDO1FBQ0osQ0FBQztRQUVELE9BQU8sTUFBTSxDQUFDO0lBQ2hCLENBQUM7SUFFTyxlQUFlLENBQUMsVUFBa0I7UUFDeEMsMkVBQTJFO1FBQzNFLE9BQU8sVUFBVSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxTQUFTLENBQUM7SUFDL0MsQ0FBQztDQUNGLENBQUE7QUE3TlksMERBQXVCO2tDQUF2Qix1QkFBdUI7SUFEbkMsSUFBQSxtQkFBVSxHQUFFO0dBQ0EsdUJBQXVCLENBNk5uQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXG1vZHVsZXNcXGFpXFxhcHBsaWNhdGlvblxcc2VydmljZXNcXHJlc2lsaWVuY2VcXGNpcmN1aXQtYnJlYWtlci5zZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluamVjdGFibGUsIExvZ2dlciB9IGZyb20gJ0BuZXN0anMvY29tbW9uJztcclxuaW1wb3J0IHsgQ2lyY3VpdEJyZWFrZXIsIENpcmN1aXRCcmVha2VyT3B0aW9ucywgQ2lyY3VpdEJyZWFrZXJTdGF0ZSwgQ2lyY3VpdEJyZWFrZXJNZXRyaWNzIH0gZnJvbSAnQC9zaGFyZWQta2VybmVsL3BhdHRlcm5zL2NpcmN1aXQtYnJlYWtlcic7XHJcbmltcG9ydCB7IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbiB9IGZyb20gJ0Avc2hhcmVkLWtlcm5lbC9leGNlcHRpb25zL3NlcnZpY2UtdW5hdmFpbGFibGUuZXhjZXB0aW9uJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQUlDaXJjdWl0QnJlYWtlckNvbmZpZyB7XHJcbiAgZmFpbHVyZVRocmVzaG9sZDogbnVtYmVyO1xyXG4gIHJlY292ZXJ5VGltZW91dDogbnVtYmVyO1xyXG4gIG1vbml0b3JpbmdQZXJpb2Q6IG51bWJlcjtcclxuICBoYWxmT3Blbk1heENhbGxzOiBudW1iZXI7XHJcbiAgZW5hYmxlRmFsbGJhY2s6IGJvb2xlYW47XHJcbiAgZmFsbGJhY2tSZXNwb25zZT86IGFueTtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBSVByb3ZpZGVyQ2lyY3VpdEJyZWFrZXJNZXRyaWNzIGV4dGVuZHMgQ2lyY3VpdEJyZWFrZXJNZXRyaWNzIHtcclxuICBwcm92aWRlcklkOiBzdHJpbmc7XHJcbiAgcHJvdmlkZXJUeXBlOiBzdHJpbmc7XHJcbiAgbGFzdEVycm9yPzogc3RyaW5nO1xyXG59XHJcblxyXG4vKipcclxuICogQUktc3BlY2lmaWMgY2lyY3VpdCBicmVha2VyIHNlcnZpY2UgZm9yIG1hbmFnaW5nIEFJIHByb3ZpZGVyIHJlc2lsaWVuY2VcclxuICovXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIEFJQ2lyY3VpdEJyZWFrZXJTZXJ2aWNlIHtcclxuICBwcml2YXRlIHJlYWRvbmx5IGxvZ2dlciA9IG5ldyBMb2dnZXIoQUlDaXJjdWl0QnJlYWtlclNlcnZpY2UubmFtZSk7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBjaXJjdWl0QnJlYWtlcnMgPSBuZXcgTWFwPHN0cmluZywgQ2lyY3VpdEJyZWFrZXI+KCk7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBjb25maWdzID0gbmV3IE1hcDxzdHJpbmcsIEFJQ2lyY3VpdEJyZWFrZXJDb25maWc+KCk7XHJcblxyXG4gIC8qKlxyXG4gICAqIFJlZ2lzdGVyIGEgY2lyY3VpdCBicmVha2VyIGZvciBhbiBBSSBwcm92aWRlclxyXG4gICAqL1xyXG4gIHJlZ2lzdGVyUHJvdmlkZXIoXHJcbiAgICBwcm92aWRlcklkOiBzdHJpbmcsXHJcbiAgICBwcm92aWRlclR5cGU6IHN0cmluZyxcclxuICAgIGNvbmZpZzogQUlDaXJjdWl0QnJlYWtlckNvbmZpZ1xyXG4gICk6IHZvaWQge1xyXG4gICAgY29uc3Qgb3B0aW9uczogQ2lyY3VpdEJyZWFrZXJPcHRpb25zID0ge1xyXG4gICAgICBmYWlsdXJlVGhyZXNob2xkOiBjb25maWcuZmFpbHVyZVRocmVzaG9sZCxcclxuICAgICAgcmVjb3ZlcnlUaW1lb3V0OiBjb25maWcucmVjb3ZlcnlUaW1lb3V0LFxyXG4gICAgICBtb25pdG9yaW5nUGVyaW9kOiBjb25maWcubW9uaXRvcmluZ1BlcmlvZCxcclxuICAgICAgaGFsZk9wZW5NYXhDYWxsczogY29uZmlnLmhhbGZPcGVuTWF4Q2FsbHMsXHJcbiAgICAgIG9uU3RhdGVDaGFuZ2U6IChzdGF0ZTogQ2lyY3VpdEJyZWFrZXJTdGF0ZSkgPT4ge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLmxvZyhcclxuICAgICAgICAgIGBDaXJjdWl0IGJyZWFrZXIgZm9yICR7cHJvdmlkZXJUeXBlfSBwcm92aWRlciAke3Byb3ZpZGVySWR9IGNoYW5nZWQgdG8gJHtzdGF0ZX1gXHJcbiAgICAgICAgKTtcclxuICAgICAgfSxcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgY2lyY3VpdEJyZWFrZXIgPSBuZXcgQ2lyY3VpdEJyZWFrZXIob3B0aW9ucyk7XHJcbiAgICB0aGlzLmNpcmN1aXRCcmVha2Vycy5zZXQocHJvdmlkZXJJZCwgY2lyY3VpdEJyZWFrZXIpO1xyXG4gICAgdGhpcy5jb25maWdzLnNldChwcm92aWRlcklkLCBjb25maWcpO1xyXG5cclxuICAgIHRoaXMubG9nZ2VyLmxvZyhcclxuICAgICAgYFJlZ2lzdGVyZWQgY2lyY3VpdCBicmVha2VyIGZvciAke3Byb3ZpZGVyVHlwZX0gcHJvdmlkZXIgJHtwcm92aWRlcklkfWBcclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBFeGVjdXRlIGFuIEFJIG9wZXJhdGlvbiB3aXRoIGNpcmN1aXQgYnJlYWtlciBwcm90ZWN0aW9uXHJcbiAgICovXHJcbiAgYXN5bmMgZXhlY3V0ZVdpdGhQcm90ZWN0aW9uPFQ+KFxyXG4gICAgcHJvdmlkZXJJZDogc3RyaW5nLFxyXG4gICAgb3BlcmF0aW9uOiAoKSA9PiBQcm9taXNlPFQ+LFxyXG4gICAgZmFsbGJhY2tGbj86ICgpID0+IFByb21pc2U8VD5cclxuICApOiBQcm9taXNlPFQ+IHtcclxuICAgIGNvbnN0IGNpcmN1aXRCcmVha2VyID0gdGhpcy5jaXJjdWl0QnJlYWtlcnMuZ2V0KHByb3ZpZGVySWQpO1xyXG4gICAgaWYgKCFjaXJjdWl0QnJlYWtlcikge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYENpcmN1aXQgYnJlYWtlciBub3QgcmVnaXN0ZXJlZCBmb3IgcHJvdmlkZXI6ICR7cHJvdmlkZXJJZH1gKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBjb25maWcgPSB0aGlzLmNvbmZpZ3MuZ2V0KHByb3ZpZGVySWQpO1xyXG4gICAgXHJcbiAgICB0cnkge1xyXG4gICAgICByZXR1cm4gYXdhaXQgY2lyY3VpdEJyZWFrZXIuZXhlY3V0ZShvcGVyYXRpb24pO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoXHJcbiAgICAgICAgYENpcmN1aXQgYnJlYWtlciBleGVjdXRpb24gZmFpbGVkIGZvciBwcm92aWRlciAke3Byb3ZpZGVySWR9YCxcclxuICAgICAgICBlcnJvclxyXG4gICAgICApO1xyXG5cclxuICAgICAgLy8gVHJ5IGZhbGxiYWNrIGlmIGVuYWJsZWQgYW5kIGF2YWlsYWJsZVxyXG4gICAgICBpZiAoY29uZmlnPy5lbmFibGVGYWxsYmFjayAmJiBmYWxsYmFja0ZuKSB7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIud2FybihcclxuICAgICAgICAgIGBFeGVjdXRpbmcgZmFsbGJhY2sgZm9yIHByb3ZpZGVyICR7cHJvdmlkZXJJZH0gZHVlIHRvIGNpcmN1aXQgYnJlYWtlcmBcclxuICAgICAgICApO1xyXG4gICAgICAgIHJldHVybiBhd2FpdCBmYWxsYmFja0ZuKCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFJldHVybiBjYWNoZWQgZmFsbGJhY2sgcmVzcG9uc2UgaWYgYXZhaWxhYmxlXHJcbiAgICAgIGlmIChjb25maWc/LmVuYWJsZUZhbGxiYWNrICYmIGNvbmZpZy5mYWxsYmFja1Jlc3BvbnNlKSB7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIud2FybihcclxuICAgICAgICAgIGBSZXR1cm5pbmcgY2FjaGVkIGZhbGxiYWNrIHJlc3BvbnNlIGZvciBwcm92aWRlciAke3Byb3ZpZGVySWR9YFxyXG4gICAgICAgICk7XHJcbiAgICAgICAgcmV0dXJuIGNvbmZpZy5mYWxsYmFja1Jlc3BvbnNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBjaXJjdWl0IGJyZWFrZXIgbWV0cmljcyBmb3IgYSBzcGVjaWZpYyBwcm92aWRlclxyXG4gICAqL1xyXG4gIGdldFByb3ZpZGVyTWV0cmljcyhwcm92aWRlcklkOiBzdHJpbmcpOiBBSVByb3ZpZGVyQ2lyY3VpdEJyZWFrZXJNZXRyaWNzIHwgbnVsbCB7XHJcbiAgICBjb25zdCBjaXJjdWl0QnJlYWtlciA9IHRoaXMuY2lyY3VpdEJyZWFrZXJzLmdldChwcm92aWRlcklkKTtcclxuICAgIGlmICghY2lyY3VpdEJyZWFrZXIpIHtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgbWV0cmljcyA9IGNpcmN1aXRCcmVha2VyLmdldE1ldHJpY3MoKTtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIC4uLm1ldHJpY3MsXHJcbiAgICAgIHByb3ZpZGVySWQsXHJcbiAgICAgIHByb3ZpZGVyVHlwZTogdGhpcy5nZXRQcm92aWRlclR5cGUocHJvdmlkZXJJZCksXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IG1ldHJpY3MgZm9yIGFsbCByZWdpc3RlcmVkIHByb3ZpZGVyc1xyXG4gICAqL1xyXG4gIGdldEFsbFByb3ZpZGVyTWV0cmljcygpOiBBSVByb3ZpZGVyQ2lyY3VpdEJyZWFrZXJNZXRyaWNzW10ge1xyXG4gICAgY29uc3QgbWV0cmljczogQUlQcm92aWRlckNpcmN1aXRCcmVha2VyTWV0cmljc1tdID0gW107XHJcbiAgICBcclxuICAgIGZvciAoY29uc3QgW3Byb3ZpZGVySWQsIGNpcmN1aXRCcmVha2VyXSBvZiB0aGlzLmNpcmN1aXRCcmVha2Vycykge1xyXG4gICAgICBjb25zdCBwcm92aWRlck1ldHJpY3MgPSBjaXJjdWl0QnJlYWtlci5nZXRNZXRyaWNzKCk7XHJcbiAgICAgIG1ldHJpY3MucHVzaCh7XHJcbiAgICAgICAgLi4ucHJvdmlkZXJNZXRyaWNzLFxyXG4gICAgICAgIHByb3ZpZGVySWQsXHJcbiAgICAgICAgcHJvdmlkZXJUeXBlOiB0aGlzLmdldFByb3ZpZGVyVHlwZShwcm92aWRlcklkKSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIG1ldHJpY3M7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBhIHByb3ZpZGVyIGlzIGF2YWlsYWJsZSAoY2lyY3VpdCBicmVha2VyIGlzIGNsb3NlZCBvciBoYWxmLW9wZW4pXHJcbiAgICovXHJcbiAgaXNQcm92aWRlckF2YWlsYWJsZShwcm92aWRlcklkOiBzdHJpbmcpOiBib29sZWFuIHtcclxuICAgIGNvbnN0IGNpcmN1aXRCcmVha2VyID0gdGhpcy5jaXJjdWl0QnJlYWtlcnMuZ2V0KHByb3ZpZGVySWQpO1xyXG4gICAgaWYgKCFjaXJjdWl0QnJlYWtlcikge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgbWV0cmljcyA9IGNpcmN1aXRCcmVha2VyLmdldE1ldHJpY3MoKTtcclxuICAgIHJldHVybiBtZXRyaWNzLnN0YXRlICE9PSBDaXJjdWl0QnJlYWtlclN0YXRlLk9QRU47XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgYWxsIGF2YWlsYWJsZSBwcm92aWRlcnNcclxuICAgKi9cclxuICBnZXRBdmFpbGFibGVQcm92aWRlcnMoKTogc3RyaW5nW10ge1xyXG4gICAgY29uc3QgYXZhaWxhYmxlUHJvdmlkZXJzOiBzdHJpbmdbXSA9IFtdO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IFtwcm92aWRlcklkXSBvZiB0aGlzLmNpcmN1aXRCcmVha2Vycykge1xyXG4gICAgICBpZiAodGhpcy5pc1Byb3ZpZGVyQXZhaWxhYmxlKHByb3ZpZGVySWQpKSB7XHJcbiAgICAgICAgYXZhaWxhYmxlUHJvdmlkZXJzLnB1c2gocHJvdmlkZXJJZCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYXZhaWxhYmxlUHJvdmlkZXJzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogUmVzZXQgY2lyY3VpdCBicmVha2VyIGZvciBhIHNwZWNpZmljIHByb3ZpZGVyXHJcbiAgICovXHJcbiAgcmVzZXRQcm92aWRlcihwcm92aWRlcklkOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIGNvbnN0IGNpcmN1aXRCcmVha2VyID0gdGhpcy5jaXJjdWl0QnJlYWtlcnMuZ2V0KHByb3ZpZGVySWQpO1xyXG4gICAgaWYgKGNpcmN1aXRCcmVha2VyKSB7XHJcbiAgICAgIGNpcmN1aXRCcmVha2VyLnJlc2V0KCk7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmxvZyhgUmVzZXQgY2lyY3VpdCBicmVha2VyIGZvciBwcm92aWRlciAke3Byb3ZpZGVySWR9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBGb3JjZSBvcGVuIGNpcmN1aXQgYnJlYWtlciBmb3IgYSBzcGVjaWZpYyBwcm92aWRlclxyXG4gICAqL1xyXG4gIGZvcmNlT3BlblByb3ZpZGVyKHByb3ZpZGVySWQ6IHN0cmluZyk6IHZvaWQge1xyXG4gICAgY29uc3QgY2lyY3VpdEJyZWFrZXIgPSB0aGlzLmNpcmN1aXRCcmVha2Vycy5nZXQocHJvdmlkZXJJZCk7XHJcbiAgICBpZiAoY2lyY3VpdEJyZWFrZXIpIHtcclxuICAgICAgY2lyY3VpdEJyZWFrZXIuZm9yY2VPcGVuKCk7XHJcbiAgICAgIHRoaXMubG9nZ2VyLndhcm4oYEZvcmNlZCBvcGVuIGNpcmN1aXQgYnJlYWtlciBmb3IgcHJvdmlkZXIgJHtwcm92aWRlcklkfWApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogUmVzZXQgYWxsIGNpcmN1aXQgYnJlYWtlcnNcclxuICAgKi9cclxuICByZXNldEFsbFByb3ZpZGVycygpOiB2b2lkIHtcclxuICAgIGZvciAoY29uc3QgW3Byb3ZpZGVySWQsIGNpcmN1aXRCcmVha2VyXSBvZiB0aGlzLmNpcmN1aXRCcmVha2Vycykge1xyXG4gICAgICBjaXJjdWl0QnJlYWtlci5yZXNldCgpO1xyXG4gICAgICB0aGlzLmxvZ2dlci5sb2coYFJlc2V0IGNpcmN1aXQgYnJlYWtlciBmb3IgcHJvdmlkZXIgJHtwcm92aWRlcklkfWApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogVXBkYXRlIGNvbmZpZ3VyYXRpb24gZm9yIGEgcHJvdmlkZXJcclxuICAgKi9cclxuICB1cGRhdGVQcm92aWRlckNvbmZpZyhwcm92aWRlcklkOiBzdHJpbmcsIGNvbmZpZzogUGFydGlhbDxBSUNpcmN1aXRCcmVha2VyQ29uZmlnPik6IHZvaWQge1xyXG4gICAgY29uc3QgZXhpc3RpbmdDb25maWcgPSB0aGlzLmNvbmZpZ3MuZ2V0KHByb3ZpZGVySWQpO1xyXG4gICAgaWYgKGV4aXN0aW5nQ29uZmlnKSB7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRDb25maWcgPSB7IC4uLmV4aXN0aW5nQ29uZmlnLCAuLi5jb25maWcgfTtcclxuICAgICAgdGhpcy5jb25maWdzLnNldChwcm92aWRlcklkLCB1cGRhdGVkQ29uZmlnKTtcclxuICAgICAgXHJcbiAgICAgIC8vIFJlLXJlZ2lzdGVyIHdpdGggbmV3IGNvbmZpZ1xyXG4gICAgICBjb25zdCBwcm92aWRlclR5cGUgPSB0aGlzLmdldFByb3ZpZGVyVHlwZShwcm92aWRlcklkKTtcclxuICAgICAgdGhpcy5yZWdpc3RlclByb3ZpZGVyKHByb3ZpZGVySWQsIHByb3ZpZGVyVHlwZSwgdXBkYXRlZENvbmZpZyk7XHJcbiAgICAgIFxyXG4gICAgICB0aGlzLmxvZ2dlci5sb2coYFVwZGF0ZWQgY29uZmlndXJhdGlvbiBmb3IgcHJvdmlkZXIgJHtwcm92aWRlcklkfWApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogUmVtb3ZlIGEgcHJvdmlkZXIncyBjaXJjdWl0IGJyZWFrZXJcclxuICAgKi9cclxuICB1bnJlZ2lzdGVyUHJvdmlkZXIocHJvdmlkZXJJZDogc3RyaW5nKTogdm9pZCB7XHJcbiAgICB0aGlzLmNpcmN1aXRCcmVha2Vycy5kZWxldGUocHJvdmlkZXJJZCk7XHJcbiAgICB0aGlzLmNvbmZpZ3MuZGVsZXRlKHByb3ZpZGVySWQpO1xyXG4gICAgdGhpcy5sb2dnZXIubG9nKGBVbnJlZ2lzdGVyZWQgY2lyY3VpdCBicmVha2VyIGZvciBwcm92aWRlciAke3Byb3ZpZGVySWR9YCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgaGVhbHRoIHN0YXR1cyBvZiBhbGwgcHJvdmlkZXJzXHJcbiAgICovXHJcbiAgZ2V0SGVhbHRoU3RhdHVzKCk6IFJlY29yZDxzdHJpbmcsIHsgYXZhaWxhYmxlOiBib29sZWFuOyBtZXRyaWNzOiBBSVByb3ZpZGVyQ2lyY3VpdEJyZWFrZXJNZXRyaWNzIH0+IHtcclxuICAgIGNvbnN0IHN0YXR1czogUmVjb3JkPHN0cmluZywgeyBhdmFpbGFibGU6IGJvb2xlYW47IG1ldHJpY3M6IEFJUHJvdmlkZXJDaXJjdWl0QnJlYWtlck1ldHJpY3MgfT4gPSB7fTtcclxuICAgIFxyXG4gICAgZm9yIChjb25zdCBbcHJvdmlkZXJJZF0gb2YgdGhpcy5jaXJjdWl0QnJlYWtlcnMpIHtcclxuICAgICAgY29uc3QgYXZhaWxhYmxlID0gdGhpcy5pc1Byb3ZpZGVyQXZhaWxhYmxlKHByb3ZpZGVySWQpO1xyXG4gICAgICBjb25zdCBtZXRyaWNzID0gdGhpcy5nZXRQcm92aWRlck1ldHJpY3MocHJvdmlkZXJJZCkhO1xyXG4gICAgICBcclxuICAgICAgc3RhdHVzW3Byb3ZpZGVySWRdID0ge1xyXG4gICAgICAgIGF2YWlsYWJsZSxcclxuICAgICAgICBtZXRyaWNzLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBzdGF0dXM7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGdldFByb3ZpZGVyVHlwZShwcm92aWRlcklkOiBzdHJpbmcpOiBzdHJpbmcge1xyXG4gICAgLy8gRXh0cmFjdCBwcm92aWRlciB0eXBlIGZyb20gcHJvdmlkZXIgSUQgKGUuZy4sIFwib3BlbmFpLWdwdDRcIiAtPiBcIm9wZW5haVwiKVxyXG4gICAgcmV0dXJuIHByb3ZpZGVySWQuc3BsaXQoJy0nKVswXSB8fCAndW5rbm93bic7XHJcbiAgfVxyXG59Il0sInZlcnNpb24iOjN9