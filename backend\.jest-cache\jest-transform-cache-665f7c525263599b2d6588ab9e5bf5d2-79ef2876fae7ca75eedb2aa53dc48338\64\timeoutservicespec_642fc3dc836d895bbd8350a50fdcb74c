550c84d07096e3c39dc0628454a13b17
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const timeout_service_1 = require("../timeout.service");
describe('AITimeoutService', () => {
    let service;
    const defaultConfig = {
        defaultTimeout: 1000, // Use smaller timeouts for faster tests
        operationTimeouts: {
            'quick-operation': 500,
            'slow-operation': 2000,
        },
        escalationTimeouts: [1000, 1500, 2000],
        enableEscalation: true,
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [timeout_service_1.AITimeoutService],
        }).compile();
        service = module.get(timeout_service_1.AITimeoutService);
    });
    afterEach(() => {
        // Clean up all registered providers and cancel operations
        service.cancelAllOperations();
        service.resetAllMetrics();
    });
    describe('registerProvider', () => {
        it('should register a new provider with timeout configuration', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const config = service.getProviderConfig(providerId);
            expect(config).toEqual(defaultConfig);
        });
        it('should initialize metrics for registered provider', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const metrics = service.getProviderMetrics(providerId);
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe(providerId);
            expect(metrics.totalOperations).toBe(0);
            expect(metrics.timeoutCount).toBe(0);
        });
    });
    describe('executeWithTimeout', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should execute successful operation within timeout', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            const result = await service.executeWithTimeout('test-provider', operation);
            expect(result).toBe('success');
            expect(operation).toHaveBeenCalledTimes(1);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.timeoutCount).toBe(0);
        });
        it('should timeout operation that exceeds timeout limit', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1500));
                return 'success';
            });
            await expect(service.executeWithTimeout('test-provider', operation)).rejects.toThrow('Operation timed out after 1000ms');
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.timeoutCount).toBe(1);
            expect(metrics.timeoutRate).toBe(1);
        });
        it('should use operation-specific timeout', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 700));
                return 'success';
            });
            // Should timeout with quick-operation timeout (500ms)
            await expect(service.executeWithTimeout('test-provider', operation, 'quick-operation')).rejects.toThrow('Operation timed out after 500ms');
        });
        it('should use custom timeout when provided', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 400));
                return 'success';
            });
            const result = await service.executeWithTimeout('test-provider', operation, undefined, 300);
            // Should timeout with custom timeout (300ms)
            await expect(service.executeWithTimeout('test-provider', operation, undefined, 300)).rejects.toThrow('Operation timed out after 300ms');
        });
        it('should throw error for unregistered provider', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await expect(service.executeWithTimeout('unknown-provider', operation)).rejects.toThrow('Timeout configuration not registered for provider: unknown-provider');
        });
        it('should handle operation errors properly', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('operation failed'));
            await expect(service.executeWithTimeout('test-provider', operation)).rejects.toThrow('operation failed');
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.timeoutCount).toBe(0); // Not a timeout error
        });
        it('should support abort signal in operation', async () => {
            const operation = jest.fn().mockImplementation(async (signal) => {
                return new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => resolve('success'), 500);
                    if (signal) {
                        signal.addEventListener('abort', () => {
                            clearTimeout(timeout);
                            reject(new Error('Operation aborted'));
                        });
                    }
                });
            });
            // Start operation and cancel it
            const promise = service.executeWithTimeout('test-provider', operation);
            // Cancel after a short delay
            setTimeout(() => {
                service.cancelProviderOperations('test-provider');
            }, 100);
            await expect(promise).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('executeWithTimeoutDetails', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return detailed timeout information for successful operation', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            const result = await service.executeWithTimeoutDetails('test-provider', operation);
            expect(result.result).toBe('success');
            expect(result.timedOut).toBe(false);
            expect(result.executionTime).toBeGreaterThan(90);
            expect(result.executionTime).toBeLessThan(200);
        });
        it('should return detailed timeout information for timed out operation', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1500));
                return 'success';
            });
            const result = await service.executeWithTimeoutDetails('test-provider', operation);
            expect(result.timedOut).toBe(true);
            expect(result.executionTime).toBeGreaterThan(900);
        });
    });
    describe('executeWithEscalation', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should use escalating timeouts for different attempts', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1200));
                return 'success';
            });
            // First attempt should timeout with 1000ms
            await expect(service.executeWithEscalation('test-provider', operation, 1)).rejects.toThrow('Operation timed out after 1000ms');
            // Second attempt should timeout with 1500ms (should succeed)
            const operation2 = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1200));
                return 'success';
            });
            const result = await service.executeWithEscalation('test-provider', operation2, 2);
            expect(result).toBe('success');
        });
        it('should handle escalation when disabled', async () => {
            const configWithoutEscalation = {
                ...defaultConfig,
                enableEscalation: false,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithoutEscalation);
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            const result = await service.executeWithEscalation('test-provider', operation, 3);
            expect(result).toBe('success');
        });
    });
    describe('cancelProviderOperations', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should cancel active operations for a provider', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 2000));
                return 'success';
            });
            // Start multiple operations
            const promise1 = service.executeWithTimeout('test-provider', operation);
            const promise2 = service.executeWithTimeout('test-provider', operation);
            // Wait a bit to ensure operations are active
            await new Promise(resolve => setTimeout(resolve, 50));
            expect(service.getActiveOperationCount('test-provider')).toBe(2);
            const cancelledCount = service.cancelProviderOperations('test-provider');
            expect(cancelledCount).toBe(2);
            await expect(promise1).rejects.toThrow('Operation was cancelled');
            await expect(promise2).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('cancelAllOperations', () => {
        beforeEach(() => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
        });
        it('should cancel all active operations', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 2000));
                return 'success';
            });
            // Start operations for different providers
            const promise1 = service.executeWithTimeout('provider1', operation);
            const promise2 = service.executeWithTimeout('provider2', operation);
            // Wait a bit to ensure operations are active
            await new Promise(resolve => setTimeout(resolve, 50));
            expect(service.getTotalActiveOperationCount()).toBe(2);
            const cancelledCount = service.cancelAllOperations();
            expect(cancelledCount).toBe(2);
            await expect(promise1).rejects.toThrow('Operation was cancelled');
            await expect(promise2).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after operations', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            await service.executeWithTimeout('test-provider', operation);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.averageExecutionTime).toBeGreaterThan(90);
            expect(metrics.maxExecutionTime).toBeGreaterThan(90);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('resetProviderMetrics', () => {
        beforeEach(async () => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
            // Generate some metrics
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            await service.executeWithTimeout('test-provider', operation);
        });
        it('should reset metrics for specific provider', () => {
            // Verify metrics exist
            let metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            // Reset metrics
            service.resetProviderMetrics('test-provider');
            // Verify metrics are reset
            metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(0);
            expect(metrics.averageExecutionTime).toBe(0);
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { defaultTimeout: 5000 };
            service.updateProviderConfig('test-provider', newConfig);
            const config = service.getProviderConfig('test-provider');
            expect(config.defaultTimeout).toBe(5000);
            expect(config.enableEscalation).toBe(true); // Should keep existing values
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider configuration and metrics', () => {
            expect(service.getProviderConfig('test-provider')).toBeDefined();
            expect(service.getProviderMetrics('test-provider')).toBeDefined();
            service.unregisterProvider('test-provider');
            expect(service.getProviderConfig('test-provider')).toBeNull();
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
        it('should cancel active operations when unregistering', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 2000));
                return 'success';
            });
            const promise = service.executeWithTimeout('test-provider', operation);
            // Wait a bit to ensure operation is active
            await new Promise(resolve => setTimeout(resolve, 50));
            service.unregisterProvider('test-provider');
            await expect(promise).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('static factory methods', () => {
        it('should create default timeout config', () => {
            const config = timeout_service_1.AITimeoutService.createDefaultConfig(60000);
            expect(config.defaultTimeout).toBe(60000);
            expect(config.enableEscalation).toBe(true);
            expect(config.escalationTimeouts).toHaveLength(3);
        });
        it('should create quick timeout config', () => {
            const config = timeout_service_1.AITimeoutService.createQuickConfig(3000);
            expect(config.defaultTimeout).toBe(3000);
            expect(config.operationTimeouts['quick-analysis']).toBe(2000);
            expect(config.enableEscalation).toBe(true);
        });
        it('should create long running timeout config', () => {
            const config = timeout_service_1.AITimeoutService.createLongRunningConfig(180000);
            expect(config.defaultTimeout).toBe(180000);
            expect(config.operationTimeouts['model-training']).toBe(300000);
            expect(config.escalationTimeouts).toHaveLength(4);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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