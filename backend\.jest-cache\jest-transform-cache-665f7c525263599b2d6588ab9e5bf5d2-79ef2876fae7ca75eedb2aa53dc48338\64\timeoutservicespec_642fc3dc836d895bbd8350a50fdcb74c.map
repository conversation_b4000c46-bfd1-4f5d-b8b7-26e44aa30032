{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\timeout.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,wDAAqE;AAErE,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,OAAyB,CAAC;IAE9B,MAAM,aAAa,GAAkB;QACnC,cAAc,EAAE,IAAI,EAAE,wCAAwC;QAC9D,iBAAiB,EAAE;YACjB,iBAAiB,EAAE,GAAG;YACtB,gBAAgB,EAAE,IAAI;SACvB;QACD,kBAAkB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACtC,gBAAgB,EAAE,IAAI;KACvB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE,CAAC,kCAAgB,CAAC;SAC9B,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAmB,kCAAgB,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,0DAA0D;QAC1D,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAC9B,OAAO,CAAC,eAAe,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,UAAU,GAAG,eAAe,CAAC;YACnC,MAAM,YAAY,GAAG,QAAQ,CAAC;YAE9B,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,UAAU,GAAG,eAAe,CAAC;YACnC,MAAM,YAAY,GAAG,QAAQ,CAAC;YAE9B,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE5E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,CACV,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CACvD,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;YAEtD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,OAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,sDAAsD;YACtD,MAAM,MAAM,CACV,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAC1E,CAAC,OAAO,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;YAE5F,6CAA6C;YAC7C,MAAM,MAAM,CACV,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CACvE,CAAC,OAAO,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,CACV,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,qEAAqE,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAE7E,MAAM,MAAM,CACV,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CACvD,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAEtC,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAoB,EAAE,EAAE;gBAC5E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC;oBAE1D,IAAI,MAAM,EAAE,CAAC;wBACX,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;4BACpC,YAAY,CAAC,OAAO,CAAC,CAAC;4BACtB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;wBACzC,CAAC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEvE,6BAA6B;YAC7B,UAAU,CAAC,GAAG,EAAE;gBACd,OAAO,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;YACpD,CAAC,EAAE,GAAG,CAAC,CAAC;YAER,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,KAAK,IAAI,EAAE;YACnF,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEnF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,KAAK,IAAI,EAAE;YAClF,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEnF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,2CAA2C;YAC3C,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,EAAE,CAAC,CAAC,CAC7D,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;YAEtD,6DAA6D;YAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACzD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;YACnF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,uBAAuB,GAAkB;gBAC7C,GAAG,aAAa;gBAChB,gBAAgB,EAAE,KAAK;aACxB,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,uBAAuB,CAAC,CAAC;YAE7E,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YAClF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAExE,6CAA6C;YAC7C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEjE,MAAM,cAAc,GAAG,OAAO,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;YACzE,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE/B,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;YAClE,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAEpE,6CAA6C;YAC7C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,MAAM,CAAC,OAAO,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEvD,MAAM,cAAc,GAAG,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACrD,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE/B,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;YAClE,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5D,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE7D,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAQ,CAAC,oBAAoB,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAQ,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAEnE,wBAAwB;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,MAAM,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,uBAAuB;YACvB,IAAI,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEzC,gBAAgB;YAChB,OAAO,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAE9C,2BAA2B;YAC3B,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YACtD,MAAM,CAAC,OAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAQ,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;YAE3C,OAAO,CAAC,oBAAoB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,8BAA8B;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAElE,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEvE,2CAA2C;YAC3C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,kCAAgB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAE3D,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,kCAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,MAAM,GAAG,kCAAgB,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\timeout.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { AITimeoutService, TimeoutConfig } from '../timeout.service';\r\n\r\ndescribe('AITimeoutService', () => {\r\n  let service: AITimeoutService;\r\n\r\n  const defaultConfig: TimeoutConfig = {\r\n    defaultTimeout: 1000, // Use smaller timeouts for faster tests\r\n    operationTimeouts: {\r\n      'quick-operation': 500,\r\n      'slow-operation': 2000,\r\n    },\r\n    escalationTimeouts: [1000, 1500, 2000],\r\n    enableEscalation: true,\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [AITimeoutService],\r\n    }).compile();\r\n\r\n    service = module.get<AITimeoutService>(AITimeoutService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    // Clean up all registered providers and cancel operations\r\n    service.cancelAllOperations();\r\n    service.resetAllMetrics();\r\n  });\r\n\r\n  describe('registerProvider', () => {\r\n    it('should register a new provider with timeout configuration', () => {\r\n      const providerId = 'test-provider';\r\n      const providerType = 'openai';\r\n\r\n      service.registerProvider(providerId, providerType, defaultConfig);\r\n\r\n      const config = service.getProviderConfig(providerId);\r\n      expect(config).toEqual(defaultConfig);\r\n    });\r\n\r\n    it('should initialize metrics for registered provider', () => {\r\n      const providerId = 'test-provider';\r\n      const providerType = 'openai';\r\n\r\n      service.registerProvider(providerId, providerType, defaultConfig);\r\n\r\n      const metrics = service.getProviderMetrics(providerId);\r\n      expect(metrics).toBeDefined();\r\n      expect(metrics!.providerId).toBe(providerId);\r\n      expect(metrics!.totalOperations).toBe(0);\r\n      expect(metrics!.timeoutCount).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('executeWithTimeout', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should execute successful operation within timeout', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        return 'success';\r\n      });\r\n\r\n      const result = await service.executeWithTimeout('test-provider', operation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n      \r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalOperations).toBe(1);\r\n      expect(metrics!.timeoutCount).toBe(0);\r\n    });\r\n\r\n    it('should timeout operation that exceeds timeout limit', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 1500));\r\n        return 'success';\r\n      });\r\n\r\n      await expect(\r\n        service.executeWithTimeout('test-provider', operation)\r\n      ).rejects.toThrow('Operation timed out after 1000ms');\r\n\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalOperations).toBe(1);\r\n      expect(metrics!.timeoutCount).toBe(1);\r\n      expect(metrics!.timeoutRate).toBe(1);\r\n    });\r\n\r\n    it('should use operation-specific timeout', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 700));\r\n        return 'success';\r\n      });\r\n\r\n      // Should timeout with quick-operation timeout (500ms)\r\n      await expect(\r\n        service.executeWithTimeout('test-provider', operation, 'quick-operation')\r\n      ).rejects.toThrow('Operation timed out after 500ms');\r\n    });\r\n\r\n    it('should use custom timeout when provided', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 400));\r\n        return 'success';\r\n      });\r\n\r\n      const result = await service.executeWithTimeout('test-provider', operation, undefined, 300);\r\n\r\n      // Should timeout with custom timeout (300ms)\r\n      await expect(\r\n        service.executeWithTimeout('test-provider', operation, undefined, 300)\r\n      ).rejects.toThrow('Operation timed out after 300ms');\r\n    });\r\n\r\n    it('should throw error for unregistered provider', async () => {\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      await expect(\r\n        service.executeWithTimeout('unknown-provider', operation)\r\n      ).rejects.toThrow('Timeout configuration not registered for provider: unknown-provider');\r\n    });\r\n\r\n    it('should handle operation errors properly', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('operation failed'));\r\n\r\n      await expect(\r\n        service.executeWithTimeout('test-provider', operation)\r\n      ).rejects.toThrow('operation failed');\r\n\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalOperations).toBe(1);\r\n      expect(metrics!.timeoutCount).toBe(0); // Not a timeout error\r\n    });\r\n\r\n    it('should support abort signal in operation', async () => {\r\n      const operation = jest.fn().mockImplementation(async (signal?: AbortSignal) => {\r\n        return new Promise((resolve, reject) => {\r\n          const timeout = setTimeout(() => resolve('success'), 500);\r\n          \r\n          if (signal) {\r\n            signal.addEventListener('abort', () => {\r\n              clearTimeout(timeout);\r\n              reject(new Error('Operation aborted'));\r\n            });\r\n          }\r\n        });\r\n      });\r\n\r\n      // Start operation and cancel it\r\n      const promise = service.executeWithTimeout('test-provider', operation);\r\n      \r\n      // Cancel after a short delay\r\n      setTimeout(() => {\r\n        service.cancelProviderOperations('test-provider');\r\n      }, 100);\r\n\r\n      await expect(promise).rejects.toThrow('Operation was cancelled');\r\n    });\r\n  });\r\n\r\n  describe('executeWithTimeoutDetails', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return detailed timeout information for successful operation', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        return 'success';\r\n      });\r\n\r\n      const result = await service.executeWithTimeoutDetails('test-provider', operation);\r\n\r\n      expect(result.result).toBe('success');\r\n      expect(result.timedOut).toBe(false);\r\n      expect(result.executionTime).toBeGreaterThan(90);\r\n      expect(result.executionTime).toBeLessThan(200);\r\n    });\r\n\r\n    it('should return detailed timeout information for timed out operation', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 1500));\r\n        return 'success';\r\n      });\r\n\r\n      const result = await service.executeWithTimeoutDetails('test-provider', operation);\r\n\r\n      expect(result.timedOut).toBe(true);\r\n      expect(result.executionTime).toBeGreaterThan(900);\r\n    });\r\n  });\r\n\r\n  describe('executeWithEscalation', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should use escalating timeouts for different attempts', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 1200));\r\n        return 'success';\r\n      });\r\n\r\n      // First attempt should timeout with 1000ms\r\n      await expect(\r\n        service.executeWithEscalation('test-provider', operation, 1)\r\n      ).rejects.toThrow('Operation timed out after 1000ms');\r\n\r\n      // Second attempt should timeout with 1500ms (should succeed)\r\n      const operation2 = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 1200));\r\n        return 'success';\r\n      });\r\n\r\n      const result = await service.executeWithEscalation('test-provider', operation2, 2);\r\n      expect(result).toBe('success');\r\n    });\r\n\r\n    it('should handle escalation when disabled', async () => {\r\n      const configWithoutEscalation: TimeoutConfig = {\r\n        ...defaultConfig,\r\n        enableEscalation: false,\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', configWithoutEscalation);\r\n\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        return 'success';\r\n      });\r\n\r\n      const result = await service.executeWithEscalation('test-provider', operation, 3);\r\n      expect(result).toBe('success');\r\n    });\r\n  });\r\n\r\n  describe('cancelProviderOperations', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should cancel active operations for a provider', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 2000));\r\n        return 'success';\r\n      });\r\n\r\n      // Start multiple operations\r\n      const promise1 = service.executeWithTimeout('test-provider', operation);\r\n      const promise2 = service.executeWithTimeout('test-provider', operation);\r\n\r\n      // Wait a bit to ensure operations are active\r\n      await new Promise(resolve => setTimeout(resolve, 50));\r\n\r\n      expect(service.getActiveOperationCount('test-provider')).toBe(2);\r\n\r\n      const cancelledCount = service.cancelProviderOperations('test-provider');\r\n      expect(cancelledCount).toBe(2);\r\n\r\n      await expect(promise1).rejects.toThrow('Operation was cancelled');\r\n      await expect(promise2).rejects.toThrow('Operation was cancelled');\r\n    });\r\n  });\r\n\r\n  describe('cancelAllOperations', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n    });\r\n\r\n    it('should cancel all active operations', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 2000));\r\n        return 'success';\r\n      });\r\n\r\n      // Start operations for different providers\r\n      const promise1 = service.executeWithTimeout('provider1', operation);\r\n      const promise2 = service.executeWithTimeout('provider2', operation);\r\n\r\n      // Wait a bit to ensure operations are active\r\n      await new Promise(resolve => setTimeout(resolve, 50));\r\n\r\n      expect(service.getTotalActiveOperationCount()).toBe(2);\r\n\r\n      const cancelledCount = service.cancelAllOperations();\r\n      expect(cancelledCount).toBe(2);\r\n\r\n      await expect(promise1).rejects.toThrow('Operation was cancelled');\r\n      await expect(promise2).rejects.toThrow('Operation was cancelled');\r\n    });\r\n  });\r\n\r\n  describe('getProviderMetrics', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return metrics for registered provider', () => {\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n\r\n      expect(metrics).toBeDefined();\r\n      expect(metrics!.providerId).toBe('test-provider');\r\n    });\r\n\r\n    it('should return null for unregistered provider', () => {\r\n      const metrics = service.getProviderMetrics('unknown-provider');\r\n      expect(metrics).toBeNull();\r\n    });\r\n\r\n    it('should update metrics after operations', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        return 'success';\r\n      });\r\n\r\n      await service.executeWithTimeout('test-provider', operation);\r\n\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalOperations).toBe(1);\r\n      expect(metrics!.averageExecutionTime).toBeGreaterThan(90);\r\n      expect(metrics!.maxExecutionTime).toBeGreaterThan(90);\r\n    });\r\n  });\r\n\r\n  describe('getAllProviderMetrics', () => {\r\n    it('should return empty array when no providers registered', () => {\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toEqual([]);\r\n    });\r\n\r\n    it('should return metrics for all registered providers', () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toHaveLength(2);\r\n      expect(metrics.map(m => m.providerId)).toContain('provider1');\r\n      expect(metrics.map(m => m.providerId)).toContain('provider2');\r\n    });\r\n  });\r\n\r\n  describe('resetProviderMetrics', () => {\r\n    beforeEach(async () => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n      \r\n      // Generate some metrics\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        return 'success';\r\n      });\r\n      await service.executeWithTimeout('test-provider', operation);\r\n    });\r\n\r\n    it('should reset metrics for specific provider', () => {\r\n      // Verify metrics exist\r\n      let metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalOperations).toBe(1);\r\n\r\n      // Reset metrics\r\n      service.resetProviderMetrics('test-provider');\r\n\r\n      // Verify metrics are reset\r\n      metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalOperations).toBe(0);\r\n      expect(metrics!.averageExecutionTime).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('updateProviderConfig', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should update provider configuration', () => {\r\n      const newConfig = { defaultTimeout: 5000 };\r\n      \r\n      service.updateProviderConfig('test-provider', newConfig);\r\n\r\n      const config = service.getProviderConfig('test-provider');\r\n      expect(config!.defaultTimeout).toBe(5000);\r\n      expect(config!.enableEscalation).toBe(true); // Should keep existing values\r\n    });\r\n  });\r\n\r\n  describe('unregisterProvider', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should remove provider configuration and metrics', () => {\r\n      expect(service.getProviderConfig('test-provider')).toBeDefined();\r\n      expect(service.getProviderMetrics('test-provider')).toBeDefined();\r\n\r\n      service.unregisterProvider('test-provider');\r\n\r\n      expect(service.getProviderConfig('test-provider')).toBeNull();\r\n      expect(service.getProviderMetrics('test-provider')).toBeNull();\r\n    });\r\n\r\n    it('should cancel active operations when unregistering', async () => {\r\n      const operation = jest.fn().mockImplementation(async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 2000));\r\n        return 'success';\r\n      });\r\n\r\n      const promise = service.executeWithTimeout('test-provider', operation);\r\n\r\n      // Wait a bit to ensure operation is active\r\n      await new Promise(resolve => setTimeout(resolve, 50));\r\n\r\n      service.unregisterProvider('test-provider');\r\n\r\n      await expect(promise).rejects.toThrow('Operation was cancelled');\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    it('should create default timeout config', () => {\r\n      const config = AITimeoutService.createDefaultConfig(60000);\r\n\r\n      expect(config.defaultTimeout).toBe(60000);\r\n      expect(config.enableEscalation).toBe(true);\r\n      expect(config.escalationTimeouts).toHaveLength(3);\r\n    });\r\n\r\n    it('should create quick timeout config', () => {\r\n      const config = AITimeoutService.createQuickConfig(3000);\r\n\r\n      expect(config.defaultTimeout).toBe(3000);\r\n      expect(config.operationTimeouts['quick-analysis']).toBe(2000);\r\n      expect(config.enableEscalation).toBe(true);\r\n    });\r\n\r\n    it('should create long running timeout config', () => {\r\n      const config = AITimeoutService.createLongRunningConfig(180000);\r\n\r\n      expect(config.defaultTimeout).toBe(180000);\r\n      expect(config.operationTimeouts['model-training']).toBe(300000);\r\n      expect(config.escalationTimeouts).toHaveLength(4);\r\n    });\r\n  });\r\n});"], "version": 3}