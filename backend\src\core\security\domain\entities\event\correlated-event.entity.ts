import { BaseAggregateRoot } from '../../../../../shared-kernel/domain/base-aggregate-root';
import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { EnrichedEvent } from './enriched-event.entity';
import { IOC } from '../../value-objects/threat-indicators/ioc.value-object';
import { ConfidenceLevel } from '../../enums/confidence-level.enum';
import { EventsCorrelatedEvent } from '../../events/events-correlated.event';

/**
 * Correlated Event Properties
 */
export interface CorrelatedEventProps {
  /** Reference to the primary enriched event */
  primaryEventId: UniqueEntityId;
  /** Related enriched event IDs */
  relatedEventIds: UniqueEntityId[];
  /** Correlation analysis results */
  correlationAnalysis: CorrelationAnalysis;
  /** Correlation metadata */
  correlationMetadata: CorrelationMetadata;
  /** Correlation confidence level */
  confidence: ConfidenceLevel;
  /** Correlation score (0-100) */
  score: number;
  /** Correlation errors if any */
  errors: CorrelationError[];
}

/**
 * Correlation Analysis
 */
export interface CorrelationAnalysis {
  /** Correlation type */
  type: CorrelationType;
  /** Correlation patterns found */
  patterns: CorrelationPattern[];
  /** Common indicators across events */
  commonIndicators: IOC[];
  /** Temporal correlation data */
  temporalCorrelation?: TemporalCorrelation;
  /** Spatial correlation data */
  spatialCorrelation?: SpatialCorrelation;
  /** Behavioral correlation data */
  behavioralCorrelation?: BehavioralCorrelation;
  /** Campaign correlation data */
  campaignCorrelation?: CampaignCorrelation;
  /** Attack chain analysis */
  attackChain?: AttackChainAnalysis;
}

/**
 * Correlation Type
 */
export enum CorrelationType {
  TEMPORAL = 'temporal',
  SPATIAL = 'spatial',
  BEHAVIORAL = 'behavioral',
  INDICATOR = 'indicator',
  CAMPAIGN = 'campaign',
  ATTACK_CHAIN = 'attack_chain',
  HYBRID = 'hybrid',
}

/**
 * Correlation Pattern
 */
export interface CorrelationPattern {
  /** Pattern type */
  type: string;
  /** Pattern description */
  description: string;
  /** Pattern confidence (0-100) */
  confidence: number;
  /** Events matching this pattern */
  matchingEvents: string[];
  /** Pattern attributes */
  attributes: Record<string, any>;
  /** Pattern strength (0-1) */
  strength: number;
}

/**
 * Temporal Correlation
 */
export interface TemporalCorrelation {
  /** Time window in milliseconds */
  timeWindow: number;
  /** Event sequence */
  sequence: Array<{
    eventId: string;
    timestamp: Date;
    order: number;
  }>;
  /** Time gaps between events */
  timeGaps: number[];
  /** Temporal pattern type */
  patternType: 'burst' | 'periodic' | 'sequential' | 'simultaneous';
  /** Pattern regularity score (0-100) */
  regularity: number;
}

/**
 * Spatial Correlation
 */
export interface SpatialCorrelation {
  /** Common network segments */
  commonSegments: string[];
  /** Common geographic locations */
  commonLocations: string[];
  /** Network proximity score (0-100) */
  networkProximity: number;
  /** Geographic proximity score (0-100) */
  geographicProximity: number;
  /** Asset relationships */
  assetRelationships: Array<{
    type: 'same_subnet' | 'same_vlan' | 'same_location' | 'trust_relationship';
    assets: string[];
    strength: number;
  }>;
}

/**
 * Behavioral Correlation
 */
export interface BehavioralCorrelation {
  /** Common user behaviors */
  commonBehaviors: string[];
  /** Anomaly patterns */
  anomalyPatterns: string[];
  /** Behavioral similarity score (0-100) */
  similarityScore: number;
  /** Deviation from baseline */
  baselineDeviation: number;
  /** Behavioral clusters */
  clusters: Array<{
    name: string;
    events: string[];
    characteristics: string[];
  }>;
}

/**
 * Campaign Correlation
 */
export interface CampaignCorrelation {
  /** Campaign identifier */
  campaignId?: string;
  /** Campaign name */
  campaignName?: string;
  /** Threat actor attribution */
  threatActor?: string;
  /** Campaign confidence (0-100) */
  campaignConfidence: number;
  /** Common TTPs */
  commonTTPs: string[];
  /** Campaign timeline */
  timeline: {
    start: Date;
    end?: Date;
    phases: Array<{
      name: string;
      start: Date;
      end?: Date;
      events: string[];
    }>;
  };
}

/**
 * Attack Chain Analysis
 */
export interface AttackChainAnalysis {
  /** Attack phases identified */
  phases: Array<{
    name: string;
    order: number;
    events: string[];
    techniques: string[];
    confidence: number;
  }>;
  /** Kill chain mapping */
  killChainMapping: Record<string, string[]>;
  /** Chain completeness (0-100) */
  completeness: number;
  /** Chain confidence (0-100) */
  confidence: number;
  /** Missing phases */
  missingPhases: string[];
}

/**
 * Correlation Metadata
 */
export interface CorrelationMetadata {
  /** Correlation engine version */
  engineVersion: string;
  /** Correlation algorithms used */
  algorithmsUsed: string[];
  /** Processing duration in milliseconds */
  processingDuration: number;
  /** Correlated at timestamp */
  correlatedAt: Date;
  /** Data sources used */
  dataSources: string[];
  /** Correlation rules applied */
  rulesApplied: string[];
  /** Performance metrics */
  performanceMetrics: {
    eventsProcessed: number;
    correlationsFound: number;
    falsePositives: number;
    processingRate: number;
  };
}

/**
 * Correlation Error
 */
export interface CorrelationError {
  /** Error type */
  type: 'timeout' | 'insufficient_data' | 'algorithm_failure' | 'resource_limit';
  /** Error message */
  message: string;
  /** Affected events */
  affectedEvents: string[];
  /** Error severity */
  severity: 'low' | 'medium' | 'high' | 'critical';
  /** Recovery suggestions */
  recoverySuggestions: string[];
}

/**
 * Correlated Event Entity
 * 
 * Represents a group of enriched events that have been correlated based on
 * temporal, spatial, behavioral, or indicator-based relationships.
 * 
 * Key responsibilities:
 * - Store correlation analysis results
 * - Track correlation confidence and scoring
 * - Provide correlation pattern insights
 * - Support attack chain reconstruction
 * - Enable campaign attribution
 * 
 * Business Rules:
 * - Must reference at least two enriched events
 * - Correlation score must reflect analysis quality
 * - High-confidence correlations trigger threat escalation
 * - Attack chain analysis requires temporal ordering
 */
export class CorrelatedEvent extends BaseAggregateRoot<CorrelatedEventProps> {
  private static readonly MIN_EVENTS_FOR_CORRELATION = 2;
  private static readonly MIN_CORRELATION_SCORE = 0;
  private static readonly MAX_CORRELATION_SCORE = 100;
  private static readonly HIGH_CONFIDENCE_THRESHOLD = 80;

  constructor(props: CorrelatedEventProps, id?: UniqueEntityId) {
    super(props, id);
  }

  protected validate(): void {
    if (!this.props.primaryEventId) {
      throw new Error('Correlated event must have a primary event');
    }

    if (!this.props.relatedEventIds || this.props.relatedEventIds.length === 0) {
      throw new Error('Correlated event must have related events');
    }

    const totalEvents = 1 + this.props.relatedEventIds.length;
    if (totalEvents < CorrelatedEvent.MIN_EVENTS_FOR_CORRELATION) {
      throw new Error(`Correlation requires at least ${CorrelatedEvent.MIN_EVENTS_FOR_CORRELATION} events`);
    }

    if (!this.props.correlationAnalysis) {
      throw new Error('Correlated event must have correlation analysis');
    }

    if (!this.props.correlationMetadata) {
      throw new Error('Correlated event must have correlation metadata');
    }

    if (!Object.values(ConfidenceLevel).includes(this.props.confidence)) {
      throw new Error(`Invalid confidence level: ${this.props.confidence}`);
    }

    if (this.props.score < CorrelatedEvent.MIN_CORRELATION_SCORE || 
        this.props.score > CorrelatedEvent.MAX_CORRELATION_SCORE) {
      throw new Error(`Correlation score must be between ${CorrelatedEvent.MIN_CORRELATION_SCORE} and ${CorrelatedEvent.MAX_CORRELATION_SCORE}`);
    }

    // Validate correlation type
    if (!Object.values(CorrelationType).includes(this.props.correlationAnalysis.type)) {
      throw new Error(`Invalid correlation type: ${this.props.correlationAnalysis.type}`);
    }
  }

  /**
   * Create a correlated event from enriched events
   */
  static create(
    primaryEvent: EnrichedEvent,
    relatedEvents: EnrichedEvent[],
    correlationAnalysis: CorrelationAnalysis,
    correlationMetadata: CorrelationMetadata,
    options?: {
      errors?: CorrelationError[];
    }
  ): CorrelatedEvent {
    const confidence = CorrelatedEvent.calculateConfidence(correlationAnalysis);
    const score = CorrelatedEvent.calculateScore(correlationAnalysis, correlationMetadata);

    const props: CorrelatedEventProps = {
      primaryEventId: primaryEvent.id,
      relatedEventIds: relatedEvents.map(event => event.id),
      correlationAnalysis,
      correlationMetadata,
      confidence,
      score,
      errors: options?.errors || [],
    };

    const correlatedEvent = new CorrelatedEvent(props);

    // Publish domain event
    correlatedEvent.addDomainEvent(new EventsCorrelatedEvent(
      correlatedEvent.id,
      {
        correlationId: correlatedEvent.id.toString(),
        primaryEventId: primaryEvent.id.toString(),
        relatedEventIds: relatedEvents.map(e => e.id.toString()),
        correlationType: correlationAnalysis.type,
        confidence: confidence,
        score,
        eventCount: 1 + relatedEvents.length,
        commonIndicatorCount: correlationAnalysis.commonIndicators.length,
        hasAttackChain: !!correlationAnalysis.attackChain,
        hasCampaignAttribution: !!correlationAnalysis.campaignCorrelation?.campaignId,
        timestamp: new Date().toISOString(),
      }
    ));

    return correlatedEvent;
  }

  private static calculateConfidence(analysis: CorrelationAnalysis): ConfidenceLevel {
    let confidenceScore = 0;

    // Base confidence from patterns
    const avgPatternConfidence = analysis.patterns.length > 0
      ? analysis.patterns.reduce((sum, p) => sum + p.confidence, 0) / analysis.patterns.length
      : 0;
    confidenceScore += avgPatternConfidence * 0.4;

    // Confidence from common indicators
    confidenceScore += Math.min(analysis.commonIndicators.length * 10, 30);

    // Confidence from correlation type
    const typeConfidence = {
      [CorrelationType.INDICATOR]: 20,
      [CorrelationType.TEMPORAL]: 15,
      [CorrelationType.SPATIAL]: 15,
      [CorrelationType.BEHAVIORAL]: 10,
      [CorrelationType.CAMPAIGN]: 25,
      [CorrelationType.ATTACK_CHAIN]: 30,
      [CorrelationType.HYBRID]: 20,
    };
    confidenceScore += typeConfidence[analysis.type] || 10;

    // Bonus for attack chain or campaign correlation
    if (analysis.attackChain) {
      confidenceScore += analysis.attackChain.confidence * 0.2;
    }
    if (analysis.campaignCorrelation) {
      confidenceScore += analysis.campaignCorrelation.campaignConfidence * 0.2;
    }

    return ConfidenceLevel.fromNumericValue(Math.min(100, confidenceScore));
  }

  private static calculateScore(
    analysis: CorrelationAnalysis,
    metadata: CorrelationMetadata
  ): number {
    let score = 0;

    // Base score from pattern strength
    const avgPatternStrength = analysis.patterns.length > 0
      ? analysis.patterns.reduce((sum, p) => sum + p.strength, 0) / analysis.patterns.length
      : 0;
    score += avgPatternStrength * 40;

    // Score from common indicators
    score += Math.min(analysis.commonIndicators.length * 5, 25);

    // Score from correlation completeness
    if (analysis.attackChain) {
      score += analysis.attackChain.completeness * 0.2;
    }

    // Score from temporal regularity
    if (analysis.temporalCorrelation) {
      score += analysis.temporalCorrelation.regularity * 0.15;
    }

    // Score from spatial proximity
    if (analysis.spatialCorrelation) {
      const avgProximity = (analysis.spatialCorrelation.networkProximity + 
                           analysis.spatialCorrelation.geographicProximity) / 2;
      score += avgProximity * 0.1;
    }

    // Penalty for errors
    const errorPenalty = metadata.performanceMetrics.falsePositives * 2;
    score = Math.max(0, score - errorPenalty);

    return Math.min(100, Math.round(score));
  }

  /**
   * Get primary event ID
   */
  get primaryEventId(): UniqueEntityId {
    return this.props.primaryEventId;
  }

  /**
   * Get related event IDs
   */
  get relatedEventIds(): UniqueEntityId[] {
    return [...this.props.relatedEventIds];
  }

  /**
   * Get all event IDs (primary + related)
   */
  get allEventIds(): UniqueEntityId[] {
    return [this.props.primaryEventId, ...this.props.relatedEventIds];
  }

  /**
   * Get correlation analysis
   */
  get correlationAnalysis(): CorrelationAnalysis {
    return this.props.correlationAnalysis;
  }

  /**
   * Get correlation metadata
   */
  get correlationMetadata(): CorrelationMetadata {
    return this.props.correlationMetadata;
  }

  /**
   * Get confidence level
   */
  get confidence(): ConfidenceLevel {
    return this.props.confidence;
  }

  /**
   * Get correlation score
   */
  get score(): number {
    return this.props.score;
  }

  /**
   * Get correlation errors
   */
  get errors(): CorrelationError[] {
    return [...this.props.errors];
  }

  /**
   * Get correlation type
   */
  get correlationType(): CorrelationType {
    return this.props.correlationAnalysis.type;
  }

  /**
   * Get event count
   */
  get eventCount(): number {
    return 1 + this.props.relatedEventIds.length;
  }

  /**
   * Check if correlation is high confidence
   */
  isHighConfidence(): boolean {
    return this.props.score >= CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD;
  }

  /**
   * Check if correlation has attack chain
   */
  hasAttackChain(): boolean {
    return !!this.props.correlationAnalysis.attackChain;
  }

  /**
   * Check if correlation has campaign attribution
   */
  hasCampaignAttribution(): boolean {
    return !!this.props.correlationAnalysis.campaignCorrelation?.campaignId;
  }

  /**
   * Check if correlation has common indicators
   */
  hasCommonIndicators(): boolean {
    return this.props.correlationAnalysis.commonIndicators.length > 0;
  }

  /**
   * Check if correlation spans multiple assets
   */
  spansMultipleAssets(): boolean {
    return this.props.correlationAnalysis.spatialCorrelation?.assetRelationships.length > 0;
  }

  /**
   * Check if correlation indicates coordinated attack
   */
  indicatesCoordinatedAttack(): boolean {
    return this.hasAttackChain() || 
           this.hasCampaignAttribution() || 
           (this.correlationType === CorrelationType.TEMPORAL && this.isHighConfidence());
  }

  /**
   * Get threat escalation priority
   */
  getThreatEscalationPriority(): 'low' | 'medium' | 'high' | 'critical' {
    if (this.indicatesCoordinatedAttack() && this.isHighConfidence()) {
      return 'critical';
    }
    if (this.hasAttackChain() || this.hasCampaignAttribution()) {
      return 'high';
    }
    if (this.isHighConfidence() && this.hasCommonIndicators()) {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Get investigation recommendations
   */
  getInvestigationRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.hasAttackChain()) {
      recommendations.push('Analyze complete attack chain progression');
      recommendations.push('Identify missing attack phases');
      recommendations.push('Assess potential future attack steps');
    }

    if (this.hasCampaignAttribution()) {
      recommendations.push('Research threat actor TTPs');
      recommendations.push('Check for additional campaign indicators');
      recommendations.push('Review historical campaign activities');
    }

    if (this.hasCommonIndicators()) {
      recommendations.push('Investigate all systems with common indicators');
      recommendations.push('Expand IOC hunting across environment');
    }

    if (this.spansMultipleAssets()) {
      recommendations.push('Assess lateral movement paths');
      recommendations.push('Review network segmentation effectiveness');
    }

    if (this.correlationType === CorrelationType.TEMPORAL) {
      recommendations.push('Analyze event timing patterns');
      recommendations.push('Look for additional events in time window');
    }

    return recommendations;
  }

  /**
   * Get correlation summary
   */
  getCorrelationSummary(): {
    type: string;
    eventCount: number;
    confidence: string;
    score: number;
    hasAttackChain: boolean;
    hasCampaignAttribution: boolean;
    commonIndicatorCount: number;
    threatPriority: string;
  } {
    return {
      type: this.correlationType,
      eventCount: this.eventCount,
      confidence: this.confidence,
      score: this.score,
      hasAttackChain: this.hasAttackChain(),
      hasCampaignAttribution: this.hasCampaignAttribution(),
      commonIndicatorCount: this.props.correlationAnalysis.commonIndicators.length,
      threatPriority: this.getThreatEscalationPriority(),
    };
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      id: this.id.toString(),
      primaryEventId: this.props.primaryEventId.toString(),
      relatedEventIds: this.props.relatedEventIds.map(id => id.toString()),
      correlationAnalysis: this.props.correlationAnalysis,
      correlationMetadata: this.props.correlationMetadata,
      confidence: this.props.confidence,
      score: this.props.score,
      errors: this.props.errors,
      analysis: {
        isHighConfidence: this.isHighConfidence(),
        hasAttackChain: this.hasAttackChain(),
        hasCampaignAttribution: this.hasCampaignAttribution(),
        hasCommonIndicators: this.hasCommonIndicators(),
        spansMultipleAssets: this.spansMultipleAssets(),
        indicatesCoordinatedAttack: this.indicatesCoordinatedAttack(),
        threatEscalationPriority: this.getThreatEscalationPriority(),
        investigationRecommendations: this.getInvestigationRecommendations(),
        correlationSummary: this.getCorrelationSummary(),
      },
      createdAt: this.createdAt?.toISOString(),
      updatedAt: this.updatedAt?.toISOString(),
    };
  }
}
