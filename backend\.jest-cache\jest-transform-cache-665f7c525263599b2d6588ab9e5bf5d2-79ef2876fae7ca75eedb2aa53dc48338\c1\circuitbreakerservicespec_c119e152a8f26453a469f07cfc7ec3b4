72deb0ae0a10ad1943fdfade23edcefe
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const circuit_breaker_service_1 = require("../circuit-breaker.service");
const circuit_breaker_1 = require("@/shared-kernel/patterns/circuit-breaker");
const service_unavailable_exception_1 = require("@/shared-kernel/exceptions/service-unavailable.exception");
describe('AICircuitBreakerService', () => {
    let service;
    const defaultConfig = {
        failureThreshold: 3,
        recoveryTimeout: 5000,
        monitoringPeriod: 10000,
        halfOpenMaxCalls: 2,
        enableFallback: false,
    };
    const fallbackConfig = {
        failureThreshold: 3,
        recoveryTimeout: 5000,
        monitoringPeriod: 10000,
        halfOpenMaxCalls: 2,
        enableFallback: true,
        fallbackResponse: { message: 'fallback response' },
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [circuit_breaker_service_1.AICircuitBreakerService],
        }).compile();
        service = module.get(circuit_breaker_service_1.AICircuitBreakerService);
    });
    afterEach(() => {
        // Clean up all registered providers
        service.resetAllProviders();
    });
    describe('registerProvider', () => {
        it('should register a new provider with circuit breaker', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            expect(service.isProviderAvailable(providerId)).toBe(true);
        });
        it('should register multiple providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            expect(service.isProviderAvailable('provider1')).toBe(true);
            expect(service.isProviderAvailable('provider2')).toBe(true);
            expect(service.getAvailableProviders()).toHaveLength(2);
        });
    });
    describe('executeWithProtection', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should execute successful operation', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toBe('success');
            expect(operation).toHaveBeenCalledTimes(1);
        });
        it('should handle single failure and continue', async () => {
            const operation = jest.fn()
                .mockRejectedValueOnce(new Error('failure'))
                .mockResolvedValue('success');
            // First call should fail
            await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            // Second call should succeed
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toBe('success');
        });
        it('should open circuit breaker after threshold failures', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail 3 times to reach threshold
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            // Circuit should now be open
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            // Next call should fail with circuit breaker exception
            await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
        });
        it('should use fallback function when circuit is open', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            const fallback = jest.fn().mockResolvedValue('fallback result');
            // Fail enough times to open circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            // Execute with fallback
            const result = await service.executeWithProtection('test-provider', operation, fallback);
            expect(result).toBe('fallback result');
            expect(fallback).toHaveBeenCalledTimes(1);
        });
        it('should use cached fallback response when available', async () => {
            // Register provider with fallback enabled
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', fallbackConfig);
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail enough times to open circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            // Execute without fallback function - should use cached response
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toEqual({ message: 'fallback response' });
        });
        it('should throw error for unregistered provider', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await expect(service.executeWithProtection('unknown-provider', operation)).rejects.toThrow('Circuit breaker not registered for provider: unknown-provider');
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
            expect(metrics.providerType).toBe('test');
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(metrics.failureCount).toBe(0);
            expect(metrics.successCount).toBe(0);
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after operations', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await service.executeWithProtection('test-provider', operation);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.successCount).toBe(1);
            expect(metrics.totalCalls).toBe(1);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('isProviderAvailable', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return true for available provider', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
        });
        it('should return false for unregistered provider', () => {
            expect(service.isProviderAvailable('unknown-provider')).toBe(false);
        });
        it('should return false when circuit is open', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail enough times to open circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            expect(service.isProviderAvailable('test-provider')).toBe(false);
        });
    });
    describe('getAvailableProviders', () => {
        it('should return empty array when no providers available', () => {
            expect(service.getAvailableProviders()).toEqual([]);
        });
        it('should return all available providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const available = service.getAvailableProviders();
            expect(available).toHaveLength(2);
            expect(available).toContain('provider1');
            expect(available).toContain('provider2');
        });
        it('should exclude providers with open circuits', async () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            // Open circuit for provider1
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('provider1', operation)).rejects.toThrow('failure');
            }
            const available = service.getAvailableProviders();
            expect(available).toHaveLength(1);
            expect(available).toContain('provider2');
            expect(available).not.toContain('provider1');
        });
    });
    describe('resetProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should reset circuit breaker state', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Open the circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            // Reset the circuit
            service.resetProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.failureCount).toBe(0);
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
        });
    });
    describe('forceOpenProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should force circuit breaker to open state', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            service.forceOpenProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { failureThreshold: 5 };
            service.updateProviderConfig('test-provider', newConfig);
            // Verify the provider is still registered and available
            expect(service.isProviderAvailable('test-provider')).toBe(true);
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider circuit breaker', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            service.unregisterProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
    });
    describe('getHealthStatus', () => {
        it('should return empty object when no providers registered', () => {
            const status = service.getHealthStatus();
            expect(status).toEqual({});
        });
        it('should return health status for all providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const status = service.getHealthStatus();
            expect(Object.keys(status)).toHaveLength(2);
            expect(status['provider1']).toBeDefined();
            expect(status['provider1'].available).toBe(true);
            expect(status['provider1'].metrics).toBeDefined();
            expect(status['provider2']).toBeDefined();
            expect(status['provider2'].available).toBe(true);
            expect(status['provider2'].metrics).toBeDefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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