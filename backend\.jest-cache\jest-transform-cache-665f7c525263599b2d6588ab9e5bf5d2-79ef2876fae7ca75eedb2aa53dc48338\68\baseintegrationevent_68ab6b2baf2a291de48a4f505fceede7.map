{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\integration\\base-integration-event.ts", "mappings": ";;;AAAA,+BAAoC;AAEpC;;;;GAIG;AACH,MAAsB,oBAAoB;IAWxC,YACE,SAAiB,EACjB,SAAY,EACZ,OAMC;QAED,IAAI,CAAC,EAAE,GAAG,IAAA,SAAM,GAAE,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,MAAM,IAAI,kBAAkB,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,IAAI,KAAK,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,aAAa,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,WAAW,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,QAAQ,IAAI,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,SAAS;SACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;IACJ,CAAC;IAiBD;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,cAAc,IAAK,IAAI,CAAC,SAAiB,EAAE,aAAa,IAAI,IAAI,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAQ,IAAI,CAAC,SAAiB,EAAE,IAAI,IAAI,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,CAAC,uBAAuB;IACtC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,CAAC,CAAC,CAAC,yBAAyB;IACrC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,CAAC,yBAAyB;IACxC,CAAC;CACF;AArKD,oDAqKC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\integration\\base-integration-event.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\n\n/**\n * Base Integration Event\n *\n * Abstract base class for all integration events in the system\n */\nexport abstract class BaseIntegrationEvent<T = any> {\n  public readonly id: string;\n  public readonly timestamp: Date;\n  public readonly version: string;\n  public readonly source: string;\n  public readonly eventType: string;\n  public readonly eventData: T;\n  private readonly _correlationId?: string;\n  private readonly _causationId?: string;\n  private readonly _metadata: Record<string, any>;\n\n  constructor(\n    eventType: string,\n    eventData: T,\n    options?: {\n      source?: string;\n      version?: string;\n      correlationId?: string;\n      causationId?: string;\n      metadata?: Record<string, any>;\n    }\n  ) {\n    this.id = uuidv4();\n    this.timestamp = new Date();\n    this.eventType = eventType;\n    this.eventData = eventData;\n    this.source = options?.source || 'sentinel-backend';\n    this.version = options?.version || '1.0';\n    this._correlationId = options?.correlationId;\n    this._causationId = options?.causationId;\n    this._metadata = options?.metadata || {};\n  }\n\n  /**\n   * Get event ID\n   */\n  getId(): string {\n    return this.id;\n  }\n\n  /**\n   * Get event timestamp\n   */\n  getTimestamp(): Date {\n    return this.timestamp;\n  }\n\n  /**\n   * Get event type\n   */\n  getEventType(): string {\n    return this.eventType;\n  }\n\n  /**\n   * Get event source\n   */\n  getSource(): string {\n    return this.source;\n  }\n\n  /**\n   * Get event version\n   */\n  getVersion(): string {\n    return this.version;\n  }\n\n  /**\n   * Get event data\n   */\n  getEventData(): any {\n    return this.eventData;\n  }\n\n  /**\n   * Convert event to JSON\n   */\n  toJSON(): any {\n    return {\n      id: this.id,\n      timestamp: this.timestamp.toISOString(),\n      eventType: this.eventType,\n      source: this.source,\n      version: this.version,\n      data: this.eventData,\n    };\n  }\n\n  /**\n   * Get event metadata\n   */\n  getMetadata(): any {\n    return {\n      id: this.id,\n      timestamp: this.timestamp,\n      eventType: this.eventType,\n      source: this.source,\n      version: this.version,\n    };\n  }\n\n  /**\n   * Validate event data\n   */\n  abstract validate(): boolean;\n\n  /**\n   * Get event priority\n   */\n  abstract getPriority(): 'low' | 'medium' | 'high' | 'critical';\n\n  /**\n   * Check if event requires immediate processing\n   */\n  abstract requiresImmediateProcessing(): boolean;\n\n  /**\n   * Get event correlation ID for tracking related events\n   */\n  getCorrelationId(): string | null {\n    return this._correlationId || (this.eventData as any)?.correlationId || null;\n  }\n\n  /**\n   * Get event causation ID for tracking event chains\n   */\n  getCausationId(): string | null {\n    return this._causationId || null;\n  }\n\n  /**\n   * Get event metadata\n   */\n  getEventMetadata(): Record<string, any> {\n    return { ...this._metadata };\n  }\n\n  /**\n   * Get event tags for categorization\n   */\n  getTags(): string[] {\n    return (this.eventData as any)?.tags || [];\n  }\n\n  /**\n   * Check if event is retryable in case of processing failure\n   */\n  isRetryable(): boolean {\n    return true; // Default to retryable\n  }\n\n  /**\n   * Get maximum retry attempts\n   */\n  getMaxRetryAttempts(): number {\n    return 3; // Default retry attempts\n  }\n\n  /**\n   * Get retry delay in milliseconds\n   */\n  getRetryDelay(): number {\n    return 1000; // Default 1 second delay\n  }\n}\n"], "version": 3}