import { BaseAggregateRoot } from '../../../../../shared-kernel/domain/base-aggregate-root';
import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { ThreatSeverity } from '../../enums/threat-severity.enum';
import { ConfidenceLevel } from '../../enums/confidence-level.enum';
import { IncidentCreatedEvent } from '../../events/incident-created.event';
import { IncidentStatusChangedEvent } from '../../events/incident-status-changed.event';
import { IncidentEscalatedEvent } from '../../events/incident-escalated.event';

/**
 * Incident Properties
 */
export interface IncidentProps {
  /** Incident title */
  title: string;
  /** Incident description */
  description: string;
  /** Incident severity */
  severity: ThreatSeverity;
  /** Incident category */
  category: string;
  /** Incident type */
  type: string;
  /** Incident status */
  status: IncidentStatus;
  /** Incident priority */
  priority: IncidentPriority;
  /** Incident confidence */
  confidence: ConfidenceLevel;
  /** Related event IDs */
  relatedEventIds: UniqueEntityId[];
  /** Related threat IDs */
  relatedThreatIds: UniqueEntityId[];
  /** Related vulnerability IDs */
  relatedVulnerabilityIds: UniqueEntityId[];
  /** Affected assets */
  affectedAssets: IncidentAsset[];
  /** Incident timeline */
  timeline: IncidentTimeline;
  /** Response team */
  responseTeam: IncidentResponseTeam;
  /** Investigation details */
  investigation: IncidentInvestigation;
  /** Containment actions */
  containment: IncidentContainment;
  /** Recovery actions */
  recovery: IncidentRecovery;
  /** Lessons learned */
  lessonsLearned: IncidentLessonsLearned;
  /** Communication log */
  communications: IncidentCommunication[];
  /** Evidence collected */
  evidence: IncidentEvidence[];
  /** Impact assessment */
  impact: IncidentImpact;
  /** Compliance requirements */
  compliance: IncidentCompliance[];
  /** Incident tags */
  tags: string[];
  /** Custom attributes */
  attributes: Record<string, any>;
}

/**
 * Incident Status
 */
export enum IncidentStatus {
  OPEN = 'open',
  INVESTIGATING = 'investigating',
  CONTAINING = 'containing',
  CONTAINED = 'contained',
  ERADICATING = 'eradicating',
  ERADICATED = 'eradicated',
  RECOVERING = 'recovering',
  RECOVERED = 'recovered',
  CLOSED = 'closed',
  FALSE_POSITIVE = 'false_positive',
}

/**
 * Incident Priority
 */
export enum IncidentPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
  EMERGENCY = 'emergency',
}

/**
 * Incident Asset
 */
export interface IncidentAsset {
  /** Asset identifier */
  assetId: string;
  /** Asset name */
  assetName: string;
  /** Asset type */
  assetType: string;
  /** Asset criticality */
  criticality: 'low' | 'medium' | 'high' | 'critical';
  /** Impact on asset */
  impact: 'none' | 'low' | 'medium' | 'high' | 'critical';
  /** Asset status */
  status: 'operational' | 'degraded' | 'offline' | 'compromised' | 'isolated';
  /** Recovery status */
  recoveryStatus: 'not_started' | 'in_progress' | 'completed';
  /** Business services affected */
  affectedServices: string[];
}

/**
 * Incident Timeline
 */
export interface IncidentTimeline {
  /** When incident was detected */
  detected: Date;
  /** When incident was reported */
  reported: Date;
  /** When response started */
  responseStarted?: Date;
  /** When containment started */
  containmentStarted?: Date;
  /** When incident was contained */
  contained?: Date;
  /** When eradication started */
  eradicationStarted?: Date;
  /** When incident was eradicated */
  eradicated?: Date;
  /** When recovery started */
  recoveryStarted?: Date;
  /** When recovery was completed */
  recoveryCompleted?: Date;
  /** When incident was closed */
  closed?: Date;
  /** Key milestones */
  milestones: Array<{
    name: string;
    timestamp: Date;
    description: string;
    performedBy: string;
  }>;
}

/**
 * Incident Response Team
 */
export interface IncidentResponseTeam {
  /** Incident commander */
  incidentCommander?: string;
  /** Lead investigator */
  leadInvestigator?: string;
  /** Team members */
  members: Array<{
    userId: string;
    name: string;
    role: string;
    expertise: string[];
    assignedAt: Date;
  }>;
  /** External consultants */
  externalConsultants: Array<{
    name: string;
    organization: string;
    role: string;
    contactInfo: string;
  }>;
  /** Escalation contacts */
  escalationContacts: Array<{
    name: string;
    role: string;
    contactInfo: string;
    escalationLevel: number;
  }>;
}

/**
 * Incident Investigation
 */
export interface IncidentInvestigation {
  /** Investigation status */
  status: 'not_started' | 'in_progress' | 'completed';
  /** Investigation findings */
  findings: Array<{
    finding: string;
    confidence: ConfidenceLevel;
    evidence: string[];
    timestamp: Date;
    investigator: string;
  }>;
  /** Root cause analysis */
  rootCause?: {
    cause: string;
    contributing_factors: string[];
    confidence: ConfidenceLevel;
    evidence: string[];
  };
  /** Attack timeline */
  attackTimeline: Array<{
    timestamp: Date;
    event: string;
    technique: string;
    evidence: string[];
    confidence: ConfidenceLevel;
  }>;
  /** Indicators of compromise */
  iocs: string[];
  /** Attribution */
  attribution?: {
    actor: string;
    confidence: ConfidenceLevel;
    evidence: string[];
  };
}

/**
 * Incident Containment
 */
export interface IncidentContainment {
  /** Containment status */
  status: 'not_started' | 'in_progress' | 'completed';
  /** Containment strategy */
  strategy: string;
  /** Containment actions */
  actions: Array<{
    action: string;
    status: 'planned' | 'in_progress' | 'completed' | 'failed';
    performedBy: string;
    timestamp?: Date;
    result?: string;
    effectiveness: number;
  }>;
  /** Isolation measures */
  isolationMeasures: Array<{
    asset: string;
    measure: string;
    timestamp: Date;
    performedBy: string;
    status: 'active' | 'removed';
  }>;
  /** Containment effectiveness */
  effectiveness: number;
}

/**
 * Incident Recovery
 */
export interface IncidentRecovery {
  /** Recovery status */
  status: 'not_started' | 'in_progress' | 'completed';
  /** Recovery plan */
  plan: string;
  /** Recovery actions */
  actions: Array<{
    action: string;
    status: 'planned' | 'in_progress' | 'completed' | 'failed';
    performedBy: string;
    timestamp?: Date;
    result?: string;
    verification: string;
  }>;
  /** Business continuity measures */
  businessContinuity: Array<{
    service: string;
    measure: string;
    status: 'active' | 'completed';
    effectiveness: number;
  }>;
  /** Recovery validation */
  validation: {
    completed: boolean;
    validatedBy: string;
    validationDate?: Date;
    validationResults: string[];
  };
}

/**
 * Incident Lessons Learned
 */
export interface IncidentLessonsLearned {
  /** Lessons learned session completed */
  completed: boolean;
  /** Session date */
  sessionDate?: Date;
  /** Participants */
  participants: string[];
  /** What went well */
  whatWentWell: string[];
  /** What could be improved */
  improvements: string[];
  /** Action items */
  actionItems: Array<{
    item: string;
    assignedTo: string;
    dueDate: Date;
    status: 'open' | 'in_progress' | 'completed';
  }>;
  /** Process improvements */
  processImprovements: string[];
  /** Technology improvements */
  technologyImprovements: string[];
  /** Training needs */
  trainingNeeds: string[];
}

/**
 * Incident Communication
 */
export interface IncidentCommunication {
  /** Communication type */
  type: 'internal' | 'external' | 'regulatory' | 'customer' | 'media';
  /** Message */
  message: string;
  /** Recipients */
  recipients: string[];
  /** Sent by */
  sentBy: string;
  /** Sent at */
  sentAt: Date;
  /** Communication channel */
  channel: string;
  /** Acknowledgments */
  acknowledgments: Array<{
    recipient: string;
    acknowledgedAt: Date;
  }>;
}

/**
 * Incident Evidence
 */
export interface IncidentEvidence {
  /** Evidence type */
  type: 'log' | 'file' | 'network_capture' | 'memory_dump' | 'disk_image' | 'screenshot' | 'document';
  /** Evidence description */
  description: string;
  /** Evidence location */
  location: string;
  /** Collected by */
  collectedBy: string;
  /** Collection timestamp */
  collectedAt: Date;
  /** Chain of custody */
  chainOfCustody: Array<{
    transferredTo: string;
    transferredAt: Date;
    purpose: string;
  }>;
  /** Evidence hash */
  hash?: string;
  /** Evidence size */
  size?: number;
  /** Analysis results */
  analysisResults?: string[];
}

/**
 * Incident Impact
 */
export interface IncidentImpact {
  /** Business impact */
  business: {
    revenue_loss: number;
    productivity_loss: number;
    customer_impact: number;
    reputation_impact: 'low' | 'medium' | 'high' | 'critical';
  };
  /** Technical impact */
  technical: {
    systems_affected: number;
    data_compromised: boolean;
    data_volume: number;
    service_downtime: number;
  };
  /** Compliance impact */
  compliance: {
    regulations_affected: string[];
    reporting_required: boolean;
    potential_fines: number;
  };
  /** Recovery costs */
  costs: {
    investigation: number;
    containment: number;
    recovery: number;
    legal: number;
    external_consultants: number;
    total: number;
  };
}

/**
 * Incident Compliance
 */
export interface IncidentCompliance {
  /** Regulation */
  regulation: string;
  /** Reporting required */
  reportingRequired: boolean;
  /** Reporting deadline */
  reportingDeadline?: Date;
  /** Report submitted */
  reportSubmitted: boolean;
  /** Submission date */
  submissionDate?: Date;
  /** Compliance status */
  status: 'compliant' | 'non_compliant' | 'pending';
  /** Requirements */
  requirements: string[];
}

/**
 * Incident Entity
 * 
 * Represents a security incident with comprehensive tracking and management.
 * Orchestrates the complete incident response lifecycle.
 * 
 * Key responsibilities:
 * - Incident lifecycle management
 * - Response team coordination
 * - Investigation tracking
 * - Containment and recovery
 * - Compliance management
 * - Lessons learned capture
 * 
 * Business Rules:
 * - Critical incidents require immediate commander assignment
 * - Status changes must follow proper workflow
 * - Evidence chain of custody must be maintained
 * - Compliance deadlines must be tracked
 */
export class Incident extends BaseAggregateRoot<IncidentProps> {
  private static readonly CRITICAL_SEVERITY_THRESHOLD = ThreatSeverity.CRITICAL;
  private static readonly HIGH_PRIORITY_THRESHOLD = IncidentPriority.HIGH;

  // Explicitly declare inherited properties to help TypeScript
  protected readonly props: IncidentProps;
  protected readonly _id: UniqueEntityId;

  get id(): UniqueEntityId {
    return this._id;
  }

  protected addDomainEvent(domainEvent: any): void {
    return super.addDomainEvent(domainEvent);
  }

  constructor(props: IncidentProps, id?: UniqueEntityId) {
    super(props, id);
    this.validateInvariants();
  }

  protected validateInvariants(): void {
    super.validateInvariants();
    if (!this.props.title || this.props.title.trim().length === 0) {
      throw new Error('Incident must have a title');
    }

    if (!this.props.description || this.props.description.trim().length === 0) {
      throw new Error('Incident must have a description');
    }

    if (!Object.values(ThreatSeverity).includes(this.props.severity)) {
      throw new Error(`Invalid incident severity: ${this.props.severity}`);
    }

    if (!Object.values(IncidentStatus).includes(this.props.status)) {
      throw new Error(`Invalid incident status: ${this.props.status}`);
    }

    if (!Object.values(IncidentPriority).includes(this.props.priority)) {
      throw new Error(`Invalid incident priority: ${this.props.priority}`);
    }

    if (!this.props.category || this.props.category.trim().length === 0) {
      throw new Error('Incident must have a category');
    }

    if (!this.props.type || this.props.type.trim().length === 0) {
      throw new Error('Incident must have a type');
    }

    if (!this.props.timeline.detected) {
      throw new Error('Incident must have a detection timestamp');
    }

    if (!this.props.timeline.reported) {
      throw new Error('Incident must have a reporting timestamp');
    }

    // Critical incidents must have incident commander
    if (this.isCritical() && !this.props.responseTeam.incidentCommander) {
      throw new Error('Critical incidents must have an incident commander assigned');
    }
  }

  /**
   * Create a new incident
   */
  static create(
    basicInfo: {
      title: string;
      description: string;
      severity: ThreatSeverity;
      category: string;
      type: string;
    },
    timestamps: {
      detectedAt: Date;
      reportedAt: Date;
    },
    options?: {
      relatedEventIds?: UniqueEntityId[];
      relatedThreatIds?: UniqueEntityId[];
      relatedVulnerabilityIds?: UniqueEntityId[];
      affectedAssets?: IncidentAsset[];
      incidentCommander?: string;
      tags?: string[];
      attributes?: Record<string, any>;
    }
  ): Incident {
    const priority = Incident.calculatePriority(basicInfo.severity);
    const confidence = ConfidenceLevel.MEDIUM; // Default confidence

    const props: IncidentProps = {
      title: basicInfo.title.trim(),
      description: basicInfo.description.trim(),
      severity: basicInfo.severity,
      category: basicInfo.category.trim(),
      type: basicInfo.type.trim(),
      status: IncidentStatus.OPEN,
      priority,
      confidence,
      relatedEventIds: options?.relatedEventIds || [],
      relatedThreatIds: options?.relatedThreatIds || [],
      relatedVulnerabilityIds: options?.relatedVulnerabilityIds || [],
      affectedAssets: options?.affectedAssets || [],
      timeline: {
        detected: timestamps.detectedAt,
        reported: timestamps.reportedAt,
        milestones: [],
      },
      responseTeam: {
        incidentCommander: options?.incidentCommander,
        members: [],
        externalConsultants: [],
        escalationContacts: [],
      },
      investigation: {
        status: 'not_started',
        findings: [],
        attackTimeline: [],
        iocs: [],
      },
      containment: {
        status: 'not_started',
        strategy: '',
        actions: [],
        isolationMeasures: [],
        effectiveness: 0,
      },
      recovery: {
        status: 'not_started',
        plan: '',
        actions: [],
        businessContinuity: [],
        validation: {
          completed: false,
          validatedBy: '',
          validationResults: [],
        },
      },
      lessonsLearned: {
        completed: false,
        participants: [],
        whatWentWell: [],
        improvements: [],
        actionItems: [],
        processImprovements: [],
        technologyImprovements: [],
        trainingNeeds: [],
      },
      communications: [],
      evidence: [],
      impact: {
        business: {
          revenue_loss: 0,
          productivity_loss: 0,
          customer_impact: 0,
          reputation_impact: 'low',
        },
        technical: {
          systems_affected: options?.affectedAssets?.length || 0,
          data_compromised: false,
          data_volume: 0,
          service_downtime: 0,
        },
        compliance: {
          regulations_affected: [],
          reporting_required: false,
          potential_fines: 0,
        },
        costs: {
          investigation: 0,
          containment: 0,
          recovery: 0,
          legal: 0,
          external_consultants: 0,
          total: 0,
        },
      },
      compliance: [],
      tags: options?.tags || [],
      attributes: options?.attributes || {},
    };

    const incident = new Incident(props);

    // Publish domain event
    incident.addDomainEvent(new IncidentCreatedEvent(
      incident.id,
      {
        incidentId: incident.id.toString(),
        title: incident.props.title,
        severity: incident.props.severity,
        priority: incident.props.priority,
        category: incident.props.category,
        type: incident.props.type,
        detectedAt: detectedAt.toISOString(),
        reportedAt: reportedAt.toISOString(),
        affectedAssetCount: incident.props.affectedAssets.length,
        hasIncidentCommander: !!incident.props.responseTeam.incidentCommander,
        timestamp: new Date().toISOString(),
      }
    ));

    return incident;
  }

  private static calculatePriority(severity: ThreatSeverity): IncidentPriority {
    switch (severity) {
      case ThreatSeverity.CRITICAL: return IncidentPriority.EMERGENCY;
      case ThreatSeverity.HIGH: return IncidentPriority.CRITICAL;
      case ThreatSeverity.MEDIUM: return IncidentPriority.HIGH;
      case ThreatSeverity.LOW: return IncidentPriority.MEDIUM;
      default: return IncidentPriority.LOW;
    }
  }

  /**
   * Get incident title
   */
  get title(): string {
    return this.props.title;
  }

  /**
   * Get incident description
   */
  get description(): string {
    return this.props.description;
  }

  /**
   * Get incident severity
   */
  get severity(): ThreatSeverity {
    return this.props.severity;
  }

  /**
   * Get incident status
   */
  get status(): IncidentStatus {
    return this.props.status;
  }

  /**
   * Get incident priority
   */
  get priority(): IncidentPriority {
    return this.props.priority;
  }

  /**
   * Get incident timeline
   */
  get timeline(): IncidentTimeline {
    return this.props.timeline;
  }

  /**
   * Get response team
   */
  get responseTeam(): IncidentResponseTeam {
    return this.props.responseTeam;
  }

  /**
   * Get affected assets
   */
  get affectedAssets(): IncidentAsset[] {
    return [...this.props.affectedAssets];
  }

  /**
   * Change incident status
   */
  changeStatus(newStatus: IncidentStatus, reason: string): void {
    if (newStatus === this.props.status) {
      return; // No change needed
    }

    const oldStatus = this.props.status;
    this.props.status = newStatus;

    // Update timeline based on status
    this.updateTimelineForStatus(newStatus);

    // Publish domain event
    this.addDomainEvent(new IncidentStatusChangedEvent(
      this.id,
      {
        incidentId: this.id.toString(),
        oldStatus,
        newStatus,
        reason,
        timestamp: new Date().toISOString(),
      }
    ));
  }

  private updateTimelineForStatus(status: IncidentStatus): void {
    const now = new Date();
    
    switch (status) {
      case IncidentStatus.INVESTIGATING:
        if (!this.props.timeline.responseStarted) {
          this.props.timeline.responseStarted = now;
        }
        this.props.investigation.status = 'in_progress';
        break;
      case IncidentStatus.CONTAINING:
        if (!this.props.timeline.containmentStarted) {
          this.props.timeline.containmentStarted = now;
        }
        this.props.containment.status = 'in_progress';
        break;
      case IncidentStatus.CONTAINED:
        if (!this.props.timeline.contained) {
          this.props.timeline.contained = now;
        }
        this.props.containment.status = 'completed';
        break;
      case IncidentStatus.ERADICATING:
        if (!this.props.timeline.eradicationStarted) {
          this.props.timeline.eradicationStarted = now;
        }
        break;
      case IncidentStatus.ERADICATED:
        if (!this.props.timeline.eradicated) {
          this.props.timeline.eradicated = now;
        }
        break;
      case IncidentStatus.RECOVERING:
        if (!this.props.timeline.recoveryStarted) {
          this.props.timeline.recoveryStarted = now;
        }
        this.props.recovery.status = 'in_progress';
        break;
      case IncidentStatus.RECOVERED:
        if (!this.props.timeline.recoveryCompleted) {
          this.props.timeline.recoveryCompleted = now;
        }
        this.props.recovery.status = 'completed';
        break;
      case IncidentStatus.CLOSED:
        if (!this.props.timeline.closed) {
          this.props.timeline.closed = now;
        }
        break;
    }
  }

  /**
   * Escalate incident
   */
  escalate(reason: string, escalatedTo: string): void {
    // Increase priority if possible
    const currentPriorityIndex = Object.values(IncidentPriority).indexOf(this.props.priority);
    if (currentPriorityIndex < Object.values(IncidentPriority).length - 1) {
      this.props.priority = Object.values(IncidentPriority)[currentPriorityIndex + 1];
    }

    // Publish escalation event
    this.addDomainEvent(new IncidentEscalatedEvent(
      this.id,
      {
        incidentId: this.id.toString(),
        reason,
        escalatedTo,
        newPriority: this.props.priority,
        timestamp: new Date().toISOString(),
      }
    ));
  }

  /**
   * Check if incident is critical
   */
  isCritical(): boolean {
    return this.props.severity === ThreatSeverity.CRITICAL ||
           this.props.priority === IncidentPriority.EMERGENCY;
  }

  /**
   * Check if incident is active
   */
  isActive(): boolean {
    const activeStatuses = [
      IncidentStatus.OPEN,
      IncidentStatus.INVESTIGATING,
      IncidentStatus.CONTAINING,
      IncidentStatus.ERADICATING,
      IncidentStatus.RECOVERING,
    ];
    return activeStatuses.includes(this.props.status);
  }

  /**
   * Check if incident is resolved
   */
  isResolved(): boolean {
    return [
      IncidentStatus.RECOVERED,
      IncidentStatus.CLOSED,
    ].includes(this.props.status);
  }

  /**
   * Get incident age in hours
   */
  getAge(): number {
    return (Date.now() - this.props.timeline.detected.getTime()) / (1000 * 60 * 60);
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      id: this.id.toString(),
      title: this.props.title,
      description: this.props.description,
      severity: this.props.severity,
      category: this.props.category,
      type: this.props.type,
      status: this.props.status,
      priority: this.props.priority,
      confidence: this.props.confidence,
      relatedEventIds: this.props.relatedEventIds.map(id => id.toString()),
      relatedThreatIds: this.props.relatedThreatIds.map(id => id.toString()),
      relatedVulnerabilityIds: this.props.relatedVulnerabilityIds.map(id => id.toString()),
      affectedAssets: this.props.affectedAssets,
      timeline: this.props.timeline,
      responseTeam: this.props.responseTeam,
      investigation: this.props.investigation,
      containment: this.props.containment,
      recovery: this.props.recovery,
      lessonsLearned: this.props.lessonsLearned,
      communications: this.props.communications,
      evidence: this.props.evidence,
      impact: this.props.impact,
      compliance: this.props.compliance,
      tags: this.props.tags,
      attributes: this.props.attributes,
      analysis: {
        isCritical: this.isCritical(),
        isActive: this.isActive(),
        isResolved: this.isResolved(),
        age: this.getAge(),
      },
    };
  }
}
