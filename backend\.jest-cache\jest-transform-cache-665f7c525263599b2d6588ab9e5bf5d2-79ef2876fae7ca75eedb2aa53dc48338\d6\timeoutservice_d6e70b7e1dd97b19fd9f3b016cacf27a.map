{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\timeout.service.ts", "mappings": ";;;;;;;;;;AAAA,2CAAoD;AA2BpD;;GAEG;AAEI,IAAM,gBAAgB,wBAAtB,MAAM,gBAAgB;IAAtB;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,kBAAgB,CAAC,IAAI,CAAC,CAAC;QAC3C,YAAO,GAAG,IAAI,GAAG,EAAyB,CAAC;QAC3C,YAAO,GAAG,IAAI,GAAG,EAA0B,CAAC;QAC5C,qBAAgB,GAAG,IAAI,GAAG,EAA2B,CAAC;IAydzE,CAAC;IAvdC;;OAEG;IACH,gBAAgB,CACd,UAAkB,EAClB,YAAoB,EACpB,MAAqB;QAErB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAErC,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YAC3B,UAAU;YACV,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC;YACf,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,QAAQ;YAC1B,WAAW,EAAE,CAAC;SACf,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,wCAAwC,YAAY,aAAa,UAAU,yBAAyB,MAAM,CAAC,cAAc,IAAI,CAC9H,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CACtB,UAAkB,EAClB,SAA+C,EAC/C,aAAsB,EACtB,aAAsB;QAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,sDAAsD,UAAU,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACzD,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAE9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAExD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAClD,SAAS,EACT,OAAO,EACP,eAAe,CAAC,MAAM,EACtB,WAAW,CACZ,CAAC;YAEF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YAErD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,MAAM,SAAS,GAAG,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC;YAE1E,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,oCAAoC,UAAU,UAAU,OAAO,IAAI,CACpE,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC7B,UAAkB,EAClB,SAA+C,EAC/C,aAAsB,EACtB,aAAsB;QAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,sDAAsD,UAAU,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACzD,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAE9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAExD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAClD,SAAS,EACT,OAAO,EACP,eAAe,CAAC,MAAM,EACtB,WAAW,CACZ,CAAC;YAEF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YAErD,OAAO;gBACL,MAAM;gBACN,aAAa;gBACb,QAAQ,EAAE,KAAK;aAChB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,QAAQ,GAAG,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC;YAEnE,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;gBAEpD,OAAO;oBACL,MAAM,EAAE,SAAgB;oBACxB,aAAa;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;gBACrD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,UAAkB,EAClB,SAA+C,EAC/C,OAAe,EACf,aAAsB;QAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,sDAAsD,UAAU,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAClE,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,EAAE,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpF,MAAM,OAAO,GAAG,MAAM,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAE3D,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,mCAAmC,eAAe,aAAa,OAAO,mBAAmB,UAAU,EAAE,CACtG,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACzD,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAE9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAExD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAClD,SAAS,EACT,OAAO,EACP,eAAe,CAAC,MAAM,EACtB,WAAW,CACZ,CAAC;YAEF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YAErD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,MAAM,SAAS,GAAG,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC;YAE1E,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,8CAA8C,UAAU,aAAa,eAAe,UAAU,OAAO,IAAI,CAC1G,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,UAAkB;QACzC,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,KAAK,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9D,IAAI,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAC1C,cAAc,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;QAED,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,cAAc,mCAAmC,UAAU,EAAE,CAAC,CAAC;QAC9F,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAEnD,KAAK,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9D,UAAU,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAE9B,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,eAAe,oBAAoB,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC3B,UAAU;gBACV,eAAe,EAAE,CAAC;gBAClB,YAAY,EAAE,CAAC;gBACf,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,gBAAgB,EAAE,QAAQ;gBAC1B,WAAW,EAAE,CAAC;aACf,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QACb,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACxC,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB,EAAE,MAA8B;QACrE,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,MAAM,EAAE,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,UAAU,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,iDAAiD;QACjD,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mDAAmD,UAAU,EAAE,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,UAAkB;QACxC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAE,CAAC;YACvD,IAAI,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,4BAA4B;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CACtC,SAA+C,EAC/C,SAAiB,EACjB,MAAmB,EACnB,WAAmB;QAEnB,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxC,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;gBACpC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,6BAA6B,SAAS,IAAI,CAAC,CAAC;gBACpE,KAAK,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC5B,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,sBAAsB;YACtB,MAAM,YAAY,GAAG,GAAG,EAAE;gBACxB,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;gBACnD,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC;gBAC1B,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,YAAY,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAE/C,SAAS,CAAC,MAAM,CAAC;iBACd,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5B,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAClD,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5B,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAClD,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,UAAU,CAChB,MAAqB,EACrB,aAAsB,EACtB,aAAsB;QAEtB,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAChC,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,IAAI,aAAa,IAAI,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7D,OAAO,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,MAAM,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,UAAkB;QAC5C,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAClF,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,UAAkB,EAAE,aAAqB,EAAE,QAAiB;QAChF,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;YACvC,CAAC;YAED,mCAAmC;YACnC,OAAO,CAAC,oBAAoB;gBAC1B,CAAC,OAAO,CAAC,oBAAoB,GAAG,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;oBAC9E,OAAO,CAAC,eAAe,CAAC;YAE1B,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAC7E,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAE7E,yBAAyB;YACzB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC;YAErE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,iBAAyB,KAAK;QACvD,OAAO;YACL,cAAc;YACd,iBAAiB,EAAE,EAAE;YACrB,kBAAkB,EAAE,CAAC,cAAc,EAAE,cAAc,GAAG,GAAG,EAAE,cAAc,GAAG,CAAC,CAAC;YAC9E,gBAAgB,EAAE,IAAI;SACvB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,iBAAyB,IAAI;QACpD,OAAO;YACL,cAAc;YACd,iBAAiB,EAAE;gBACjB,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,IAAI;aACrB;YACD,kBAAkB,EAAE,CAAC,cAAc,EAAE,cAAc,GAAG,GAAG,EAAE,cAAc,GAAG,GAAG,CAAC;YAChF,gBAAgB,EAAE,IAAI;SACvB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,iBAAyB,MAAM;QAC5D,OAAO;YACL,cAAc;YACd,iBAAiB,EAAE;gBACjB,gBAAgB,EAAE,MAAM,EAAE,YAAY;gBACtC,gBAAgB,EAAE,MAAM,EAAE,YAAY;aACvC;YACD,kBAAkB,EAAE,CAAC,cAAc,EAAE,cAAc,GAAG,GAAG,EAAE,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC;YAClG,gBAAgB,EAAE,IAAI;SACvB,CAAC;IACJ,CAAC;CACF,CAAA;AA7dY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;GACA,gBAAgB,CA6d5B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\timeout.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\n\r\nexport interface TimeoutConfig {\r\n  defaultTimeout: number; // Default timeout in milliseconds\r\n  operationTimeouts: Record<string, number>; // Operation-specific timeouts\r\n  escalationTimeouts: number[]; // Escalation timeouts for retries\r\n  enableEscalation: boolean;\r\n}\r\n\r\nexport interface TimeoutMetrics {\r\n  providerId: string;\r\n  totalOperations: number;\r\n  timeoutCount: number;\r\n  averageExecutionTime: number;\r\n  maxExecutionTime: number;\r\n  minExecutionTime: number;\r\n  timeoutRate: number;\r\n  lastTimeoutTime?: Date;\r\n}\r\n\r\nexport interface TimeoutResult<T> {\r\n  result: T;\r\n  executionTime: number;\r\n  timedOut: boolean;\r\n  escalationLevel?: number;\r\n}\r\n\r\n/**\r\n * AI-specific timeout service for managing operation timeouts\r\n */\r\n@Injectable()\r\nexport class AITimeoutService {\r\n  private readonly logger = new Logger(AITimeoutService.name);\r\n  private readonly configs = new Map<string, TimeoutConfig>();\r\n  private readonly metrics = new Map<string, TimeoutMetrics>();\r\n  private readonly activeOperations = new Map<string, AbortController>();\r\n\r\n  /**\r\n   * Register timeout configuration for an AI provider\r\n   */\r\n  registerProvider(\r\n    providerId: string,\r\n    providerType: string,\r\n    config: TimeoutConfig\r\n  ): void {\r\n    this.configs.set(providerId, config);\r\n    \r\n    // Initialize metrics\r\n    this.metrics.set(providerId, {\r\n      providerId,\r\n      totalOperations: 0,\r\n      timeoutCount: 0,\r\n      averageExecutionTime: 0,\r\n      maxExecutionTime: 0,\r\n      minExecutionTime: Infinity,\r\n      timeoutRate: 0,\r\n    });\r\n\r\n    this.logger.log(\r\n      `Registered timeout configuration for ${providerType} provider ${providerId} with default timeout ${config.defaultTimeout}ms`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Execute an operation with timeout protection\r\n   */\r\n  async executeWithTimeout<T>(\r\n    providerId: string,\r\n    operation: (signal?: AbortSignal) => Promise<T>,\r\n    operationType?: string,\r\n    customTimeout?: number\r\n  ): Promise<T> {\r\n    const config = this.configs.get(providerId);\r\n    if (!config) {\r\n      throw new Error(`Timeout configuration not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    const timeout = this.getTimeout(config, operationType, customTimeout);\r\n    const operationId = this.generateOperationId(providerId);\r\n    const abortController = new AbortController();\r\n    \r\n    this.activeOperations.set(operationId, abortController);\r\n\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      const result = await this.executeWithTimeoutInternal(\r\n        operation,\r\n        timeout,\r\n        abortController.signal,\r\n        operationId\r\n      );\r\n\r\n      const executionTime = Date.now() - startTime;\r\n      this.updateMetrics(providerId, executionTime, false);\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      const executionTime = Date.now() - startTime;\r\n      const isTimeout = error instanceof Error && error.name === 'TimeoutError';\r\n      \r\n      if (isTimeout) {\r\n        this.logger.warn(\r\n          `Operation timed out for provider ${providerId} after ${timeout}ms`\r\n        );\r\n        this.updateMetrics(providerId, executionTime, true);\r\n      } else {\r\n        this.updateMetrics(providerId, executionTime, false);\r\n      }\r\n      \r\n      throw error;\r\n    } finally {\r\n      this.activeOperations.delete(operationId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute with timeout and detailed result information\r\n   */\r\n  async executeWithTimeoutDetails<T>(\r\n    providerId: string,\r\n    operation: (signal?: AbortSignal) => Promise<T>,\r\n    operationType?: string,\r\n    customTimeout?: number\r\n  ): Promise<TimeoutResult<T>> {\r\n    const config = this.configs.get(providerId);\r\n    if (!config) {\r\n      throw new Error(`Timeout configuration not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    const timeout = this.getTimeout(config, operationType, customTimeout);\r\n    const operationId = this.generateOperationId(providerId);\r\n    const abortController = new AbortController();\r\n    \r\n    this.activeOperations.set(operationId, abortController);\r\n\r\n    const startTime = Date.now();\r\n    let timedOut = false;\r\n\r\n    try {\r\n      const result = await this.executeWithTimeoutInternal(\r\n        operation,\r\n        timeout,\r\n        abortController.signal,\r\n        operationId\r\n      );\r\n\r\n      const executionTime = Date.now() - startTime;\r\n      this.updateMetrics(providerId, executionTime, false);\r\n      \r\n      return {\r\n        result,\r\n        executionTime,\r\n        timedOut: false,\r\n      };\r\n    } catch (error) {\r\n      const executionTime = Date.now() - startTime;\r\n      timedOut = error instanceof Error && error.name === 'TimeoutError';\r\n      \r\n      if (timedOut) {\r\n        this.updateMetrics(providerId, executionTime, true);\r\n        \r\n        return {\r\n          result: undefined as any,\r\n          executionTime,\r\n          timedOut: true,\r\n        };\r\n      } else {\r\n        this.updateMetrics(providerId, executionTime, false);\r\n        throw error;\r\n      }\r\n    } finally {\r\n      this.activeOperations.delete(operationId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute with escalating timeouts for retry scenarios\r\n   */\r\n  async executeWithEscalation<T>(\r\n    providerId: string,\r\n    operation: (signal?: AbortSignal) => Promise<T>,\r\n    attempt: number,\r\n    operationType?: string\r\n  ): Promise<T> {\r\n    const config = this.configs.get(providerId);\r\n    if (!config) {\r\n      throw new Error(`Timeout configuration not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    if (!config.enableEscalation || !config.escalationTimeouts.length) {\r\n      return this.executeWithTimeout(providerId, operation, operationType);\r\n    }\r\n\r\n    const escalationLevel = Math.min(attempt - 1, config.escalationTimeouts.length - 1);\r\n    const timeout = config.escalationTimeouts[escalationLevel];\r\n    \r\n    this.logger.debug(\r\n      `Executing with escalation level ${escalationLevel}, timeout ${timeout}ms for provider ${providerId}`\r\n    );\r\n\r\n    const operationId = this.generateOperationId(providerId);\r\n    const abortController = new AbortController();\r\n    \r\n    this.activeOperations.set(operationId, abortController);\r\n\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      const result = await this.executeWithTimeoutInternal(\r\n        operation,\r\n        timeout,\r\n        abortController.signal,\r\n        operationId\r\n      );\r\n\r\n      const executionTime = Date.now() - startTime;\r\n      this.updateMetrics(providerId, executionTime, false);\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      const executionTime = Date.now() - startTime;\r\n      const isTimeout = error instanceof Error && error.name === 'TimeoutError';\r\n      \r\n      if (isTimeout) {\r\n        this.logger.warn(\r\n          `Escalated operation timed out for provider ${providerId} at level ${escalationLevel} after ${timeout}ms`\r\n        );\r\n        this.updateMetrics(providerId, executionTime, true);\r\n      } else {\r\n        this.updateMetrics(providerId, executionTime, false);\r\n      }\r\n      \r\n      throw error;\r\n    } finally {\r\n      this.activeOperations.delete(operationId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel all active operations for a provider\r\n   */\r\n  cancelProviderOperations(providerId: string): number {\r\n    let cancelledCount = 0;\r\n    \r\n    for (const [operationId, controller] of this.activeOperations) {\r\n      if (operationId.startsWith(providerId)) {\r\n        controller.abort();\r\n        this.activeOperations.delete(operationId);\r\n        cancelledCount++;\r\n      }\r\n    }\r\n\r\n    if (cancelledCount > 0) {\r\n      this.logger.log(`Cancelled ${cancelledCount} active operations for provider ${providerId}`);\r\n    }\r\n\r\n    return cancelledCount;\r\n  }\r\n\r\n  /**\r\n   * Cancel all active operations\r\n   */\r\n  cancelAllOperations(): number {\r\n    const totalOperations = this.activeOperations.size;\r\n    \r\n    for (const [operationId, controller] of this.activeOperations) {\r\n      controller.abort();\r\n    }\r\n    \r\n    this.activeOperations.clear();\r\n\r\n    if (totalOperations > 0) {\r\n      this.logger.log(`Cancelled ${totalOperations} active operations`);\r\n    }\r\n\r\n    return totalOperations;\r\n  }\r\n\r\n  /**\r\n   * Get timeout metrics for a specific provider\r\n   */\r\n  getProviderMetrics(providerId: string): TimeoutMetrics | null {\r\n    return this.metrics.get(providerId) || null;\r\n  }\r\n\r\n  /**\r\n   * Get metrics for all registered providers\r\n   */\r\n  getAllProviderMetrics(): TimeoutMetrics[] {\r\n    return Array.from(this.metrics.values());\r\n  }\r\n\r\n  /**\r\n   * Reset metrics for a specific provider\r\n   */\r\n  resetProviderMetrics(providerId: string): void {\r\n    const metrics = this.metrics.get(providerId);\r\n    if (metrics) {\r\n      this.metrics.set(providerId, {\r\n        providerId,\r\n        totalOperations: 0,\r\n        timeoutCount: 0,\r\n        averageExecutionTime: 0,\r\n        maxExecutionTime: 0,\r\n        minExecutionTime: Infinity,\r\n        timeoutRate: 0,\r\n      });\r\n      this.logger.log(`Reset timeout metrics for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset metrics for all providers\r\n   */\r\n  resetAllMetrics(): void {\r\n    for (const [providerId] of this.metrics) {\r\n      this.resetProviderMetrics(providerId);\r\n    }\r\n    this.logger.log('Reset timeout metrics for all providers');\r\n  }\r\n\r\n  /**\r\n   * Update provider configuration\r\n   */\r\n  updateProviderConfig(providerId: string, config: Partial<TimeoutConfig>): void {\r\n    const existingConfig = this.configs.get(providerId);\r\n    if (existingConfig) {\r\n      const updatedConfig = { ...existingConfig, ...config };\r\n      this.configs.set(providerId, updatedConfig);\r\n      \r\n      this.logger.log(`Updated timeout configuration for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a provider's timeout configuration\r\n   */\r\n  unregisterProvider(providerId: string): void {\r\n    // Cancel any active operations for this provider\r\n    this.cancelProviderOperations(providerId);\r\n    \r\n    this.configs.delete(providerId);\r\n    this.metrics.delete(providerId);\r\n    this.logger.log(`Unregistered timeout configuration for provider ${providerId}`);\r\n  }\r\n\r\n  /**\r\n   * Get provider configuration\r\n   */\r\n  getProviderConfig(providerId: string): TimeoutConfig | null {\r\n    return this.configs.get(providerId) || null;\r\n  }\r\n\r\n  /**\r\n   * Get active operation count for a provider\r\n   */\r\n  getActiveOperationCount(providerId: string): number {\r\n    let count = 0;\r\n    for (const operationId of this.activeOperations.keys()) {\r\n      if (operationId.startsWith(providerId)) {\r\n        count++;\r\n      }\r\n    }\r\n    return count;\r\n  }\r\n\r\n  /**\r\n   * Get total active operation count\r\n   */\r\n  getTotalActiveOperationCount(): number {\r\n    return this.activeOperations.size;\r\n  }\r\n\r\n  /**\r\n   * Execute operation with timeout protection\r\n   */\r\n  private async executeWithTimeoutInternal<T>(\r\n    operation: (signal?: AbortSignal) => Promise<T>,\r\n    timeoutMs: number,\r\n    signal: AbortSignal,\r\n    operationId: string\r\n  ): Promise<T> {\r\n    return new Promise<T>((resolve, reject) => {\r\n      const timeoutHandle = setTimeout(() => {\r\n        const error = new Error(`Operation timed out after ${timeoutMs}ms`);\r\n        error.name = 'TimeoutError';\r\n        reject(error);\r\n      }, timeoutMs);\r\n\r\n      // Handle abort signal\r\n      const abortHandler = () => {\r\n        clearTimeout(timeoutHandle);\r\n        const error = new Error('Operation was cancelled');\r\n        error.name = 'AbortError';\r\n        reject(error);\r\n      };\r\n\r\n      if (signal.aborted) {\r\n        abortHandler();\r\n        return;\r\n      }\r\n\r\n      signal.addEventListener('abort', abortHandler);\r\n\r\n      operation(signal)\r\n        .then((result) => {\r\n          clearTimeout(timeoutHandle);\r\n          signal.removeEventListener('abort', abortHandler);\r\n          resolve(result);\r\n        })\r\n        .catch((error) => {\r\n          clearTimeout(timeoutHandle);\r\n          signal.removeEventListener('abort', abortHandler);\r\n          reject(error);\r\n        });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get timeout value based on configuration\r\n   */\r\n  private getTimeout(\r\n    config: TimeoutConfig,\r\n    operationType?: string,\r\n    customTimeout?: number\r\n  ): number {\r\n    if (customTimeout !== undefined) {\r\n      return customTimeout;\r\n    }\r\n\r\n    if (operationType && config.operationTimeouts[operationType]) {\r\n      return config.operationTimeouts[operationType];\r\n    }\r\n\r\n    return config.defaultTimeout;\r\n  }\r\n\r\n  /**\r\n   * Generate unique operation ID\r\n   */\r\n  private generateOperationId(providerId: string): string {\r\n    return `${providerId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  /**\r\n   * Update timeout metrics\r\n   */\r\n  private updateMetrics(providerId: string, executionTime: number, timedOut: boolean): void {\r\n    const metrics = this.metrics.get(providerId);\r\n    if (metrics) {\r\n      metrics.totalOperations++;\r\n      \r\n      if (timedOut) {\r\n        metrics.timeoutCount++;\r\n        metrics.lastTimeoutTime = new Date();\r\n      }\r\n      \r\n      // Update execution time statistics\r\n      metrics.averageExecutionTime = \r\n        (metrics.averageExecutionTime * (metrics.totalOperations - 1) + executionTime) / \r\n        metrics.totalOperations;\r\n      \r\n      metrics.maxExecutionTime = Math.max(metrics.maxExecutionTime, executionTime);\r\n      metrics.minExecutionTime = Math.min(metrics.minExecutionTime, executionTime);\r\n      \r\n      // Calculate timeout rate\r\n      metrics.timeoutRate = metrics.timeoutCount / metrics.totalOperations;\r\n      \r\n      this.metrics.set(providerId, metrics);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create predefined timeout configurations\r\n   */\r\n  static createDefaultConfig(defaultTimeout: number = 30000): TimeoutConfig {\r\n    return {\r\n      defaultTimeout,\r\n      operationTimeouts: {},\r\n      escalationTimeouts: [defaultTimeout, defaultTimeout * 1.5, defaultTimeout * 2],\r\n      enableEscalation: true,\r\n    };\r\n  }\r\n\r\n  static createQuickConfig(defaultTimeout: number = 5000): TimeoutConfig {\r\n    return {\r\n      defaultTimeout,\r\n      operationTimeouts: {\r\n        'quick-analysis': 2000,\r\n        'simple-query': 3000,\r\n      },\r\n      escalationTimeouts: [defaultTimeout, defaultTimeout * 1.2, defaultTimeout * 1.5],\r\n      enableEscalation: true,\r\n    };\r\n  }\r\n\r\n  static createLongRunningConfig(defaultTimeout: number = 120000): TimeoutConfig {\r\n    return {\r\n      defaultTimeout,\r\n      operationTimeouts: {\r\n        'model-training': 300000, // 5 minutes\r\n        'large-analysis': 180000, // 3 minutes\r\n      },\r\n      escalationTimeouts: [defaultTimeout, defaultTimeout * 1.5, defaultTimeout * 2, defaultTimeout * 3],\r\n      enableEscalation: true,\r\n    };\r\n  }\r\n}"], "version": 3}