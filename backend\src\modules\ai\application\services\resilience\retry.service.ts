import { Injectable, Logger } from '@nestjs/common';
import { RetryStrategy, RetryOptions, RetryResult } from '@/shared-kernel/patterns/retry-strategy';
import { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';
import { RateLimitException } from '@/shared-kernel/exceptions/rate-limit.exception';

export interface AIRetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
  retryableErrors: string[];
  timeoutMs?: number;
}

export interface AIRetryMetrics {
  providerId: string;
  totalAttempts: number;
  successfulRetries: number;
  failedRetries: number;
  averageAttempts: number;
  totalDelay: number;
  lastRetryTime?: Date;
}

/**
 * AI-specific retry service for managing AI provider resilience
 */
@Injectable()
export class AIRetryService {
  private readonly logger = new Logger(AIRetryService.name);
  private readonly retryStrategies = new Map<string, RetryStrategy>();
  private readonly configs = new Map<string, AIRetryConfig>();
  private readonly metrics = new Map<string, AIRetryMetrics>();

  /**
   * Register a retry strategy for an AI provider
   */
  registerProvider(
    providerId: string,
    providerType: string,
    config: AIRetryConfig
  ): void {
    const retryOptions: RetryOptions = {
      maxAttempts: config.maxAttempts,
      baseDelay: config.baseDelay,
      maxDelay: config.maxDelay,
      backoffMultiplier: config.backoffMultiplier,
      jitter: config.jitter,
      retryOn: (error: Error) => this.shouldRetry(error, config.retryableErrors),
      onRetry: (error: Error, attempt: number) => {
        this.logger.warn(
          `Retry attempt ${attempt} for ${providerType} provider ${providerId}: ${error.message}`
        );
        this.updateMetrics(providerId, attempt, false);
      },
    };

    const retryStrategy = new RetryStrategy(retryOptions);
    this.retryStrategies.set(providerId, retryStrategy);
    this.configs.set(providerId, config);
    
    // Initialize metrics
    this.metrics.set(providerId, {
      providerId,
      totalAttempts: 0,
      successfulRetries: 0,
      failedRetries: 0,
      averageAttempts: 0,
      totalDelay: 0,
    });

    this.logger.log(
      `Registered retry strategy for ${providerType} provider ${providerId} with max ${config.maxAttempts} attempts`
    );
  }

  /**
   * Execute an AI operation with retry logic
   */
  async executeWithRetry<T>(
    providerId: string,
    operation: () => Promise<T>,
    timeoutMs?: number
  ): Promise<T> {
    const retryStrategy = this.retryStrategies.get(providerId);
    if (!retryStrategy) {
      throw new Error(`Retry strategy not registered for provider: ${providerId}`);
    }

    const config = this.configs.get(providerId);
    const operationTimeout = timeoutMs || config?.timeoutMs;

    try {
      let wrappedOperation = operation;
      
      // Add timeout wrapper if specified
      if (operationTimeout) {
        wrappedOperation = () => this.withTimeout(operation(), operationTimeout);
      }

      const result = await retryStrategy.execute(wrappedOperation);
      this.updateMetrics(providerId, 1, true);
      return result;
    } catch (error) {
      this.logger.error(
        `Retry execution failed for provider ${providerId} after all attempts`,
        error
      );
      this.updateMetrics(providerId, config?.maxAttempts || 1, false);
      throw error;
    }
  }

  /**
   * Execute with detailed retry information
   */
  async executeWithRetryDetails<T>(
    providerId: string,
    operation: () => Promise<T>,
    timeoutMs?: number
  ): Promise<RetryResult<T>> {
    const retryStrategy = this.retryStrategies.get(providerId);
    if (!retryStrategy) {
      throw new Error(`Retry strategy not registered for provider: ${providerId}`);
    }

    const config = this.configs.get(providerId);
    const operationTimeout = timeoutMs || config?.timeoutMs;

    try {
      let wrappedOperation = operation;
      
      // Add timeout wrapper if specified
      if (operationTimeout) {
        wrappedOperation = () => this.withTimeout(operation(), operationTimeout);
      }

      const result = await retryStrategy.executeWithDetails(wrappedOperation);
      this.updateMetricsWithDetails(providerId, result);
      return result;
    } catch (error) {
      this.logger.error(
        `Retry execution with details failed for provider ${providerId}`,
        error
      );
      throw error;
    }
  }

  /**
   * Get retry metrics for a specific provider
   */
  getProviderMetrics(providerId: string): AIRetryMetrics | null {
    return this.metrics.get(providerId) || null;
  }

  /**
   * Get metrics for all registered providers
   */
  getAllProviderMetrics(): AIRetryMetrics[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Reset metrics for a specific provider
   */
  resetProviderMetrics(providerId: string): void {
    const metrics = this.metrics.get(providerId);
    if (metrics) {
      this.metrics.set(providerId, {
        providerId,
        totalAttempts: 0,
        successfulRetries: 0,
        failedRetries: 0,
        averageAttempts: 0,
        totalDelay: 0,
      });
      this.logger.log(`Reset retry metrics for provider ${providerId}`);
    }
  }

  /**
   * Reset metrics for all providers
   */
  resetAllMetrics(): void {
    for (const [providerId] of this.metrics) {
      this.resetProviderMetrics(providerId);
    }
    this.logger.log('Reset retry metrics for all providers');
  }

  /**
   * Update provider configuration
   */
  updateProviderConfig(providerId: string, config: Partial<AIRetryConfig>): void {
    const existingConfig = this.configs.get(providerId);
    if (existingConfig) {
      const updatedConfig = { ...existingConfig, ...config };
      this.configs.set(providerId, updatedConfig);
      
      // Re-register with new config
      const providerType = this.getProviderType(providerId);
      this.registerProvider(providerId, providerType, updatedConfig);
      
      this.logger.log(`Updated retry configuration for provider ${providerId}`);
    }
  }

  /**
   * Remove a provider's retry strategy
   */
  unregisterProvider(providerId: string): void {
    this.retryStrategies.delete(providerId);
    this.configs.delete(providerId);
    this.metrics.delete(providerId);
    this.logger.log(`Unregistered retry strategy for provider ${providerId}`);
  }

  /**
   * Get provider configuration
   */
  getProviderConfig(providerId: string): AIRetryConfig | null {
    return this.configs.get(providerId) || null;
  }

  /**
   * Check if an error should be retried based on configuration
   */
  private shouldRetry(error: Error, retryableErrors: string[]): boolean {
    // Always retry on specific exception types
    if (error instanceof ServiceUnavailableException || 
        error instanceof RateLimitException) {
      return true;
    }

    // Check if error message contains any retryable error patterns
    const errorMessage = error.message.toLowerCase();
    return retryableErrors.some(pattern => 
      errorMessage.includes(pattern.toLowerCase())
    );
  }

  /**
   * Add timeout wrapper to operation
   */
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Operation timed out after ${timeoutMs}ms`));
        }, timeoutMs);
      }),
    ]);
  }

  /**
   * Update metrics after retry attempt
   */
  private updateMetrics(providerId: string, attempts: number, success: boolean): void {
    const metrics = this.metrics.get(providerId);
    if (metrics) {
      metrics.totalAttempts += attempts;
      
      if (success) {
        metrics.successfulRetries++;
      } else {
        metrics.failedRetries++;
      }
      
      metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);
      metrics.lastRetryTime = new Date();
      
      this.metrics.set(providerId, metrics);
    }
  }

  /**
   * Update metrics with detailed retry information
   */
  private updateMetricsWithDetails(providerId: string, result: RetryResult<any>): void {
    const metrics = this.metrics.get(providerId);
    if (metrics) {
      metrics.totalAttempts += result.attempts;
      metrics.successfulRetries++;
      metrics.totalDelay += result.totalDelay;
      metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);
      metrics.lastRetryTime = new Date();
      
      this.metrics.set(providerId, metrics);
    }
  }

  /**
   * Extract provider type from provider ID
   */
  private getProviderType(providerId: string): string {
    return providerId.split('-')[0] || 'unknown';
  }

  /**
   * Create predefined retry strategies
   */
  static createExponentialBackoffConfig(
    maxAttempts: number = 3,
    baseDelay: number = 1000,
    retryableErrors: string[] = ['timeout', 'network', 'connection', 'unavailable']
  ): AIRetryConfig {
    return {
      maxAttempts,
      baseDelay,
      maxDelay: baseDelay * Math.pow(2, maxAttempts - 1),
      backoffMultiplier: 2,
      jitter: true,
      retryableErrors,
    };
  }

  static createLinearBackoffConfig(
    maxAttempts: number = 3,
    delay: number = 1000,
    retryableErrors: string[] = ['timeout', 'network', 'connection', 'unavailable']
  ): AIRetryConfig {
    return {
      maxAttempts,
      baseDelay: delay,
      maxDelay: delay,
      backoffMultiplier: 1,
      jitter: false,
      retryableErrors,
    };
  }

  static createFixedDelayConfig(
    maxAttempts: number = 3,
    delay: number = 1000,
    retryableErrors: string[] = ['timeout', 'network', 'connection', 'unavailable']
  ): AIRetryConfig {
    return {
      maxAttempts,
      baseDelay: delay,
      maxDelay: delay,
      backoffMultiplier: 1,
      jitter: false,
      retryableErrors,
    };
  }
}