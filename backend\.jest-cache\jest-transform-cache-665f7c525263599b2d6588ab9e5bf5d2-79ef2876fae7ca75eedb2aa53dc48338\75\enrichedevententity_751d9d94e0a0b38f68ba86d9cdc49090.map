{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\enriched-event.entity.ts", "mappings": ";;;AAAA,6DAA8E;AAG9E,sEAA6D;AAG7D,4EAAmE;AACnE,uGAAgG;AAChG,qHAA6G;AAC7G,2HAAmH;AAEnH;;GAEG;AACH,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IAC1B,uCAAmB,CAAA;IACnB,+CAA2B,CAAA;IAC3B,2CAAuB,CAAA;IACvB,qCAAiB,CAAA;IACjB,uCAAmB,CAAA;IACnB,uCAAmB,CAAA;AACrB,CAAC,EAPW,gBAAgB,gCAAhB,gBAAgB,QAO3B;AAkJD;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAa,aAAc,SAAQ,iCAAqC;IAOtE,YAAY,KAAyB,EAAE,EAAmB;QACxD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,KAAyB,EAAE,EAAmB;QAC1D,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEnD,+CAA+C;QAC/C,aAAa,CAAC,cAAc,CAAC,IAAI,qEAA+B,CAC9D,aAAa,CAAC,EAAE,EAChB;YACE,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;YAC1C,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,sBAAsB,EAAE,KAAK,CAAC,sBAAsB;YACpD,iBAAiB,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM;YAC5C,mBAAmB,EAAE,KAAK,CAAC,cAAc,CAAC,MAAM;YAChD,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,oBAAoB,EAAE,KAAK,CAAC,oBAAoB,IAAI,KAAK;SAC1D,CACF,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAES,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,aAAa,CAAC,2BAA2B,EAAE,CAAC;YACjF,MAAM,IAAI,KAAK,CAAC,uCAAuC,aAAa,CAAC,2BAA2B,0BAA0B,CAAC,CAAC;QAC9H,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,KAAK,SAAS;YAC/C,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,EAAE,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,SAAS;YACzC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS;YAClC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,KAAK,SAAS;YACxC,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC3B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAC7E,MAAM,IAAI,KAAK,CAAC,yBAAyB,aAAa,CAAC,qBAAqB,oBAAoB,CAAC,CAAC;QACpG,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAE3C,qCAAqC;QACrC,IAAI,CAAC,+BAA+B,EAAE,CAAC;IACzC,CAAC;IAEO,mCAAmC;QACzC,kEAAkE;QAClE,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACzE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC5D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB;gBAC/B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBACtF,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,iEAAiE;QACjE,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACjE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,+BAA+B;QACrC,qCAAqC;QACrC,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YACvD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,cAAc,CAAC,UAAU,GAAG,CAAC,IAAI,cAAc,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBACrE,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;YAC1E,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAChC,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC1E,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;oBAC1D,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,4BAA4B,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;IACtC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;IAC1C,CAAC;IAED,IAAI,YAAY;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IACtF,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IAC1C,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACrE,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACnF,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/E,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC;IAClD,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC3C,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,MAAM,UAAU,GAAU,EAAE,CAAC;QAE7B,0CAA0C;QAC1C,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YACnD,IAAI,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;gBAChC,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjD,CAAC;YACD,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;gBAC1B,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,SAAS;YAC1D,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC;IACpC,CAAC;IAED,mBAAmB;IAEnB;;OAEG;IACH,eAAe;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,WAAW,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAEzE,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,MAAwB;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC;QACrG,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,SAAS,CAAC;QAE3C,qDAAqD;QACrD,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;QAE7C,yCAAyC;QACzC,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAExC,IAAI,CAAC,cAAc,CAAC,IAAI,kFAAqC,CAC3D,IAAI,CAAC,EAAE,EACP;YACE,SAAS,EAAE,gBAAgB,CAAC,WAAW;YACvC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YACtC,MAAM;YACN,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB;YACzD,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,KAAK;SAC/D,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,KAAa,EAAE,MAAkC;QAC9D,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAEvC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG;gBAC5B,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;gBACvC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,EAAE;gBACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC;gBAChC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,IAAI,CAAC;gBACtD,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,CAAC;gBAC5C,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,CAAC;gBACpC,kBAAkB,EAAE,MAAM,CAAC,kBAAkB,IAAI,CAAC;aACnD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,wFAAwC,CAC9D,IAAI,CAAC,EAAE,EACP;YACE,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;YAC/C,KAAK;YACL,OAAO,EAAE,IAAI,CAAC,kBAAkB;YAChC,mBAAmB,EAAE,IAAI,CAAC,gCAAgC,EAAE;SAC7D,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QAC3B,IAAI,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC/F,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,CAAC;QACvD,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,SAAS,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;QAEhC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,IAAI,CAAC,gCAAgC,EAAE,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,CAAC;QACvD,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,SAAS,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,SAAS,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,SAAS,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAExC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,cAA8B;QAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,IAAI,aAAa,CAAC,2BAA2B,EAAE,CAAC;YAClF,MAAM,IAAI,KAAK,CAAC,wBAAwB,aAAa,CAAC,2BAA2B,0BAA0B,CAAC,CAAC;QAC/G,CAAC;QAED,kDAAkD;QAClD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CACjD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,CACnF,CAAC;QAEF,IAAI,YAAY,EAAE,CAAC;YACjB,uBAAuB;YACvB,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,eAAe;YACf,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,YAAiC;QAClD,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,YAAY,EAAE,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAoB;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;QACzE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAa;QAClC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC;QACpC,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAA4B;QAC7C,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,OAAO,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAA4B;QAC5C,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,OAAO,EAAE,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,OAA4B;QAC/C,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,OAA4B;QACnD,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,GAAG,OAAO,EAAE,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,MAAc,EAAE,KAAa;QAC9C,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,4BAA4B,CAAC,KAAa;QACxC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAgB;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,EAAE,CAAC;QACxD,MAAM,SAAS,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,MAAM,CAAC,CAAC;QAEhD,IAAI,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,yBAAyB,aAAa,CAAC,qBAAqB,oBAAoB,CAAC,CAAC;QACpG,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAc;QAChC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB,EAAE,KAAc;QACrD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QACjC,CAAC;IACH,CAAC;IAED,gBAAgB;IAEhB;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,SAAS,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,MAAM,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,WAAW,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,OAAO,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,CAAC,OAAO,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,CAAC,IAAI,aAAa,CAAC,4BAA4B,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,gCAAgC;QAC9B,OAAO,IAAI,CAAC,kBAAkB,IAAI,aAAa,CAAC,uBAAuB,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5D,IAAI,CAAC,wBAAwB,EAAE;YAC/B,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,CAAC,IAAI,aAAa,CAAC,0BAA0B,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3C,CAAC,yCAAgB,CAAC,uBAAuB,EAAE,yCAAgB,CAAC,KAAK;YAChE,yCAAgB,CAAC,gBAAgB,EAAE,yCAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAChF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,IAAI,IAAI,EAAE,CAAC;QAC/D,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,MAAwB;QAChD,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,IAAY;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;QAChE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAErC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IACvE,CAAC;IAED,yBAAyB;IAEjB,+BAA+B,CAAC,MAAwB;QAC9D,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,gCAAgC;QAChC,MAAM,kBAAkB,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;QAC1D,KAAK,IAAI,kBAAkB,CAAC;QAE5B,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACnD,KAAK,IAAI,eAAe,CAAC;QAEzB,0BAA0B;QAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;QAChD,KAAK,IAAI,aAAa,CAAC;QAEvB,kCAAkC;QAClC,IAAI,MAAM,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YAChC,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC;QAED,oCAAoC;QACpC,IAAI,MAAM,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QACzC,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IACxE,CAAC;IAEO,gCAAgC;QACtC,uDAAuD;QACvD,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,gDAAgD;QAChD,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,sDAAsD;QACtD,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,wCAAwC;QACxC,IAAI,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;YACtC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,UAAU;QAmBR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YAC1D,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB;YACzD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM;YACjD,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YACrD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC/C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC/C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;SAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YAC1D,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;YACtC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YAC3C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE;YACnD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,WAAW,EAAE;YAClE,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,WAAW,EAAE;YACtE,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;YACrD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;YAChD,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB;YACzD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;;AA/7BH,sCAg8BC;AA/7ByB,qCAAuB,GAAG,CAAC,CAAC;AAC5B,0CAA4B,GAAG,EAAE,CAAC;AAClC,wCAA0B,GAAG,EAAE,CAAC;AAChC,mCAAqB,GAAG,EAAE,CAAC;AAC3B,yCAA2B,GAAG,EAAE,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\enriched-event.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventMetadata } from '../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { EnrichmentSource } from '../enums/enrichment-source.enum';\r\nimport { EnrichedEventCreatedDomainEvent } from '../events/enriched-event-created.domain-event';\r\nimport { EnrichedEventStatusChangedDomainEvent } from '../events/enriched-event-status-changed.domain-event';\r\nimport { EnrichedEventEnrichmentFailedDomainEvent } from '../events/enriched-event-enrichment-failed.domain-event';\r\n\r\n/**\r\n * Enrichment Status Enum\r\n */\r\nexport enum EnrichmentStatus {\r\n  PENDING = 'PENDING',\r\n  IN_PROGRESS = 'IN_PROGRESS',\r\n  COMPLETED = 'COMPLETED',\r\n  FAILED = 'FAILED',\r\n  PARTIAL = 'PARTIAL',\r\n  SKIPPED = 'SKIPPED',\r\n}\r\n\r\n/**\r\n * Enrichment Rule Interface\r\n */\r\nexport interface EnrichmentRule {\r\n  /** Rule identifier */\r\n  id: string;\r\n  /** Rule name */\r\n  name: string;\r\n  /** Rule description */\r\n  description: string;\r\n  /** Rule priority (higher number = higher priority) */\r\n  priority: number;\r\n  /** Whether the rule is required for successful enrichment */\r\n  required: boolean;\r\n  /** Enrichment sources used by this rule */\r\n  sources: EnrichmentSource[];\r\n  /** Rule configuration */\r\n  config?: Record<string, any>;\r\n  /** Timeout for rule execution in milliseconds */\r\n  timeoutMs?: number;\r\n}\r\n\r\n/**\r\n * Enrichment Data Interface\r\n */\r\nexport interface EnrichmentData {\r\n  /** Source of the enrichment data */\r\n  source: EnrichmentSource;\r\n  /** Type of enrichment data */\r\n  type: string;\r\n  /** The enrichment data itself */\r\n  data: Record<string, any>;\r\n  /** Confidence score of the enrichment (0-100) */\r\n  confidence: number;\r\n  /** When the enrichment was obtained */\r\n  timestamp: Date;\r\n  /** TTL for the enrichment data in seconds */\r\n  ttl?: number;\r\n  /** Metadata about the enrichment */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Enrichment Result Interface\r\n */\r\nexport interface EnrichmentResult {\r\n  /** Whether enrichment was successful */\r\n  success: boolean;\r\n  /** Applied enrichment rules */\r\n  appliedRules: string[];\r\n  /** Failed enrichment rules */\r\n  failedRules: string[];\r\n  /** Enrichment warnings */\r\n  warnings: string[];\r\n  /** Enrichment errors */\r\n  errors: string[];\r\n  /** Processing duration in milliseconds */\r\n  processingDurationMs: number;\r\n  /** Overall confidence score of enrichment (0-100) */\r\n  confidenceScore: number;\r\n  /** Number of enrichment sources used */\r\n  sourcesUsed: number;\r\n  /** Total data points enriched */\r\n  dataPointsEnriched: number;\r\n}\r\n\r\n/**\r\n * EnrichedEvent Entity Properties\r\n */\r\nexport interface EnrichedEventProps {\r\n  /** Original normalized event ID that was enriched */\r\n  normalizedEventId: UniqueEntityId;\r\n  /** Event metadata containing timestamp, source, and processing information */\r\n  metadata: EventMetadata;\r\n  /** Type of the security event */\r\n  type: EventType;\r\n  /** Severity level of the event */\r\n  severity: EventSeverity;\r\n  /** Current status of the event */\r\n  status: EventStatus;\r\n  /** Current processing status in the pipeline */\r\n  processingStatus: EventProcessingStatus;\r\n  /** Current enrichment status */\r\n  enrichmentStatus: EnrichmentStatus;\r\n  /** Original normalized event data */\r\n  normalizedData: Record<string, any>;\r\n  /** Enriched event data with additional context */\r\n  enrichedData: Record<string, any>;\r\n  /** Event title/summary */\r\n  title: string;\r\n  /** Detailed description of the event */\r\n  description?: string;\r\n  /** Tags for categorization and filtering */\r\n  tags?: string[];\r\n  /** Risk score (0-100) */\r\n  riskScore?: number;\r\n  /** Confidence level (0-100) */\r\n  confidenceLevel?: number;\r\n  /** Additional custom attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Event correlation ID for grouping related events */\r\n  correlationId?: string;\r\n  /** Parent event ID if this is a child event */\r\n  parentEventId?: UniqueEntityId;\r\n  /** Applied enrichment rules */\r\n  appliedRules: EnrichmentRule[];\r\n  /** Enrichment data from various sources */\r\n  enrichmentData: EnrichmentData[];\r\n  /** Enrichment result details */\r\n  enrichmentResult?: EnrichmentResult;\r\n  /** When enrichment started */\r\n  enrichmentStartedAt?: Date;\r\n  /** When enrichment completed */\r\n  enrichmentCompletedAt?: Date;\r\n  /** Enrichment processing attempts */\r\n  enrichmentAttempts?: number;\r\n  /** Last enrichment error */\r\n  lastEnrichmentError?: string;\r\n  /** Threat intelligence score (0-100) */\r\n  threatIntelScore?: number;\r\n  /** Asset context information */\r\n  assetContext?: Record<string, any>;\r\n  /** User context information */\r\n  userContext?: Record<string, any>;\r\n  /** Network context information */\r\n  networkContext?: Record<string, any>;\r\n  /** Geolocation context */\r\n  geolocationContext?: Record<string, any>;\r\n  /** Reputation scores from various sources */\r\n  reputationScores?: Record<string, number>;\r\n  /** Whether the enriched event requires manual review */\r\n  requiresManualReview?: boolean;\r\n  /** Manual review notes */\r\n  reviewNotes?: string;\r\n  /** Who reviewed the event */\r\n  reviewedBy?: string;\r\n  /** When the event was reviewed */\r\n  reviewedAt?: Date;\r\n  /** Enrichment quality score (0-100) */\r\n  enrichmentQualityScore?: number;\r\n  /** Validation errors found during enrichment */\r\n  validationErrors?: string[];\r\n}\r\n\r\n/**\r\n * EnrichedEvent Entity\r\n * \r\n * Represents a security event that has been processed through the enrichment pipeline.\r\n * Enriched events have additional context from threat intelligence, asset management,\r\n * user directories, and other external sources.\r\n * \r\n * Key responsibilities:\r\n * - Maintain enriched event state and lifecycle\r\n * - Enforce enrichment business rules and data quality standards\r\n * - Track enrichment process and applied rules\r\n * - Generate domain events for enrichment state changes\r\n * - Support threat intelligence integration and context building\r\n * - Manage manual review workflow for complex enrichments\r\n * \r\n * Business Rules:\r\n * - Enriched events must reference a valid normalized event\r\n * - Enrichment data must include source attribution and confidence scores\r\n * - Threat intelligence scores must be calculated based on multiple sources\r\n * - High-risk enrichments may require manual review before processing\r\n * - Enrichment attempts are tracked and limited\r\n * - Failed enrichment must preserve data integrity and provide fallback\r\n */\r\nexport class EnrichedEvent extends BaseAggregateRoot<EnrichedEventProps> {\r\n  private static readonly MAX_ENRICHMENT_ATTEMPTS = 3;\r\n  private static readonly MIN_ENRICHMENT_QUALITY_SCORE = 70;\r\n  private static readonly HIGH_RISK_REVIEW_THRESHOLD = 85;\r\n  private static readonly MAX_VALIDATION_ERRORS = 10;\r\n  private static readonly MAX_ENRICHMENT_DATA_SOURCES = 50;\r\n\r\n  constructor(props: EnrichedEventProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Create a new EnrichedEvent\r\n   */\r\n  static create(props: EnrichedEventProps, id?: UniqueEntityId): EnrichedEvent {\r\n    const enrichedEvent = new EnrichedEvent(props, id);\r\n    \r\n    // Add domain event for enriched event creation\r\n    enrichedEvent.addDomainEvent(new EnrichedEventCreatedDomainEvent(\r\n      enrichedEvent.id,\r\n      {\r\n        normalizedEventId: props.normalizedEventId,\r\n        eventType: props.type,\r\n        severity: props.severity,\r\n        enrichmentStatus: props.enrichmentStatus,\r\n        enrichmentQualityScore: props.enrichmentQualityScore,\r\n        appliedRulesCount: props.appliedRules.length,\r\n        enrichmentDataCount: props.enrichmentData.length,\r\n        threatIntelScore: props.threatIntelScore,\r\n        requiresManualReview: props.requiresManualReview || false,\r\n      }\r\n    ));\r\n\r\n    return enrichedEvent;\r\n  }\r\n\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n\r\n    if (!this.props.normalizedEventId) {\r\n      throw new Error('EnrichedEvent must reference a normalized event');\r\n    }\r\n\r\n    if (!this.props.metadata) {\r\n      throw new Error('EnrichedEvent must have metadata');\r\n    }\r\n\r\n    if (!this.props.type) {\r\n      throw new Error('EnrichedEvent must have a type');\r\n    }\r\n\r\n    if (!this.props.severity) {\r\n      throw new Error('EnrichedEvent must have a severity');\r\n    }\r\n\r\n    if (!this.props.status) {\r\n      throw new Error('EnrichedEvent must have a status');\r\n    }\r\n\r\n    if (!this.props.processingStatus) {\r\n      throw new Error('EnrichedEvent must have a processing status');\r\n    }\r\n\r\n    if (!this.props.enrichmentStatus) {\r\n      throw new Error('EnrichedEvent must have an enrichment status');\r\n    }\r\n\r\n    if (!this.props.normalizedData) {\r\n      throw new Error('EnrichedEvent must have normalized data');\r\n    }\r\n\r\n    if (!this.props.enrichedData) {\r\n      throw new Error('EnrichedEvent must have enriched data');\r\n    }\r\n\r\n    if (!this.props.title || this.props.title.trim().length === 0) {\r\n      throw new Error('EnrichedEvent must have a non-empty title');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.appliedRules)) {\r\n      throw new Error('EnrichedEvent must have applied rules array');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.enrichmentData)) {\r\n      throw new Error('EnrichedEvent must have enrichment data array');\r\n    }\r\n\r\n    if (this.props.enrichmentData.length > EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES) {\r\n      throw new Error(`EnrichedEvent cannot have more than ${EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES} enrichment data sources`);\r\n    }\r\n\r\n    if (this.props.enrichmentQualityScore !== undefined && \r\n        (this.props.enrichmentQualityScore < 0 || this.props.enrichmentQualityScore > 100)) {\r\n      throw new Error('Enrichment quality score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.threatIntelScore !== undefined && \r\n        (this.props.threatIntelScore < 0 || this.props.threatIntelScore > 100)) {\r\n      throw new Error('Threat intelligence score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.riskScore !== undefined && \r\n        (this.props.riskScore < 0 || this.props.riskScore > 100)) {\r\n      throw new Error('Risk score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.confidenceLevel !== undefined && \r\n        (this.props.confidenceLevel < 0 || this.props.confidenceLevel > 100)) {\r\n      throw new Error('Confidence level must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.enrichmentAttempts !== undefined && this.props.enrichmentAttempts < 0) {\r\n      throw new Error('Enrichment attempts cannot be negative');\r\n    }\r\n\r\n    if (this.props.validationErrors && \r\n        this.props.validationErrors.length > EnrichedEvent.MAX_VALIDATION_ERRORS) {\r\n      throw new Error(`Cannot have more than ${EnrichedEvent.MAX_VALIDATION_ERRORS} validation errors`);\r\n    }\r\n\r\n    // Validate enrichment status consistency\r\n    this.validateEnrichmentStatusConsistency();\r\n\r\n    // Validate enrichment data integrity\r\n    this.validateEnrichmentDataIntegrity();\r\n  }\r\n\r\n  private validateEnrichmentStatusConsistency(): void {\r\n    // If enrichment is completed, it should have completion timestamp\r\n    if (this.props.enrichmentStatus === EnrichmentStatus.COMPLETED) {\r\n      if (!this.props.enrichmentCompletedAt) {\r\n        throw new Error('Completed enrichment must have completion timestamp');\r\n      }\r\n      if (!this.props.enrichmentResult) {\r\n        throw new Error('Completed enrichment must have result');\r\n      }\r\n    }\r\n\r\n    // If enrichment failed, it should have error information\r\n    if (this.props.enrichmentStatus === EnrichmentStatus.FAILED) {\r\n      if (!this.props.lastEnrichmentError && \r\n          (!this.props.enrichmentResult || this.props.enrichmentResult.errors.length === 0)) {\r\n        throw new Error('Failed enrichment must have error information');\r\n      }\r\n    }\r\n\r\n    // If enrichment is in progress, it should have started timestamp\r\n    if (this.props.enrichmentStatus === EnrichmentStatus.IN_PROGRESS) {\r\n      if (!this.props.enrichmentStartedAt) {\r\n        throw new Error('In-progress enrichment must have start timestamp');\r\n      }\r\n    }\r\n\r\n    // Manual review consistency\r\n    if (this.props.requiresManualReview && this.props.reviewedAt) {\r\n      if (!this.props.reviewedBy) {\r\n        throw new Error('Reviewed events must have reviewer information');\r\n      }\r\n    }\r\n  }\r\n\r\n  private validateEnrichmentDataIntegrity(): void {\r\n    // Validate enrichment data structure\r\n    for (const enrichmentData of this.props.enrichmentData) {\r\n      if (!enrichmentData.source) {\r\n        throw new Error('Enrichment data must have a source');\r\n      }\r\n      if (!enrichmentData.type) {\r\n        throw new Error('Enrichment data must have a type');\r\n      }\r\n      if (!enrichmentData.data) {\r\n        throw new Error('Enrichment data must have data');\r\n      }\r\n      if (enrichmentData.confidence < 0 || enrichmentData.confidence > 100) {\r\n        throw new Error('Enrichment data confidence must be between 0 and 100');\r\n      }\r\n      if (!enrichmentData.timestamp) {\r\n        throw new Error('Enrichment data must have a timestamp');\r\n      }\r\n    }\r\n\r\n    // Validate reputation scores\r\n    if (this.props.reputationScores) {\r\n      for (const [source, score] of Object.entries(this.props.reputationScores)) {\r\n        if (typeof score !== 'number' || score < 0 || score > 100) {\r\n          throw new Error(`Reputation score for ${source} must be between 0 and 100`);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  get normalizedEventId(): UniqueEntityId {\r\n    return this.props.normalizedEventId;\r\n  }\r\n\r\n  get metadata(): EventMetadata {\r\n    return this.props.metadata;\r\n  }\r\n\r\n  get type(): EventType {\r\n    return this.props.type;\r\n  }\r\n\r\n  get severity(): EventSeverity {\r\n    return this.props.severity;\r\n  }\r\n\r\n  get status(): EventStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  get processingStatus(): EventProcessingStatus {\r\n    return this.props.processingStatus;\r\n  }\r\n\r\n  get enrichmentStatus(): EnrichmentStatus {\r\n    return this.props.enrichmentStatus;\r\n  }\r\n\r\n  get normalizedData(): Record<string, any> {\r\n    return { ...this.props.normalizedData };\r\n  }\r\n\r\n  get enrichedData(): Record<string, any> {\r\n    return { ...this.props.enrichedData };\r\n  }\r\n\r\n  get title(): string {\r\n    return this.props.title;\r\n  }\r\n\r\n  get description(): string | undefined {\r\n    return this.props.description;\r\n  }\r\n\r\n  get tags(): string[] {\r\n    return this.props.tags ? [...this.props.tags] : [];\r\n  }\r\n\r\n  get riskScore(): number | undefined {\r\n    return this.props.riskScore;\r\n  }\r\n\r\n  get confidenceLevel(): number | undefined {\r\n    return this.props.confidenceLevel;\r\n  }\r\n\r\n  get attributes(): Record<string, any> {\r\n    return this.props.attributes ? { ...this.props.attributes } : {};\r\n  }\r\n\r\n  get correlationId(): string | undefined {\r\n    return this.props.correlationId;\r\n  }\r\n\r\n  get parentEventId(): UniqueEntityId | undefined {\r\n    return this.props.parentEventId;\r\n  }\r\n\r\n  get appliedRules(): EnrichmentRule[] {\r\n    return [...this.props.appliedRules];\r\n  }\r\n\r\n  get enrichmentData(): EnrichmentData[] {\r\n    return [...this.props.enrichmentData];\r\n  }\r\n\r\n  get enrichmentResult(): EnrichmentResult | undefined {\r\n    return this.props.enrichmentResult ? { ...this.props.enrichmentResult } : undefined;\r\n  }\r\n\r\n  get enrichmentStartedAt(): Date | undefined {\r\n    return this.props.enrichmentStartedAt;\r\n  }\r\n\r\n  get enrichmentCompletedAt(): Date | undefined {\r\n    return this.props.enrichmentCompletedAt;\r\n  }\r\n\r\n  get enrichmentAttempts(): number {\r\n    return this.props.enrichmentAttempts || 0;\r\n  }\r\n\r\n  get lastEnrichmentError(): string | undefined {\r\n    return this.props.lastEnrichmentError;\r\n  }\r\n\r\n  get threatIntelScore(): number | undefined {\r\n    return this.props.threatIntelScore;\r\n  }\r\n\r\n  get assetContext(): Record<string, any> {\r\n    return this.props.assetContext ? { ...this.props.assetContext } : {};\r\n  }\r\n\r\n  get userContext(): Record<string, any> {\r\n    return this.props.userContext ? { ...this.props.userContext } : {};\r\n  }\r\n\r\n  get networkContext(): Record<string, any> {\r\n    return this.props.networkContext ? { ...this.props.networkContext } : {};\r\n  }\r\n\r\n  get geolocationContext(): Record<string, any> {\r\n    return this.props.geolocationContext ? { ...this.props.geolocationContext } : {};\r\n  }\r\n\r\n  get reputationScores(): Record<string, number> {\r\n    return this.props.reputationScores ? { ...this.props.reputationScores } : {};\r\n  }\r\n\r\n  get requiresManualReview(): boolean {\r\n    return this.props.requiresManualReview || false;\r\n  }\r\n\r\n  get reviewNotes(): string | undefined {\r\n    return this.props.reviewNotes;\r\n  }\r\n\r\n  get reviewedBy(): string | undefined {\r\n    return this.props.reviewedBy;\r\n  }\r\n\r\n  get reviewedAt(): Date | undefined {\r\n    return this.props.reviewedAt;\r\n  }\r\n\r\n  get enrichmentQualityScore(): number | undefined {\r\n    return this.props.enrichmentQualityScore;\r\n  }\r\n\r\n  get validationErrors(): string[] {\r\n    return this.props.validationErrors ? [...this.props.validationErrors] : [];\r\n  }\r\n\r\n  /**\r\n   * Get the entity ID (inherited from BaseAggregateRoot)\r\n   */\r\n  get id(): UniqueEntityId {\r\n    return this._id;\r\n  }\r\n\r\n  /**\r\n   * Get the creation timestamp from metadata\r\n   */\r\n  get createdAt(): Date | undefined {\r\n    return this.props.metadata?.timestamp;\r\n  }\r\n\r\n  /**\r\n   * Get threat indicators from enrichment data\r\n   */\r\n  get indicators(): any[] {\r\n    const indicators: any[] = [];\r\n\r\n    // Extract indicators from enrichment data\r\n    for (const enrichment of this.props.enrichmentData) {\r\n      if (enrichment.data?.indicators) {\r\n        indicators.push(...enrichment.data.indicators);\r\n      }\r\n      if (enrichment.data?.iocs) {\r\n        indicators.push(...enrichment.data.iocs);\r\n      }\r\n    }\r\n\r\n    return indicators;\r\n  }\r\n\r\n  /**\r\n   * Get enrichment score for correlation readiness\r\n   */\r\n  get enrichmentScore(): number {\r\n    return this.props.enrichmentQualityScore || this.props.confidenceLevel || 0;\r\n  }\r\n\r\n  /**\r\n   * Check if event is ready for correlation\r\n   */\r\n  isReadyForCorrelation(): boolean {\r\n    return this.props.enrichmentStatus === EnrichmentStatus.COMPLETED &&\r\n           this.enrichmentScore >= 50;\r\n  }\r\n\r\n  // Business methods\r\n\r\n  /**\r\n   * Start enrichment process\r\n   */\r\n  startEnrichment(): void {\r\n    if (this.props.enrichmentStatus !== EnrichmentStatus.PENDING) {\r\n      throw new Error('Can only start enrichment for pending events');\r\n    }\r\n\r\n    this.props.enrichmentStatus = EnrichmentStatus.IN_PROGRESS;\r\n    this.props.enrichmentStartedAt = new Date();\r\n    this.props.enrichmentAttempts = (this.props.enrichmentAttempts || 0) + 1;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Complete enrichment process\r\n   */\r\n  completeEnrichment(result: EnrichmentResult): void {\r\n    if (this.props.enrichmentStatus !== EnrichmentStatus.IN_PROGRESS) {\r\n      throw new Error('Can only complete enrichment for in-progress events');\r\n    }\r\n\r\n    this.props.enrichmentStatus = result.success ? EnrichmentStatus.COMPLETED : EnrichmentStatus.PARTIAL;\r\n    this.props.enrichmentCompletedAt = new Date();\r\n    this.props.enrichmentResult = result;\r\n    this.props.lastEnrichmentError = undefined;\r\n\r\n    // Calculate enrichment quality score based on result\r\n    this.calculateEnrichmentQualityScore(result);\r\n\r\n    // Determine if manual review is required\r\n    this.determineManualReviewRequirement();\r\n\r\n    this.addDomainEvent(new EnrichedEventStatusChangedDomainEvent(\r\n      this.id,\r\n      {\r\n        oldStatus: EnrichmentStatus.IN_PROGRESS,\r\n        newStatus: this.props.enrichmentStatus,\r\n        result,\r\n        enrichmentQualityScore: this.props.enrichmentQualityScore,\r\n        requiresManualReview: this.props.requiresManualReview || false,\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Fail enrichment process\r\n   */\r\n  failEnrichment(error: string, result?: Partial<EnrichmentResult>): void {\r\n    if (this.props.enrichmentStatus !== EnrichmentStatus.IN_PROGRESS) {\r\n      throw new Error('Can only fail enrichment for in-progress events');\r\n    }\r\n\r\n    this.props.enrichmentStatus = EnrichmentStatus.FAILED;\r\n    this.props.lastEnrichmentError = error;\r\n    \r\n    if (result) {\r\n      this.props.enrichmentResult = {\r\n        success: false,\r\n        appliedRules: result.appliedRules || [],\r\n        failedRules: result.failedRules || [],\r\n        warnings: result.warnings || [],\r\n        errors: result.errors || [error],\r\n        processingDurationMs: result.processingDurationMs || 0,\r\n        confidenceScore: result.confidenceScore || 0,\r\n        sourcesUsed: result.sourcesUsed || 0,\r\n        dataPointsEnriched: result.dataPointsEnriched || 0,\r\n      };\r\n    }\r\n\r\n    this.addDomainEvent(new EnrichedEventEnrichmentFailedDomainEvent(\r\n      this.id,\r\n      {\r\n        normalizedEventId: this.props.normalizedEventId,\r\n        error,\r\n        attempt: this.enrichmentAttempts,\r\n        maxAttemptsExceeded: this.hasExceededMaxEnrichmentAttempts(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Skip enrichment process\r\n   */\r\n  skipEnrichment(reason: string): void {\r\n    if (![EnrichmentStatus.PENDING, EnrichmentStatus.FAILED].includes(this.props.enrichmentStatus)) {\r\n      throw new Error('Can only skip enrichment for pending or failed events');\r\n    }\r\n\r\n    this.props.enrichmentStatus = EnrichmentStatus.SKIPPED;\r\n    this.props.lastEnrichmentError = undefined;\r\n    this.props.reviewNotes = reason;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Reset enrichment for retry\r\n   */\r\n  resetEnrichment(): void {\r\n    if (this.hasExceededMaxEnrichmentAttempts()) {\r\n      throw new Error('Cannot reset enrichment: maximum attempts exceeded');\r\n    }\r\n\r\n    this.props.enrichmentStatus = EnrichmentStatus.PENDING;\r\n    this.props.enrichmentStartedAt = undefined;\r\n    this.props.enrichmentCompletedAt = undefined;\r\n    this.props.lastEnrichmentError = undefined;\r\n    this.props.enrichmentResult = undefined;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Add enrichment data\r\n   */\r\n  addEnrichmentData(enrichmentData: EnrichmentData): void {\r\n    if (this.props.enrichmentData.length >= EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES) {\r\n      throw new Error(`Cannot add more than ${EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES} enrichment data sources`);\r\n    }\r\n\r\n    // Check for duplicate source and type combination\r\n    const existingData = this.props.enrichmentData.find(\r\n      data => data.source === enrichmentData.source && data.type === enrichmentData.type\r\n    );\r\n\r\n    if (existingData) {\r\n      // Update existing data\r\n      Object.assign(existingData, enrichmentData);\r\n    } else {\r\n      // Add new data\r\n      this.props.enrichmentData.push(enrichmentData);\r\n    }\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Update enriched data\r\n   */\r\n  updateEnrichedData(enrichedData: Record<string, any>): void {\r\n    this.props.enrichedData = { ...this.props.enrichedData, ...enrichedData };\r\n  }\r\n\r\n  /**\r\n   * Add applied enrichment rule\r\n   */\r\n  addAppliedRule(rule: EnrichmentRule): void {\r\n    const existingRule = this.props.appliedRules.find(r => r.id === rule.id);\r\n    if (!existingRule) {\r\n      this.props.appliedRules.push(rule);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update threat intelligence score\r\n   */\r\n  updateThreatIntelScore(score: number): void {\r\n    if (score < 0 || score > 100) {\r\n      throw new Error('Threat intelligence score must be between 0 and 100');\r\n    }\r\n\r\n    this.props.threatIntelScore = score;\r\n    this.determineManualReviewRequirement();\r\n  }\r\n\r\n  /**\r\n   * Update asset context\r\n   */\r\n  updateAssetContext(context: Record<string, any>): void {\r\n    this.props.assetContext = { ...this.props.assetContext, ...context };\r\n  }\r\n\r\n  /**\r\n   * Update user context\r\n   */\r\n  updateUserContext(context: Record<string, any>): void {\r\n    this.props.userContext = { ...this.props.userContext, ...context };\r\n  }\r\n\r\n  /**\r\n   * Update network context\r\n   */\r\n  updateNetworkContext(context: Record<string, any>): void {\r\n    this.props.networkContext = { ...this.props.networkContext, ...context };\r\n  }\r\n\r\n  /**\r\n   * Update geolocation context\r\n   */\r\n  updateGeolocationContext(context: Record<string, any>): void {\r\n    this.props.geolocationContext = { ...this.props.geolocationContext, ...context };\r\n  }\r\n\r\n  /**\r\n   * Add reputation score\r\n   */\r\n  addReputationScore(source: string, score: number): void {\r\n    if (score < 0 || score > 100) {\r\n      throw new Error('Reputation score must be between 0 and 100');\r\n    }\r\n\r\n    if (!this.props.reputationScores) {\r\n      this.props.reputationScores = {};\r\n    }\r\n\r\n    this.props.reputationScores[source] = score;\r\n  }\r\n\r\n  /**\r\n   * Update enrichment quality score\r\n   */\r\n  updateEnrichmentQualityScore(score: number): void {\r\n    if (score < 0 || score > 100) {\r\n      throw new Error('Enrichment quality score must be between 0 and 100');\r\n    }\r\n\r\n    this.props.enrichmentQualityScore = score;\r\n    this.determineManualReviewRequirement();\r\n  }\r\n\r\n  /**\r\n   * Add validation errors\r\n   */\r\n  addValidationErrors(errors: string[]): void {\r\n    const currentErrors = this.props.validationErrors || [];\r\n    const newErrors = [...currentErrors, ...errors];\r\n\r\n    if (newErrors.length > EnrichedEvent.MAX_VALIDATION_ERRORS) {\r\n      throw new Error(`Cannot have more than ${EnrichedEvent.MAX_VALIDATION_ERRORS} validation errors`);\r\n    }\r\n\r\n    this.props.validationErrors = newErrors;\r\n  }\r\n\r\n  /**\r\n   * Clear validation errors\r\n   */\r\n  clearValidationErrors(): void {\r\n    this.props.validationErrors = [];\r\n  }\r\n\r\n  /**\r\n   * Mark for manual review\r\n   */\r\n  markForManualReview(reason: string): void {\r\n    this.props.requiresManualReview = true;\r\n    this.props.reviewNotes = reason;\r\n  }\r\n\r\n  /**\r\n   * Complete manual review\r\n   */\r\n  completeManualReview(reviewedBy: string, notes?: string): void {\r\n    if (!this.props.requiresManualReview) {\r\n      throw new Error('Event is not marked for manual review');\r\n    }\r\n\r\n    this.props.reviewedBy = reviewedBy;\r\n    this.props.reviewedAt = new Date();\r\n    if (notes) {\r\n      this.props.reviewNotes = notes;\r\n    }\r\n  }\r\n\r\n  // Query methods\r\n\r\n  /**\r\n   * Check if enrichment is completed\r\n   */\r\n  isEnrichmentCompleted(): boolean {\r\n    return this.props.enrichmentStatus === EnrichmentStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment failed\r\n   */\r\n  isEnrichmentFailed(): boolean {\r\n    return this.props.enrichmentStatus === EnrichmentStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment is in progress\r\n   */\r\n  isEnrichmentInProgress(): boolean {\r\n    return this.props.enrichmentStatus === EnrichmentStatus.IN_PROGRESS;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment was skipped\r\n   */\r\n  isEnrichmentSkipped(): boolean {\r\n    return this.props.enrichmentStatus === EnrichmentStatus.SKIPPED;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment is partial\r\n   */\r\n  isEnrichmentPartial(): boolean {\r\n    return this.props.enrichmentStatus === EnrichmentStatus.PARTIAL;\r\n  }\r\n\r\n  /**\r\n   * Check if event has high enrichment quality\r\n   */\r\n  hasHighEnrichmentQuality(): boolean {\r\n    return (this.props.enrichmentQualityScore || 0) >= EnrichedEvent.MIN_ENRICHMENT_QUALITY_SCORE;\r\n  }\r\n\r\n  /**\r\n   * Check if event has validation errors\r\n   */\r\n  hasValidationErrors(): boolean {\r\n    return (this.props.validationErrors?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if event has exceeded max enrichment attempts\r\n   */\r\n  hasExceededMaxEnrichmentAttempts(): boolean {\r\n    return this.enrichmentAttempts >= EnrichedEvent.MAX_ENRICHMENT_ATTEMPTS;\r\n  }\r\n\r\n  /**\r\n   * Check if event is ready for next processing stage\r\n   */\r\n  isReadyForNextStage(): boolean {\r\n    return (this.isEnrichmentCompleted() || this.isEnrichmentPartial()) && \r\n           this.hasHighEnrichmentQuality() && \r\n           !this.hasValidationErrors() &&\r\n           (!this.requiresManualReview || this.reviewedAt !== undefined);\r\n  }\r\n\r\n  /**\r\n   * Check if event is high risk based on threat intelligence\r\n   */\r\n  isHighThreatRisk(): boolean {\r\n    return (this.props.threatIntelScore || 0) >= EnrichedEvent.HIGH_RISK_REVIEW_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Check if event has threat intelligence data\r\n   */\r\n  hasThreatIntelligence(): boolean {\r\n    return this.props.enrichmentData.some(data => \r\n      [EnrichmentSource.COMMERCIAL_THREAT_INTEL, EnrichmentSource.OSINT, \r\n       EnrichmentSource.GOVERNMENT_INTEL, EnrichmentSource.TIP].includes(data.source)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if event has reputation data\r\n   */\r\n  hasReputationData(): boolean {\r\n    return Object.keys(this.props.reputationScores || {}).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if event has geolocation data\r\n   */\r\n  hasGeolocationData(): boolean {\r\n    return Object.keys(this.props.geolocationContext || {}).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Get enrichment duration\r\n   */\r\n  getEnrichmentDuration(): number | null {\r\n    if (!this.props.enrichmentStartedAt) {\r\n      return null;\r\n    }\r\n\r\n    const endTime = this.props.enrichmentCompletedAt || new Date();\r\n    return endTime.getTime() - this.props.enrichmentStartedAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get applied rule names\r\n   */\r\n  getAppliedRuleNames(): string[] {\r\n    return this.props.appliedRules.map(rule => rule.name);\r\n  }\r\n\r\n  /**\r\n   * Check if specific rule was applied\r\n   */\r\n  hasAppliedRule(ruleId: string): boolean {\r\n    return this.props.appliedRules.some(rule => rule.id === ruleId);\r\n  }\r\n\r\n  /**\r\n   * Get enrichment data by source\r\n   */\r\n  getEnrichmentDataBySource(source: EnrichmentSource): EnrichmentData[] {\r\n    return this.props.enrichmentData.filter(data => data.source === source);\r\n  }\r\n\r\n  /**\r\n   * Get enrichment data by type\r\n   */\r\n  getEnrichmentDataByType(type: string): EnrichmentData[] {\r\n    return this.props.enrichmentData.filter(data => data.type === type);\r\n  }\r\n\r\n  /**\r\n   * Get average reputation score\r\n   */\r\n  getAverageReputationScore(): number | null {\r\n    const scores = Object.values(this.props.reputationScores || {});\r\n    if (scores.length === 0) return null;\r\n    \r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private calculateEnrichmentQualityScore(result: EnrichmentResult): void {\r\n    let score = 100;\r\n\r\n    // Reduce score for failed rules\r\n    const failedRulesPenalty = result.failedRules.length * 15;\r\n    score -= failedRulesPenalty;\r\n\r\n    // Reduce score for warnings\r\n    const warningsPenalty = result.warnings.length * 5;\r\n    score -= warningsPenalty;\r\n\r\n    // Reduce score for errors\r\n    const errorsPenalty = result.errors.length * 20;\r\n    score -= errorsPenalty;\r\n\r\n    // Reduce score for low confidence\r\n    if (result.confidenceScore < 70) {\r\n      score -= (70 - result.confidenceScore);\r\n    }\r\n\r\n    // Reduce score for low source usage\r\n    if (result.sourcesUsed < 3) {\r\n      score -= (3 - result.sourcesUsed) * 10;\r\n    }\r\n\r\n    // Ensure score is within valid range\r\n    this.props.enrichmentQualityScore = Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  private determineManualReviewRequirement(): void {\r\n    // High threat intelligence risk requires manual review\r\n    if (this.isHighThreatRisk()) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Low enrichment quality requires manual review\r\n    if (!this.hasHighEnrichmentQuality()) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Events with validation errors require manual review\r\n    if (this.hasValidationErrors()) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Critical events require manual review\r\n    if (this.severity === EventSeverity.CRITICAL) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // High risk events require manual review\r\n    if ((this.props.riskScore || 0) >= 90) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Otherwise, no manual review required\r\n    this.props.requiresManualReview = false;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for display\r\n   */\r\n  getSummary(): {\r\n    id: string;\r\n    normalizedEventId: string;\r\n    title: string;\r\n    type: EventType;\r\n    severity: EventSeverity;\r\n    status: EventStatus;\r\n    enrichmentStatus: EnrichmentStatus;\r\n    enrichmentQualityScore?: number;\r\n    threatIntelScore?: number;\r\n    riskScore?: number;\r\n    appliedRulesCount: number;\r\n    enrichmentDataCount: number;\r\n    hasValidationErrors: boolean;\r\n    requiresManualReview: boolean;\r\n    isReadyForNextStage: boolean;\r\n    hasThreatIntelligence: boolean;\r\n    hasReputationData: boolean;\r\n  } {\r\n    return {\r\n      id: this.id.toString(),\r\n      normalizedEventId: this.props.normalizedEventId.toString(),\r\n      title: this.props.title,\r\n      type: this.props.type,\r\n      severity: this.props.severity,\r\n      status: this.props.status,\r\n      enrichmentStatus: this.props.enrichmentStatus,\r\n      enrichmentQualityScore: this.props.enrichmentQualityScore,\r\n      threatIntelScore: this.props.threatIntelScore,\r\n      riskScore: this.props.riskScore,\r\n      appliedRulesCount: this.props.appliedRules.length,\r\n      enrichmentDataCount: this.props.enrichmentData.length,\r\n      hasValidationErrors: this.hasValidationErrors(),\r\n      requiresManualReview: this.requiresManualReview,\r\n      isReadyForNextStage: this.isReadyForNextStage(),\r\n      hasThreatIntelligence: this.hasThreatIntelligence(),\r\n      hasReputationData: this.hasReputationData(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      normalizedEventId: this.props.normalizedEventId.toString(),\r\n      metadata: this.props.metadata.toJSON(),\r\n      type: this.props.type,\r\n      severity: this.props.severity,\r\n      status: this.props.status,\r\n      processingStatus: this.props.processingStatus,\r\n      enrichmentStatus: this.props.enrichmentStatus,\r\n      normalizedData: this.props.normalizedData,\r\n      enrichedData: this.props.enrichedData,\r\n      title: this.props.title,\r\n      description: this.props.description,\r\n      tags: this.props.tags,\r\n      riskScore: this.props.riskScore,\r\n      confidenceLevel: this.props.confidenceLevel,\r\n      attributes: this.props.attributes,\r\n      correlationId: this.props.correlationId,\r\n      parentEventId: this.props.parentEventId?.toString(),\r\n      appliedRules: this.props.appliedRules,\r\n      enrichmentData: this.props.enrichmentData,\r\n      enrichmentResult: this.props.enrichmentResult,\r\n      enrichmentStartedAt: this.props.enrichmentStartedAt?.toISOString(),\r\n      enrichmentCompletedAt: this.props.enrichmentCompletedAt?.toISOString(),\r\n      enrichmentAttempts: this.props.enrichmentAttempts,\r\n      lastEnrichmentError: this.props.lastEnrichmentError,\r\n      threatIntelScore: this.props.threatIntelScore,\r\n      assetContext: this.props.assetContext,\r\n      userContext: this.props.userContext,\r\n      networkContext: this.props.networkContext,\r\n      geolocationContext: this.props.geolocationContext,\r\n      reputationScores: this.props.reputationScores,\r\n      requiresManualReview: this.props.requiresManualReview,\r\n      reviewNotes: this.props.reviewNotes,\r\n      reviewedBy: this.props.reviewedBy,\r\n      reviewedAt: this.props.reviewedAt?.toISOString(),\r\n      enrichmentQualityScore: this.props.enrichmentQualityScore,\r\n      validationErrors: this.props.validationErrors,\r\n      summary: this.getSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}