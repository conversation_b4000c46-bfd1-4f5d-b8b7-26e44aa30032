{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\domain\\base-domain-event.ts", "mappings": ";;;AAAA,kGAAgF;AAEhF;;;;;;;;;;;;;GAaG;AACH,MAAsB,eAAe;IAYnC,YACE,WAA2B,EAC3B,SAAY,EACZ,OAOC;QAhBK,kBAAa,GAAY,KAAK,CAAC;QAkBrC,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,OAAO,IAAI,8CAAc,CAAC,QAAQ,EAAE,CAAC;QAC9D,IAAI,CAAC,WAAW,GAAG,OAAO,EAAE,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC;QACrD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,OAAO,EAAE,YAAY,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,aAAa,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,WAAW,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,QAAQ,IAAI,EAAE,CAAC;QAEzC,4DAA4D;QAC5D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,oFAAoF;IACtF,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACrB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,QAAgB;QAC7B,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAgC;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2C,CAAC;QACrE,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE;YACzD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,WAAW,EAAE;SAChD,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,aAAqB;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2C,CAAC;QACrE,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE;YACzD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,aAAa;YACb,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,WAAmB;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2C,CAAC;QACrE,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE;YACzD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,WAAW;YACX,QAAQ,EAAE,IAAI,CAAC,SAAS;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC1C,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;YACzC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;YAC/C,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,QAAQ,CACpB,IAAyB,EACzB,UAA0C;QAE1C,OAAO,IAAI,UAAU,CACnB,8CAAc,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAC3C,IAAI,CAAC,SAAS,EACd;YACE,OAAO,EAAE,8CAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YAChD,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAuB;QACnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC1D,CAAC;CACF;AAlRD,0CAkRC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\domain\\base-domain-event.ts"], "sourcesContent": ["import { UniqueEntityId } from '../value-objects/unique-entity-id.value-object';\r\n\r\n/**\r\n * Base Domain Event\r\n * \r\n * Abstract base class for all domain events in the system.\r\n * Domain events represent something important that happened in the domain.\r\n * \r\n * Key characteristics:\r\n * - Immutable once created\r\n * - Contains all necessary information about what happened\r\n * - Can be used for event sourcing, integration events, and notifications\r\n * - Includes metadata for tracking and debugging\r\n * \r\n * @template T The event data type\r\n */\r\nexport abstract class BaseDomainEvent<T = any> {\r\n  private readonly _eventId: UniqueEntityId;\r\n  private readonly _occurredOn: Date;\r\n  private readonly _aggregateId: UniqueEntityId;\r\n  private readonly _eventVersion: number;\r\n  private readonly _eventData: T;\r\n  private _isDispatched: boolean = false;\r\n  private _dispatchedAt?: Date;\r\n  private readonly _correlationId?: string;\r\n  private readonly _causationId?: string;\r\n  private readonly _metadata: Record<string, any>;\r\n\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: T,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    this._eventId = options?.eventId || UniqueEntityId.generate();\r\n    this._occurredOn = options?.occurredOn || new Date();\r\n    this._aggregateId = aggregateId;\r\n    this._eventVersion = options?.eventVersion || 1;\r\n    this._eventData = eventData;\r\n    this._correlationId = options?.correlationId;\r\n    this._causationId = options?.causationId;\r\n    this._metadata = options?.metadata || {};\r\n\r\n    // Freeze the event data and metadata to make them immutable\r\n    Object.freeze(this._eventData);\r\n    Object.freeze(this._metadata);\r\n    // Note: We don't freeze the entire object because we need to modify dispatch status\r\n  }\r\n\r\n  /**\r\n   * Unique identifier for this event\r\n   */\r\n  get eventId(): UniqueEntityId {\r\n    return this._eventId;\r\n  }\r\n\r\n  /**\r\n   * When the event occurred\r\n   */\r\n  get occurredOn(): Date {\r\n    return this._occurredOn;\r\n  }\r\n\r\n  /**\r\n   * The aggregate that generated this event\r\n   */\r\n  get aggregateId(): UniqueEntityId {\r\n    return this._aggregateId;\r\n  }\r\n\r\n  /**\r\n   * Version of the event schema\r\n   */\r\n  get eventVersion(): number {\r\n    return this._eventVersion;\r\n  }\r\n\r\n  /**\r\n   * The event data payload\r\n   */\r\n  get eventData(): T {\r\n    return this._eventData;\r\n  }\r\n\r\n  /**\r\n   * Whether this event has been dispatched\r\n   */\r\n  get isDispatched(): boolean {\r\n    return this._isDispatched;\r\n  }\r\n\r\n  /**\r\n   * When the event was dispatched\r\n   */\r\n  get dispatchedAt(): Date | undefined {\r\n    return this._dispatchedAt;\r\n  }\r\n\r\n  /**\r\n   * Correlation ID for tracking related events\r\n   */\r\n  get correlationId(): string | undefined {\r\n    return this._correlationId;\r\n  }\r\n\r\n  /**\r\n   * Causation ID for tracking event chains\r\n   */\r\n  get causationId(): string | undefined {\r\n    return this._causationId;\r\n  }\r\n\r\n  /**\r\n   * Additional metadata for the event\r\n   */\r\n  get metadata(): Record<string, any> {\r\n    return { ...this._metadata };\r\n  }\r\n\r\n  /**\r\n   * Get the event type name\r\n   */\r\n  get eventType(): string {\r\n    return this.constructor.name;\r\n  }\r\n\r\n  /**\r\n   * Get the event name for event emitter\r\n   */\r\n  get eventName(): string {\r\n    return this.eventType;\r\n  }\r\n\r\n  /**\r\n   * Mark the event as dispatched\r\n   * This should only be called by the event dispatcher\r\n   */\r\n  public markAsDispatched(): void {\r\n    if (this._isDispatched) {\r\n      throw new Error(`Event ${this._eventId.toString()} has already been dispatched`);\r\n    }\r\n\r\n    this._isDispatched = true;\r\n    this._dispatchedAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Get the age of the event in milliseconds\r\n   */\r\n  public getAge(): number {\r\n    return Date.now() - this._occurredOn.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get the age of the event in seconds\r\n   */\r\n  public getAgeInSeconds(): number {\r\n    return Math.floor(this.getAge() / 1000);\r\n  }\r\n\r\n  /**\r\n   * Get the age of the event in minutes\r\n   */\r\n  public getAgeInMinutes(): number {\r\n    return Math.floor(this.getAge() / (1000 * 60));\r\n  }\r\n\r\n  /**\r\n   * Check if the event is stale (older than specified time)\r\n   * \r\n   * @param maxAgeMs Maximum age in milliseconds\r\n   */\r\n  public isStale(maxAgeMs: number): boolean {\r\n    return this.getAge() > maxAgeMs;\r\n  }\r\n\r\n  /**\r\n   * Create a new event with updated metadata\r\n   * \r\n   * @param newMetadata Additional metadata to merge\r\n   */\r\n  public withMetadata(newMetadata: Record<string, any>): this {\r\n    const constructor = this.constructor as new (...args: any[]) => this;\r\n    return new constructor(this._aggregateId, this._eventData, {\r\n      eventId: this._eventId,\r\n      occurredOn: this._occurredOn,\r\n      eventVersion: this._eventVersion,\r\n      correlationId: this._correlationId,\r\n      causationId: this._causationId,\r\n      metadata: { ...this._metadata, ...newMetadata },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new event with a correlation ID\r\n   * \r\n   * @param correlationId The correlation ID to set\r\n   */\r\n  public withCorrelationId(correlationId: string): this {\r\n    const constructor = this.constructor as new (...args: any[]) => this;\r\n    return new constructor(this._aggregateId, this._eventData, {\r\n      eventId: this._eventId,\r\n      occurredOn: this._occurredOn,\r\n      eventVersion: this._eventVersion,\r\n      correlationId,\r\n      causationId: this._causationId,\r\n      metadata: this._metadata,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new event with a causation ID\r\n   * \r\n   * @param causationId The causation ID to set\r\n   */\r\n  public withCausationId(causationId: string): this {\r\n    const constructor = this.constructor as new (...args: any[]) => this;\r\n    return new constructor(this._aggregateId, this._eventData, {\r\n      eventId: this._eventId,\r\n      occurredOn: this._occurredOn,\r\n      eventVersion: this._eventVersion,\r\n      correlationId: this._correlationId,\r\n      causationId,\r\n      metadata: this._metadata,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Convert event to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      eventId: this._eventId.toString(),\r\n      eventType: this.eventType,\r\n      occurredOn: this._occurredOn.toISOString(),\r\n      aggregateId: this._aggregateId.toString(),\r\n      eventVersion: this._eventVersion,\r\n      eventData: this._eventData,\r\n      isDispatched: this._isDispatched,\r\n      dispatchedAt: this._dispatchedAt?.toISOString(),\r\n      correlationId: this._correlationId,\r\n      causationId: this._causationId,\r\n      metadata: this._metadata,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create event from JSON representation\r\n   */\r\n  public static fromJSON<TEvent extends BaseDomainEvent>(\r\n    json: Record<string, any>,\r\n    eventClass: new (...args: any[]) => TEvent\r\n  ): TEvent {\r\n    return new eventClass(\r\n      UniqueEntityId.fromString(json.aggregateId),\r\n      json.eventData,\r\n      {\r\n        eventId: UniqueEntityId.fromString(json.eventId),\r\n        occurredOn: new Date(json.occurredOn),\r\n        eventVersion: json.eventVersion,\r\n        correlationId: json.correlationId,\r\n        causationId: json.causationId,\r\n        metadata: json.metadata,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Compare events for equality\r\n   */\r\n  public equals(other?: BaseDomainEvent): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    return this._eventId.equals(other._eventId);\r\n  }\r\n\r\n  /**\r\n   * Get a string representation of the event\r\n   */\r\n  public toString(): string {\r\n    return `${this.eventType}(${this._eventId.toString()})`;\r\n  }\r\n}\r\n"], "version": 3}