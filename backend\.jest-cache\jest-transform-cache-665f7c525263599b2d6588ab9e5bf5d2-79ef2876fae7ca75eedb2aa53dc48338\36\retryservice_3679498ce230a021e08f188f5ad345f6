fea76ccac1c98a31ceafc728889b39f8
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AIRetryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIRetryService = void 0;
const common_1 = require("@nestjs/common");
const retry_strategy_1 = require("@/shared-kernel/patterns/retry-strategy");
const service_unavailable_exception_1 = require("@/shared-kernel/exceptions/service-unavailable.exception");
const rate_limit_exception_1 = require("@/shared-kernel/exceptions/rate-limit.exception");
/**
 * AI-specific retry service for managing AI provider resilience
 */
let AIRetryService = AIRetryService_1 = class AIRetryService {
    constructor() {
        this.logger = new common_1.Logger(AIRetryService_1.name);
        this.retryStrategies = new Map();
        this.configs = new Map();
        this.metrics = new Map();
    }
    /**
     * Register a retry strategy for an AI provider
     */
    registerProvider(providerId, providerType, config) {
        const retryOptions = {
            maxAttempts: config.maxAttempts,
            baseDelay: config.baseDelay,
            maxDelay: config.maxDelay,
            backoffMultiplier: config.backoffMultiplier,
            jitter: config.jitter,
            retryOn: (error) => this.shouldRetry(error, config.retryableErrors),
            onRetry: (error, attempt) => {
                this.logger.warn(`Retry attempt ${attempt} for ${providerType} provider ${providerId}: ${error.message}`);
                this.updateMetrics(providerId, attempt, false);
            },
        };
        const retryStrategy = new retry_strategy_1.RetryStrategy(retryOptions);
        this.retryStrategies.set(providerId, retryStrategy);
        this.configs.set(providerId, config);
        // Initialize metrics
        this.metrics.set(providerId, {
            providerId,
            totalAttempts: 0,
            successfulRetries: 0,
            failedRetries: 0,
            averageAttempts: 0,
            totalDelay: 0,
        });
        this.logger.log(`Registered retry strategy for ${providerType} provider ${providerId} with max ${config.maxAttempts} attempts`);
    }
    /**
     * Execute an AI operation with retry logic
     */
    async executeWithRetry(providerId, operation, timeoutMs) {
        const retryStrategy = this.retryStrategies.get(providerId);
        if (!retryStrategy) {
            throw new Error(`Retry strategy not registered for provider: ${providerId}`);
        }
        const config = this.configs.get(providerId);
        const operationTimeout = timeoutMs || config?.timeoutMs;
        try {
            let wrappedOperation = operation;
            // Add timeout wrapper if specified
            if (operationTimeout) {
                wrappedOperation = () => this.withTimeout(operation(), operationTimeout);
            }
            const result = await retryStrategy.execute(wrappedOperation);
            this.updateMetrics(providerId, 1, true);
            return result;
        }
        catch (error) {
            this.logger.error(`Retry execution failed for provider ${providerId} after all attempts`, error);
            this.updateMetrics(providerId, config?.maxAttempts || 1, false);
            throw error;
        }
    }
    /**
     * Execute with detailed retry information
     */
    async executeWithRetryDetails(providerId, operation, timeoutMs) {
        const retryStrategy = this.retryStrategies.get(providerId);
        if (!retryStrategy) {
            throw new Error(`Retry strategy not registered for provider: ${providerId}`);
        }
        const config = this.configs.get(providerId);
        const operationTimeout = timeoutMs || config?.timeoutMs;
        try {
            let wrappedOperation = operation;
            // Add timeout wrapper if specified
            if (operationTimeout) {
                wrappedOperation = () => this.withTimeout(operation(), operationTimeout);
            }
            const result = await retryStrategy.executeWithDetails(wrappedOperation);
            this.updateMetricsWithDetails(providerId, result);
            return result;
        }
        catch (error) {
            this.logger.error(`Retry execution with details failed for provider ${providerId}`, error);
            throw error;
        }
    }
    /**
     * Get retry metrics for a specific provider
     */
    getProviderMetrics(providerId) {
        return this.metrics.get(providerId) || null;
    }
    /**
     * Get metrics for all registered providers
     */
    getAllProviderMetrics() {
        return Array.from(this.metrics.values());
    }
    /**
     * Reset metrics for a specific provider
     */
    resetProviderMetrics(providerId) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            this.metrics.set(providerId, {
                providerId,
                totalAttempts: 0,
                successfulRetries: 0,
                failedRetries: 0,
                averageAttempts: 0,
                totalDelay: 0,
            });
            this.logger.log(`Reset retry metrics for provider ${providerId}`);
        }
    }
    /**
     * Reset metrics for all providers
     */
    resetAllMetrics() {
        for (const [providerId] of this.metrics) {
            this.resetProviderMetrics(providerId);
        }
        this.logger.log('Reset retry metrics for all providers');
    }
    /**
     * Update provider configuration
     */
    updateProviderConfig(providerId, config) {
        const existingConfig = this.configs.get(providerId);
        if (existingConfig) {
            const updatedConfig = { ...existingConfig, ...config };
            this.configs.set(providerId, updatedConfig);
            // Re-register with new config
            const providerType = this.getProviderType(providerId);
            this.registerProvider(providerId, providerType, updatedConfig);
            this.logger.log(`Updated retry configuration for provider ${providerId}`);
        }
    }
    /**
     * Remove a provider's retry strategy
     */
    unregisterProvider(providerId) {
        this.retryStrategies.delete(providerId);
        this.configs.delete(providerId);
        this.metrics.delete(providerId);
        this.logger.log(`Unregistered retry strategy for provider ${providerId}`);
    }
    /**
     * Get provider configuration
     */
    getProviderConfig(providerId) {
        return this.configs.get(providerId) || null;
    }
    /**
     * Check if an error should be retried based on configuration
     */
    shouldRetry(error, retryableErrors) {
        // Always retry on specific exception types
        if (error instanceof service_unavailable_exception_1.ServiceUnavailableException ||
            error instanceof rate_limit_exception_1.RateLimitException) {
            return true;
        }
        // Check if error message contains any retryable error patterns
        const errorMessage = error.message.toLowerCase();
        return retryableErrors.some(pattern => errorMessage.includes(pattern.toLowerCase()));
    }
    /**
     * Add timeout wrapper to operation
     */
    withTimeout(promise, timeoutMs) {
        return Promise.race([
            promise,
            new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`Operation timed out after ${timeoutMs}ms`));
                }, timeoutMs);
            }),
        ]);
    }
    /**
     * Update metrics after retry attempt
     */
    updateMetrics(providerId, attempts, success) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            metrics.totalAttempts += attempts;
            if (success) {
                metrics.successfulRetries++;
            }
            else {
                metrics.failedRetries++;
            }
            metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);
            metrics.lastRetryTime = new Date();
            this.metrics.set(providerId, metrics);
        }
    }
    /**
     * Update metrics with detailed retry information
     */
    updateMetricsWithDetails(providerId, result) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            metrics.totalAttempts += result.attempts;
            metrics.successfulRetries++;
            metrics.totalDelay += result.totalDelay;
            metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);
            metrics.lastRetryTime = new Date();
            this.metrics.set(providerId, metrics);
        }
    }
    /**
     * Extract provider type from provider ID
     */
    getProviderType(providerId) {
        return providerId.split('-')[0] || 'unknown';
    }
    /**
     * Create predefined retry strategies
     */
    static createExponentialBackoffConfig(maxAttempts = 3, baseDelay = 1000, retryableErrors = ['timeout', 'network', 'connection', 'unavailable']) {
        return {
            maxAttempts,
            baseDelay,
            maxDelay: baseDelay * Math.pow(2, maxAttempts - 1),
            backoffMultiplier: 2,
            jitter: true,
            retryableErrors,
        };
    }
    static createLinearBackoffConfig(maxAttempts = 3, delay = 1000, retryableErrors = ['timeout', 'network', 'connection', 'unavailable']) {
        return {
            maxAttempts,
            baseDelay: delay,
            maxDelay: delay,
            backoffMultiplier: 1,
            jitter: false,
            retryableErrors,
        };
    }
    static createFixedDelayConfig(maxAttempts = 3, delay = 1000, retryableErrors = ['timeout', 'network', 'connection', 'unavailable']) {
        return {
            maxAttempts,
            baseDelay: delay,
            maxDelay: delay,
            backoffMultiplier: 1,
            jitter: false,
            retryableErrors,
        };
    }
};
exports.AIRetryService = AIRetryService;
exports.AIRetryService = AIRetryService = AIRetryService_1 = __decorate([
    (0, common_1.Injectable)()
], AIRetryService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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