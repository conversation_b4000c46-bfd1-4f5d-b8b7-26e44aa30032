ed18bcd4c8c09ba4cd7ff2bdc5a11b2a
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AIRetryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIRetryService = void 0;
const common_1 = require("@nestjs/common");
const retry_strategy_1 = require("@/shared-kernel/patterns/retry-strategy");
const service_unavailable_exception_1 = require("@/shared-kernel/exceptions/service-unavailable.exception");
const rate_limit_exception_1 = require("@/shared-kernel/exceptions/rate-limit.exception");
/**
 * AI-specific retry service for managing AI provider resilience
 */
let AIRetryService = AIRetryService_1 = class AIRetryService {
    constructor() {
        this.logger = new common_1.Logger(AIRetryService_1.name);
        this.retryStrategies = new Map();
        this.configs = new Map();
        this.metrics = new Map();
    }
    /**
     * Register a retry strategy for an AI provider
     */
    registerProvider(providerId, providerType, config) {
        const retryOptions = {
            maxAttempts: config.maxAttempts,
            baseDelay: config.baseDelay,
            maxDelay: config.maxDelay,
            backoffMultiplier: config.backoffMultiplier,
            jitter: config.jitter,
            retryOn: (error) => this.shouldRetry(error, config.retryableErrors),
            onRetry: (error, attempt) => {
                this.logger.warn(`Retry attempt ${attempt} for ${providerType} provider ${providerId}: ${error.message}`);
            },
        };
        const retryStrategy = new retry_strategy_1.RetryStrategy(retryOptions);
        this.retryStrategies.set(providerId, retryStrategy);
        this.configs.set(providerId, config);
        // Initialize metrics
        this.metrics.set(providerId, {
            providerId,
            totalAttempts: 0,
            successfulRetries: 0,
            failedRetries: 0,
            averageAttempts: 0,
            totalDelay: 0,
        });
        this.logger.log(`Registered retry strategy for ${providerType} provider ${providerId} with max ${config.maxAttempts} attempts`);
    }
    /**
     * Execute an AI operation with retry logic
     */
    async executeWithRetry(providerId, operation, timeoutMs) {
        const retryStrategy = this.retryStrategies.get(providerId);
        if (!retryStrategy) {
            throw new Error(`Retry strategy not registered for provider: ${providerId}`);
        }
        const config = this.configs.get(providerId);
        const operationTimeout = timeoutMs || config?.timeoutMs;
        try {
            let wrappedOperation = operation;
            // Add timeout wrapper if specified
            if (operationTimeout) {
                wrappedOperation = () => this.withTimeout(operation(), operationTimeout);
            }
            const result = await retryStrategy.execute(wrappedOperation);
            this.updateMetrics(providerId, 1, true);
            return result;
        }
        catch (error) {
            this.logger.error(`Retry execution failed for provider ${providerId} after all attempts`, error);
            this.updateMetrics(providerId, config?.maxAttempts || 1, false);
            throw error;
        }
    }
    /**
     * Execute with detailed retry information
     */
    async executeWithRetryDetails(providerId, operation, timeoutMs) {
        const retryStrategy = this.retryStrategies.get(providerId);
        if (!retryStrategy) {
            throw new Error(`Retry strategy not registered for provider: ${providerId}`);
        }
        const config = this.configs.get(providerId);
        const operationTimeout = timeoutMs || config?.timeoutMs;
        try {
            let wrappedOperation = operation;
            // Add timeout wrapper if specified
            if (operationTimeout) {
                wrappedOperation = () => this.withTimeout(operation(), operationTimeout);
            }
            const result = await retryStrategy.executeWithDetails(wrappedOperation);
            this.updateMetricsWithDetails(providerId, result);
            return result;
        }
        catch (error) {
            this.logger.error(`Retry execution with details failed for provider ${providerId}`, error);
            throw error;
        }
    }
    /**
     * Get retry metrics for a specific provider
     */
    getProviderMetrics(providerId) {
        return this.metrics.get(providerId) || null;
    }
    /**
     * Get metrics for all registered providers
     */
    getAllProviderMetrics() {
        return Array.from(this.metrics.values());
    }
    /**
     * Reset metrics for a specific provider
     */
    resetProviderMetrics(providerId) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            this.metrics.set(providerId, {
                providerId,
                totalAttempts: 0,
                successfulRetries: 0,
                failedRetries: 0,
                averageAttempts: 0,
                totalDelay: 0,
            });
            this.logger.log(`Reset retry metrics for provider ${providerId}`);
        }
    }
    /**
     * Reset metrics for all providers
     */
    resetAllMetrics() {
        for (const [providerId] of this.metrics) {
            this.resetProviderMetrics(providerId);
        }
        this.logger.log('Reset retry metrics for all providers');
    }
    /**
     * Update provider configuration
     */
    updateProviderConfig(providerId, config) {
        const existingConfig = this.configs.get(providerId);
        if (existingConfig) {
            const updatedConfig = { ...existingConfig, ...config };
            this.configs.set(providerId, updatedConfig);
            // Re-register with new config
            const providerType = this.getProviderType(providerId);
            this.registerProvider(providerId, providerType, updatedConfig);
            this.logger.log(`Updated retry configuration for provider ${providerId}`);
        }
    }
    /**
     * Remove a provider's retry strategy
     */
    unregisterProvider(providerId) {
        this.retryStrategies.delete(providerId);
        this.configs.delete(providerId);
        this.metrics.delete(providerId);
        this.logger.log(`Unregistered retry strategy for provider ${providerId}`);
    }
    /**
     * Get provider configuration
     */
    getProviderConfig(providerId) {
        return this.configs.get(providerId) || null;
    }
    /**
     * Check if an error should be retried based on configuration
     */
    shouldRetry(error, retryableErrors) {
        // Always retry on specific exception types
        if (error instanceof service_unavailable_exception_1.ServiceUnavailableException ||
            error instanceof rate_limit_exception_1.RateLimitException) {
            return true;
        }
        // Check if error message contains any retryable error patterns
        const errorMessage = error.message.toLowerCase();
        return retryableErrors.some(pattern => errorMessage.includes(pattern.toLowerCase()));
    }
    /**
     * Add timeout wrapper to operation
     */
    withTimeout(promise, timeoutMs) {
        return Promise.race([
            promise,
            new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`Operation timed out after ${timeoutMs}ms`));
                }, timeoutMs);
            }),
        ]);
    }
    /**
     * Update metrics after retry attempt
     */
    updateMetrics(providerId, attempts, success) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            metrics.totalAttempts += attempts;
            if (success) {
                metrics.successfulRetries++;
            }
            else {
                metrics.failedRetries++;
            }
            metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);
            metrics.lastRetryTime = new Date();
            this.metrics.set(providerId, metrics);
        }
    }
    /**
     * Update metrics with detailed retry information
     */
    updateMetricsWithDetails(providerId, result) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            metrics.totalAttempts += result.attempts;
            metrics.successfulRetries++;
            metrics.totalDelay += result.totalDelay;
            metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);
            metrics.lastRetryTime = new Date();
            this.metrics.set(providerId, metrics);
        }
    }
    /**
     * Extract provider type from provider ID
     */
    getProviderType(providerId) {
        return providerId.split('-')[0] || 'unknown';
    }
    /**
     * Create predefined retry strategies
     */
    static createExponentialBackoffConfig(maxAttempts = 3, baseDelay = 1000, retryableErrors = ['timeout', 'network', 'connection', 'unavailable']) {
        return {
            maxAttempts,
            baseDelay,
            maxDelay: baseDelay * Math.pow(2, maxAttempts - 1),
            backoffMultiplier: 2,
            jitter: true,
            retryableErrors,
        };
    }
    static createLinearBackoffConfig(maxAttempts = 3, delay = 1000, retryableErrors = ['timeout', 'network', 'connection', 'unavailable']) {
        return {
            maxAttempts,
            baseDelay: delay,
            maxDelay: delay,
            backoffMultiplier: 1,
            jitter: false,
            retryableErrors,
        };
    }
    static createFixedDelayConfig(maxAttempts = 3, delay = 1000, retryableErrors = ['timeout', 'network', 'connection', 'unavailable']) {
        return {
            maxAttempts,
            baseDelay: delay,
            maxDelay: delay,
            backoffMultiplier: 1,
            jitter: false,
            retryableErrors,
        };
    }
};
exports.AIRetryService = AIRetryService;
exports.AIRetryService = AIRetryService = AIRetryService_1 = __decorate([
    (0, common_1.Injectable)()
], AIRetryService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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