import { BaseAggregateRoot } from '../../../../../shared-kernel/domain/base-aggregate-root';
import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { NormalizedEvent } from '../normalized-event.entity';
import { IOC } from '../../value-objects/threat-indicators/ioc.value-object';
import { EventEnrichedEvent } from '../../events/event-enriched.event';

/**
 * Enriched Event Properties
 */
export interface EnrichedEventProps {
  /** Reference to the normalized event */
  normalizedEventId: UniqueEntityId;
  /** Enrichment data */
  enrichmentData: EnrichmentData;
  /** Enrichment metadata */
  enrichmentMetadata: EnrichmentMetadata;
  /** Enrichment score (0-100) */
  enrichmentScore: number;
  /** Enrichment errors if any */
  errors: EnrichmentError[];
}

/**
 * Enrichment Data
 */
export interface EnrichmentData {
  /** Threat intelligence enrichment */
  threatIntelligence: {
    /** Identified indicators of compromise */
    indicators: IOC[];
    /** Threat actor attribution */
    attribution?: {
      actor: string;
      confidence: number;
      techniques: string[];
      campaigns: string[];
    };
    /** Malware family information */
    malwareFamily?: {
      name: string;
      variant?: string;
      confidence: number;
    };
    /** Reputation scores */
    reputation: {
      sourceIp?: ReputationScore;
      destinationIp?: ReputationScore;
      domain?: ReputationScore;
      fileHash?: ReputationScore;
      url?: ReputationScore;
    };
  };
  /** Geolocation enrichment */
  geolocation: {
    sourceLocation?: GeolocationData;
    destinationLocation?: GeolocationData;
  };
  /** Asset enrichment */
  assetContext: {
    /** Asset details */
    assetDetails?: {
      owner: string;
      department: string;
      criticality: 'low' | 'medium' | 'high' | 'critical';
      classification: string;
      lastSeen: Date;
    };
    /** Vulnerability information */
    vulnerabilities?: Array<{
      cve: string;
      severity: string;
      score: number;
      description: string;
    }>;
    /** Software inventory */
    software?: Array<{
      name: string;
      version: string;
      vendor: string;
      lastUpdated: Date;
    }>;
  };
  /** User enrichment */
  userContext: {
    /** User details */
    userDetails?: {
      fullName: string;
      email: string;
      department: string;
      title: string;
      manager: string;
      riskScore: number;
    };
    /** Authentication context */
    authContext?: {
      lastLogin: Date;
      loginCount: number;
      failedAttempts: number;
      privilegeLevel: 'standard' | 'elevated' | 'admin';
    };
    /** Behavioral baseline */
    behavioralBaseline?: {
      typicalHours: string[];
      typicalLocations: string[];
      typicalAssets: string[];
      riskProfile: 'low' | 'medium' | 'high';
    };
  };
  /** Network enrichment */
  networkContext: {
    /** Network segment information */
    segmentInfo?: {
      segment: string;
      zone: string;
      trustLevel: 'untrusted' | 'dmz' | 'internal' | 'trusted';
      vlan: string;
    };
    /** DNS information */
    dnsInfo?: {
      sourceHostname?: string;
      destinationHostname?: string;
      reverseDns?: string;
    };
    /** Port and service information */
    serviceInfo?: {
      serviceName?: string;
      serviceVersion?: string;
      isKnownService: boolean;
      isEncrypted: boolean;
    };
  };
  /** Temporal enrichment */
  temporalContext: {
    /** Time-based patterns */
    timePatterns?: {
      isBusinessHours: boolean;
      isWeekend: boolean;
      isHoliday: boolean;
      timeZone: string;
    };
    /** Historical context */
    historicalContext?: {
      similarEventsCount: number;
      lastSimilarEvent?: Date;
      frequency: 'rare' | 'occasional' | 'frequent' | 'constant';
    };
  };
  /** Custom enrichment data */
  custom: Record<string, any>;
}

/**
 * Reputation Score
 */
export interface ReputationScore {
  score: number; // 0-100 (0 = malicious, 100 = benign)
  category: 'malicious' | 'suspicious' | 'unknown' | 'benign';
  sources: Array<{
    provider: string;
    score: number;
    lastUpdated: Date;
  }>;
  confidence: number;
}

/**
 * Geolocation Data
 */
export interface GeolocationData {
  country: string;
  countryCode: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
  timezone: string;
  isp: string;
  organization: string;
  asn: number;
  isAnonymousProxy: boolean;
  isSatelliteProvider: boolean;
}

/**
 * Enrichment Metadata
 */
export interface EnrichmentMetadata {
  /** Enrichment engine version */
  engineVersion: string;
  /** Enrichment sources used */
  sourcesUsed: string[];
  /** Processing duration in milliseconds */
  processingDuration: number;
  /** Enriched at timestamp */
  enrichedAt: Date;
  /** Data sources and their contributions */
  sourceContributions: Array<{
    source: string;
    fieldsEnriched: string[];
    confidence: number;
    latency: number;
  }>;
  /** Cache hits and misses */
  cacheMetrics: {
    hits: number;
    misses: number;
    hitRate: number;
  };
}

/**
 * Enrichment Error
 */
export interface EnrichmentError {
  /** Error type */
  type: 'source_unavailable' | 'timeout' | 'rate_limit' | 'invalid_data' | 'authentication';
  /** Error source */
  source: string;
  /** Error message */
  message: string;
  /** Field that failed enrichment */
  field?: string;
  /** Error severity */
  severity: 'low' | 'medium' | 'high' | 'critical';
  /** Retry information */
  retryable: boolean;
}

/**
 * Enriched Event Entity
 * 
 * Represents a normalized event that has been enriched with additional context
 * from threat intelligence, asset management, user directories, and other sources.
 * 
 * Key responsibilities:
 * - Store enriched event data with context
 * - Track enrichment metadata and sources
 * - Validate enrichment quality and completeness
 * - Provide contextual analysis capabilities
 * - Support correlation preparation
 * 
 * Business Rules:
 * - Must reference a valid normalized event
 * - Enrichment score reflects data quality and completeness
 * - Critical enrichment failures are tracked but don't prevent creation
 * - Threat intelligence data must be validated
 */
export class EnrichedEvent extends BaseAggregateRoot<EnrichedEventProps> {
  private static readonly MIN_ENRICHMENT_SCORE = 0;
  private static readonly MAX_ENRICHMENT_SCORE = 100;

  constructor(props: EnrichedEventProps, id?: UniqueEntityId) {
    super(props, id);
  }

  protected validate(): void {
    if (!this.props.normalizedEventId) {
      throw new Error('Enriched event must reference a normalized event');
    }

    if (!this.props.enrichmentData) {
      throw new Error('Enriched event must have enrichment data');
    }

    if (!this.props.enrichmentMetadata) {
      throw new Error('Enriched event must have enrichment metadata');
    }

    if (this.props.enrichmentScore < EnrichedEvent.MIN_ENRICHMENT_SCORE || 
        this.props.enrichmentScore > EnrichedEvent.MAX_ENRICHMENT_SCORE) {
      throw new Error(`Enrichment score must be between ${EnrichedEvent.MIN_ENRICHMENT_SCORE} and ${EnrichedEvent.MAX_ENRICHMENT_SCORE}`);
    }

    // Validate IOCs if present
    this.props.enrichmentData.threatIntelligence.indicators.forEach((ioc, index) => {
      if (!ioc || typeof ioc.value !== 'string' || !ioc.type) {
        throw new Error(`Invalid IOC at index ${index}`);
      }
    });
  }

  /**
   * Create an enriched event from a normalized event
   */
  static create(
    normalizedEvent: NormalizedEvent,
    enrichmentData: EnrichmentData,
    enrichmentMetadata: EnrichmentMetadata,
    errors: EnrichmentError[] = []
  ): EnrichedEvent {
    const enrichmentScore = EnrichedEvent.calculateEnrichmentScore(
      enrichmentData,
      enrichmentMetadata,
      errors
    );

    const props: EnrichedEventProps = {
      normalizedEventId: normalizedEvent.id,
      enrichmentData,
      enrichmentMetadata,
      enrichmentScore,
      errors,
    };

    const enrichedEvent = new EnrichedEvent(props);

    // Publish domain event
    const domainEvent = new EventEnrichedEvent(
      enrichedEvent.id,
      {
        normalizedEventId: normalizedEvent.id.toString(),
        enrichedEventId: enrichedEvent.id.toString(),
        enrichmentScore,
        indicatorCount: enrichmentData.threatIntelligence.indicators.length,
        sourcesUsed: enrichmentMetadata.sourcesUsed,
        hasErrors: errors.length > 0,
        processingDuration: enrichmentMetadata.processingDuration,
        timestamp: new Date().toISOString(),
      }
    );
    enrichedEvent.addDomainEvent(domainEvent);

    return enrichedEvent;
  }

  private static calculateEnrichmentScore(
    data: EnrichmentData,
    metadata: EnrichmentMetadata,
    errors: EnrichmentError[]
  ): number {
    let score = 0;

    // Base score from successful enrichment categories
    const categories = [
      { key: 'threatIntelligence', weight: 30, hasData: data.threatIntelligence.indicators.length > 0 },
      { key: 'geolocation', weight: 15, hasData: data.geolocation.sourceLocation || data.geolocation.destinationLocation },
      { key: 'assetContext', weight: 20, hasData: data.assetContext.assetDetails !== undefined },
      { key: 'userContext', weight: 20, hasData: data.userContext.userDetails !== undefined },
      { key: 'networkContext', weight: 10, hasData: data.networkContext.segmentInfo !== undefined },
      { key: 'temporalContext', weight: 5, hasData: data.temporalContext.timePatterns !== undefined },
    ];

    categories.forEach(category => {
      if (category.hasData) {
        score += category.weight;
      }
    });

    // Bonus for high-quality threat intelligence
    if (data.threatIntelligence.indicators.length > 0) {
      const highConfidenceIOCs = data.threatIntelligence.indicators.filter(
        ioc => ioc.isHighConfidence()
      );
      score += Math.min(10, highConfidenceIOCs.length * 2);
    }

    // Bonus for attribution
    if (data.threatIntelligence.attribution) {
      score += data.threatIntelligence.attribution.confidence * 0.1;
    }

    // Penalty for errors
    errors.forEach(error => {
      switch (error.severity) {
        case 'critical': score -= 15; break;
        case 'high': score -= 10; break;
        case 'medium': score -= 5; break;
        case 'low': score -= 2; break;
      }
    });

    // Bonus for cache efficiency
    if (metadata.cacheMetrics.hitRate > 0.8) {
      score += 5;
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Get the entity ID (inherited from BaseAggregateRoot)
   */
  get id(): UniqueEntityId {
    return this._id;
  }

  /**
   * Get the creation timestamp from metadata
   */
  get createdAt(): Date | undefined {
    return this.props.enrichmentMetadata?.enrichedAt;
  }

  /**
   * Get normalized event ID
   */
  get normalizedEventId(): UniqueEntityId {
    return this.props.normalizedEventId;
  }

  /**
   * Get enrichment data
   */
  get enrichmentData(): EnrichmentData {
    return this.props.enrichmentData;
  }

  /**
   * Get enrichment metadata
   */
  get enrichmentMetadata(): EnrichmentMetadata {
    return this.props.enrichmentMetadata;
  }

  /**
   * Get enrichment score
   */
  get enrichmentScore(): number {
    return this.props.enrichmentScore;
  }

  /**
   * Get enrichment errors
   */
  get errors(): EnrichmentError[] {
    return [...this.props.errors];
  }

  /**
   * Get threat intelligence data
   */
  get threatIntelligence(): EnrichmentData['threatIntelligence'] {
    return this.props.enrichmentData.threatIntelligence;
  }

  /**
   * Get IOCs
   */
  get indicators(): IOC[] {
    return this.props.enrichmentData.threatIntelligence.indicators;
  }

  /**
   * Get geolocation data
   */
  get geolocation(): EnrichmentData['geolocation'] {
    return this.props.enrichmentData.geolocation;
  }

  /**
   * Get asset context
   */
  get assetContext(): EnrichmentData['assetContext'] {
    return this.props.enrichmentData.assetContext;
  }

  /**
   * Get user context
   */
  get userContext(): EnrichmentData['userContext'] {
    return this.props.enrichmentData.userContext;
  }

  /**
   * Get network context
   */
  get networkContext(): EnrichmentData['networkContext'] {
    return this.props.enrichmentData.networkContext;
  }

  /**
   * Check if enrichment was successful
   */
  isEnrichmentSuccessful(): boolean {
    return this.props.enrichmentScore >= 50 && 
           !this.hasCriticalErrors();
  }

  /**
   * Check if event has high-quality enrichment
   */
  hasHighQualityEnrichment(): boolean {
    return this.props.enrichmentScore >= 80;
  }

  /**
   * Check if event has threat intelligence
   */
  hasThreatIntelligence(): boolean {
    return this.props.enrichmentData.threatIntelligence.indicators.length > 0;
  }

  /**
   * Check if event has malicious indicators
   */
  hasMaliciousIndicators(): boolean {
    return this.props.enrichmentData.threatIntelligence.indicators.some(
      ioc => ioc.isHighSeverity()
    );
  }

  /**
   * Check if event has critical errors
   */
  hasCriticalErrors(): boolean {
    return this.props.errors.some(error => error.severity === 'critical');
  }

  /**
   * Get high-confidence IOCs
   */
  getHighConfidenceIOCs(): IOC[] {
    return this.props.enrichmentData.threatIntelligence.indicators.filter(
      ioc => ioc.isHighConfidence()
    );
  }

  /**
   * Get malicious reputation scores
   */
  getMaliciousReputationScores(): Array<{ type: string; score: ReputationScore }> {
    const maliciousScores: Array<{ type: string; score: ReputationScore }> = [];
    const reputation = this.props.enrichmentData.threatIntelligence.reputation;

    Object.entries(reputation).forEach(([type, score]) => {
      if (score && (score.category === 'malicious' || score.category === 'suspicious')) {
        maliciousScores.push({ type, score });
      }
    });

    return maliciousScores;
  }

  /**
   * Get threat attribution confidence
   */
  getThreatAttributionConfidence(): number {
    return this.props.enrichmentData.threatIntelligence.attribution?.confidence || 0;
  }

  /**
   * Check if event is ready for correlation
   */
  isReadyForCorrelation(): boolean {
    return this.isEnrichmentSuccessful() && 
           (this.hasThreatIntelligence() || this.hasContextualData());
  }

  private hasContextualData(): boolean {
    return !!(this.props.enrichmentData.assetContext.assetDetails ||
              this.props.enrichmentData.userContext.userDetails ||
              this.props.enrichmentData.networkContext.segmentInfo);
  }

  /**
   * Get correlation readiness score
   */
  getCorrelationReadinessScore(): number {
    let score = this.props.enrichmentScore;

    // Bonus for threat intelligence
    if (this.hasThreatIntelligence()) {
      score += 15;
    }

    // Bonus for malicious indicators
    if (this.hasMaliciousIndicators()) {
      score += 10;
    }

    // Bonus for attribution
    if (this.props.enrichmentData.threatIntelligence.attribution) {
      score += 10;
    }

    // Bonus for contextual data
    if (this.hasContextualData()) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get enrichment completeness metrics
   */
  getEnrichmentCompleteness(): {
    overall: number;
    threatIntelligence: number;
    geolocation: number;
    assetContext: number;
    userContext: number;
    networkContext: number;
  } {
    const data = this.props.enrichmentData;

    return {
      overall: this.props.enrichmentScore,
      threatIntelligence: data.threatIntelligence.indicators.length > 0 ? 100 : 0,
      geolocation: (data.geolocation.sourceLocation || data.geolocation.destinationLocation) ? 100 : 0,
      assetContext: data.assetContext.assetDetails ? 100 : 0,
      userContext: data.userContext.userDetails ? 100 : 0,
      networkContext: data.networkContext.segmentInfo ? 100 : 0,
    };
  }

  /**
   * Get custom enrichment field
   */
  getCustomField<T = any>(key: string): T | undefined {
    return this.props.enrichmentData.custom[key] as T;
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      id: this.id.toString(),
      normalizedEventId: this.props.normalizedEventId.toString(),
      enrichmentData: this.props.enrichmentData,
      enrichmentMetadata: this.props.enrichmentMetadata,
      enrichmentScore: this.props.enrichmentScore,
      errors: this.props.errors,
      analysis: {
        isEnrichmentSuccessful: this.isEnrichmentSuccessful(),
        hasHighQualityEnrichment: this.hasHighQualityEnrichment(),
        hasThreatIntelligence: this.hasThreatIntelligence(),
        hasMaliciousIndicators: this.hasMaliciousIndicators(),
        isReadyForCorrelation: this.isReadyForCorrelation(),
        correlationReadinessScore: this.getCorrelationReadinessScore(),
        enrichmentCompleteness: this.getEnrichmentCompleteness(),
        highConfidenceIOCCount: this.getHighConfidenceIOCs().length,
        maliciousReputationCount: this.getMaliciousReputationScores().length,
      },
    };
  }
}
