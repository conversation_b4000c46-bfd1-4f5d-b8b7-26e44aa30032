import { Test, TestingModule } from '@nestjs/testing';
import { AITimeoutService, TimeoutConfig } from '../timeout.service';

describe('AITimeoutService', () => {
  let service: AITimeoutService;

  const defaultConfig: TimeoutConfig = {
    defaultTimeout: 1000, // Use smaller timeouts for faster tests
    operationTimeouts: {
      'quick-operation': 500,
      'slow-operation': 2000,
    },
    escalationTimeouts: [1000, 1500, 2000],
    enableEscalation: true,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AITimeoutService],
    }).compile();

    service = module.get<AITimeoutService>(AITimeoutService);
  });

  afterEach(() => {
    // Clean up all registered providers and cancel operations
    service.cancelAllOperations();
    service.resetAllMetrics();
  });

  describe('registerProvider', () => {
    it('should register a new provider with timeout configuration', () => {
      const providerId = 'test-provider';
      const providerType = 'openai';

      service.registerProvider(providerId, providerType, defaultConfig);

      const config = service.getProviderConfig(providerId);
      expect(config).toEqual(defaultConfig);
    });

    it('should initialize metrics for registered provider', () => {
      const providerId = 'test-provider';
      const providerType = 'openai';

      service.registerProvider(providerId, providerType, defaultConfig);

      const metrics = service.getProviderMetrics(providerId);
      expect(metrics).toBeDefined();
      expect(metrics!.providerId).toBe(providerId);
      expect(metrics!.totalOperations).toBe(0);
      expect(metrics!.timeoutCount).toBe(0);
    });
  });

  describe('executeWithTimeout', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should execute successful operation within timeout', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      });

      const result = await service.executeWithTimeout('test-provider', operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
      
      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalOperations).toBe(1);
      expect(metrics!.timeoutCount).toBe(0);
    });

    it('should timeout operation that exceeds timeout limit', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 1500));
        return 'success';
      });

      await expect(
        service.executeWithTimeout('test-provider', operation)
      ).rejects.toThrow('Operation timed out after 1000ms');

      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalOperations).toBe(1);
      expect(metrics!.timeoutCount).toBe(1);
      expect(metrics!.timeoutRate).toBe(1);
    });

    it('should use operation-specific timeout', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 700));
        return 'success';
      });

      // Should timeout with quick-operation timeout (500ms)
      await expect(
        service.executeWithTimeout('test-provider', operation, 'quick-operation')
      ).rejects.toThrow('Operation timed out after 500ms');
    });

    it('should use custom timeout when provided', async () => {
      // First test successful operation with custom timeout
      const quickOperation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 200));
        return 'success';
      });

      const result = await service.executeWithTimeout('test-provider', quickOperation, undefined, 500);
      expect(result).toBe('success');

      // Then test timeout with custom timeout (300ms)
      const slowOperation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 400));
        return 'success';
      });

      await expect(
        service.executeWithTimeout('test-provider', slowOperation, undefined, 300)
      ).rejects.toThrow('Operation timed out after 300ms');
    });

    it('should throw error for unregistered provider', async () => {
      const operation = jest.fn().mockResolvedValue('success');

      await expect(
        service.executeWithTimeout('unknown-provider', operation)
      ).rejects.toThrow('Timeout configuration not registered for provider: unknown-provider');
    });

    it('should handle operation errors properly', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('operation failed'));

      await expect(
        service.executeWithTimeout('test-provider', operation)
      ).rejects.toThrow('operation failed');

      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalOperations).toBe(1);
      expect(metrics!.timeoutCount).toBe(0); // Not a timeout error
    });

    it('should support abort signal in operation', async () => {
      const operation = jest.fn().mockImplementation(async (signal?: AbortSignal) => {
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => resolve('success'), 500);
          
          if (signal) {
            signal.addEventListener('abort', () => {
              clearTimeout(timeout);
              reject(new Error('Operation aborted'));
            });
          }
        });
      });

      // Start operation and cancel it
      const promise = service.executeWithTimeout('test-provider', operation);
      
      // Cancel after a short delay
      setTimeout(() => {
        service.cancelProviderOperations('test-provider');
      }, 100);

      await expect(promise).rejects.toThrow('Operation was cancelled');
    });
  });

  describe('executeWithTimeoutDetails', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return detailed timeout information for successful operation', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      });

      const result = await service.executeWithTimeoutDetails('test-provider', operation);

      expect(result.result).toBe('success');
      expect(result.timedOut).toBe(false);
      expect(result.executionTime).toBeGreaterThan(90);
      expect(result.executionTime).toBeLessThan(200);
    });

    it('should return detailed timeout information for timed out operation', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 1500));
        return 'success';
      });

      const result = await service.executeWithTimeoutDetails('test-provider', operation);

      expect(result.timedOut).toBe(true);
      expect(result.executionTime).toBeGreaterThan(900);
    });
  });

  describe('executeWithEscalation', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should use escalating timeouts for different attempts', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 1200));
        return 'success';
      });

      // First attempt should timeout with 1000ms
      await expect(
        service.executeWithEscalation('test-provider', operation, 1)
      ).rejects.toThrow('Operation timed out after 1000ms');

      // Second attempt should timeout with 1500ms (should succeed)
      const operation2 = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 1200));
        return 'success';
      });

      const result = await service.executeWithEscalation('test-provider', operation2, 2);
      expect(result).toBe('success');
    });

    it('should handle escalation when disabled', async () => {
      const configWithoutEscalation: TimeoutConfig = {
        ...defaultConfig,
        enableEscalation: false,
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', configWithoutEscalation);

      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      });

      const result = await service.executeWithEscalation('test-provider', operation, 3);
      expect(result).toBe('success');
    });
  });

  describe('cancelProviderOperations', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should cancel active operations for a provider', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        return 'success';
      });

      // Start multiple operations
      const promise1 = service.executeWithTimeout('test-provider', operation);
      const promise2 = service.executeWithTimeout('test-provider', operation);

      // Wait a bit to ensure operations are active
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(service.getActiveOperationCount('test-provider')).toBe(2);

      const cancelledCount = service.cancelProviderOperations('test-provider');
      expect(cancelledCount).toBe(2);

      await expect(promise1).rejects.toThrow('Operation was cancelled');
      await expect(promise2).rejects.toThrow('Operation was cancelled');
    });
  });

  describe('cancelAllOperations', () => {
    beforeEach(() => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);
    });

    it('should cancel all active operations', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        return 'success';
      });

      // Start operations for different providers
      const promise1 = service.executeWithTimeout('provider1', operation);
      const promise2 = service.executeWithTimeout('provider2', operation);

      // Wait a bit to ensure operations are active
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(service.getTotalActiveOperationCount()).toBe(2);

      const cancelledCount = service.cancelAllOperations();
      expect(cancelledCount).toBe(2);

      await expect(promise1).rejects.toThrow('Operation was cancelled');
      await expect(promise2).rejects.toThrow('Operation was cancelled');
    });
  });

  describe('getProviderMetrics', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return metrics for registered provider', () => {
      const metrics = service.getProviderMetrics('test-provider');

      expect(metrics).toBeDefined();
      expect(metrics!.providerId).toBe('test-provider');
    });

    it('should return null for unregistered provider', () => {
      const metrics = service.getProviderMetrics('unknown-provider');
      expect(metrics).toBeNull();
    });

    it('should update metrics after operations', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      });

      await service.executeWithTimeout('test-provider', operation);

      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalOperations).toBe(1);
      expect(metrics!.averageExecutionTime).toBeGreaterThan(90);
      expect(metrics!.maxExecutionTime).toBeGreaterThan(90);
    });
  });

  describe('getAllProviderMetrics', () => {
    it('should return empty array when no providers registered', () => {
      const metrics = service.getAllProviderMetrics();
      expect(metrics).toEqual([]);
    });

    it('should return metrics for all registered providers', () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      const metrics = service.getAllProviderMetrics();
      expect(metrics).toHaveLength(2);
      expect(metrics.map(m => m.providerId)).toContain('provider1');
      expect(metrics.map(m => m.providerId)).toContain('provider2');
    });
  });

  describe('resetProviderMetrics', () => {
    beforeEach(async () => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
      
      // Generate some metrics
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      });
      await service.executeWithTimeout('test-provider', operation);
    });

    it('should reset metrics for specific provider', () => {
      // Verify metrics exist
      let metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalOperations).toBe(1);

      // Reset metrics
      service.resetProviderMetrics('test-provider');

      // Verify metrics are reset
      metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalOperations).toBe(0);
      expect(metrics!.averageExecutionTime).toBe(0);
    });
  });

  describe('updateProviderConfig', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should update provider configuration', () => {
      const newConfig = { defaultTimeout: 5000 };
      
      service.updateProviderConfig('test-provider', newConfig);

      const config = service.getProviderConfig('test-provider');
      expect(config!.defaultTimeout).toBe(5000);
      expect(config!.enableEscalation).toBe(true); // Should keep existing values
    });
  });

  describe('unregisterProvider', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should remove provider configuration and metrics', () => {
      expect(service.getProviderConfig('test-provider')).toBeDefined();
      expect(service.getProviderMetrics('test-provider')).toBeDefined();

      service.unregisterProvider('test-provider');

      expect(service.getProviderConfig('test-provider')).toBeNull();
      expect(service.getProviderMetrics('test-provider')).toBeNull();
    });

    it('should cancel active operations when unregistering', async () => {
      const operation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        return 'success';
      });

      const promise = service.executeWithTimeout('test-provider', operation);

      // Wait a bit to ensure operation is active
      await new Promise(resolve => setTimeout(resolve, 50));

      service.unregisterProvider('test-provider');

      await expect(promise).rejects.toThrow('Operation was cancelled');
    });
  });

  describe('static factory methods', () => {
    it('should create default timeout config', () => {
      const config = AITimeoutService.createDefaultConfig(60000);

      expect(config.defaultTimeout).toBe(60000);
      expect(config.enableEscalation).toBe(true);
      expect(config.escalationTimeouts).toHaveLength(3);
    });

    it('should create quick timeout config', () => {
      const config = AITimeoutService.createQuickConfig(3000);

      expect(config.defaultTimeout).toBe(3000);
      expect(config.operationTimeouts['quick-analysis']).toBe(2000);
      expect(config.enableEscalation).toBe(true);
    });

    it('should create long running timeout config', () => {
      const config = AITimeoutService.createLongRunningConfig(180000);

      expect(config.defaultTimeout).toBe(180000);
      expect(config.operationTimeouts['model-training']).toBe(300000);
      expect(config.escalationTimeouts).toHaveLength(4);
    });
  });
});