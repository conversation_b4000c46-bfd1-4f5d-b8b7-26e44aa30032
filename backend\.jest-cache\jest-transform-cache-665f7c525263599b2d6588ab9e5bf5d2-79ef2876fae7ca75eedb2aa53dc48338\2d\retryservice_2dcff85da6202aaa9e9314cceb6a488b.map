{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\retry.service.ts", "mappings": ";;;;;;;;;;AAAA,2CAAoD;AACpD,4EAAmG;AACnG,4GAAuG;AACvG,0FAAqF;AAsBrF;;GAEG;AAEI,IAAM,cAAc,sBAApB,MAAM,cAAc;IAApB;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;QACzC,oBAAe,GAAG,IAAI,GAAG,EAAyB,CAAC;QACnD,YAAO,GAAG,IAAI,GAAG,EAAyB,CAAC;QAC3C,YAAO,GAAG,IAAI,GAAG,EAA0B,CAAC;IA0T/D,CAAC;IAxTC;;OAEG;IACH,gBAAgB,CACd,UAAkB,EAClB,YAAoB,EACpB,MAAqB;QAErB,MAAM,YAAY,GAAiB;YACjC,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;YAC3C,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,eAAe,CAAC;YAC1E,OAAO,EAAE,CAAC,KAAY,EAAE,OAAe,EAAE,EAAE;gBACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,iBAAiB,OAAO,QAAQ,YAAY,aAAa,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,CACxF,CAAC;YACJ,CAAC;SACF,CAAC;QAEF,MAAM,aAAa,GAAG,IAAI,8BAAa,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAErC,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YAC3B,UAAU;YACV,aAAa,EAAE,CAAC;YAChB,iBAAiB,EAAE,CAAC;YACpB,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,UAAU,EAAE,CAAC;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,iCAAiC,YAAY,aAAa,UAAU,aAAa,MAAM,CAAC,WAAW,WAAW,CAC/G,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,UAAkB,EAClB,SAA2B,EAC3B,SAAkB;QAElB,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,+CAA+C,UAAU,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,gBAAgB,GAAG,SAAS,IAAI,MAAM,EAAE,SAAS,CAAC;QAExD,IAAI,CAAC;YACH,IAAI,gBAAgB,GAAG,SAAS,CAAC;YAEjC,mCAAmC;YACnC,IAAI,gBAAgB,EAAE,CAAC;gBACrB,gBAAgB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAC3E,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAC7D,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;YACxC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uCAAuC,UAAU,qBAAqB,EACtE,KAAK,CACN,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,UAAkB,EAClB,SAA2B,EAC3B,SAAkB;QAElB,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,+CAA+C,UAAU,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,gBAAgB,GAAG,SAAS,IAAI,MAAM,EAAE,SAAS,CAAC;QAExD,IAAI,CAAC;YACH,IAAI,gBAAgB,GAAG,SAAS,CAAC;YAEjC,mCAAmC;YACnC,IAAI,gBAAgB,EAAE,CAAC;gBACrB,gBAAgB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAC3E,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;YACxE,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAClD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,oDAAoD,UAAU,EAAE,EAChE,KAAK,CACN,CAAC;YACF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC3B,UAAU;gBACV,aAAa,EAAE,CAAC;gBAChB,iBAAiB,EAAE,CAAC;gBACpB,aAAa,EAAE,CAAC;gBAChB,eAAe,EAAE,CAAC;gBAClB,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,UAAU,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QACb,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACxC,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB,EAAE,MAA8B;QACrE,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,MAAM,EAAE,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAE5C,8BAA8B;YAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,UAAU,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,UAAU,EAAE,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAY,EAAE,eAAyB;QACzD,2CAA2C;QAC3C,IAAI,KAAK,YAAY,2DAA2B;YAC5C,KAAK,YAAY,yCAAkB,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,+DAA+D;QAC/D,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CACpC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAC7C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,WAAW,CAAI,OAAmB,EAAE,SAAiB;QAC3D,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,OAAO;YACP,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBAC/B,UAAU,CAAC,GAAG,EAAE;oBACd,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,SAAS,IAAI,CAAC,CAAC,CAAC;gBAChE,CAAC,EAAE,SAAS,CAAC,CAAC;YAChB,CAAC,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,UAAkB,EAAE,QAAgB,EAAE,OAAgB;QAC1E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;YAElC,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,CAAC;YAED,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;YACtG,OAAO,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YAEnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,UAAkB,EAAE,MAAwB;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,aAAa,IAAI,MAAM,CAAC,QAAQ,CAAC;YACzC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;YACxC,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;YACtG,OAAO,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YAEnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,UAAkB;QACxC,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,8BAA8B,CACnC,cAAsB,CAAC,EACvB,YAAoB,IAAI,EACxB,kBAA4B,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,CAAC;QAE/E,OAAO;YACL,WAAW;YACX,SAAS;YACT,QAAQ,EAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC;YAClD,iBAAiB,EAAE,CAAC;YACpB,MAAM,EAAE,IAAI;YACZ,eAAe;SAChB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,yBAAyB,CAC9B,cAAsB,CAAC,EACvB,QAAgB,IAAI,EACpB,kBAA4B,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,CAAC;QAE/E,OAAO;YACL,WAAW;YACX,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,KAAK;YACf,iBAAiB,EAAE,CAAC;YACpB,MAAM,EAAE,KAAK;YACb,eAAe;SAChB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,sBAAsB,CAC3B,cAAsB,CAAC,EACvB,QAAgB,IAAI,EACpB,kBAA4B,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,CAAC;QAE/E,OAAO;YACL,WAAW;YACX,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,KAAK;YACf,iBAAiB,EAAE,CAAC;YACpB,MAAM,EAAE,KAAK;YACb,eAAe;SAChB,CAAC;IACJ,CAAC;CACF,CAAA;AA9TY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;GACA,cAAc,CA8T1B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\retry.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { RetryStrategy, RetryOptions, RetryResult } from '@/shared-kernel/patterns/retry-strategy';\r\nimport { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';\r\nimport { RateLimitException } from '@/shared-kernel/exceptions/rate-limit.exception';\r\n\r\nexport interface AIRetryConfig {\r\n  maxAttempts: number;\r\n  baseDelay: number;\r\n  maxDelay: number;\r\n  backoffMultiplier: number;\r\n  jitter: boolean;\r\n  retryableErrors: string[];\r\n  timeoutMs?: number;\r\n}\r\n\r\nexport interface AIRetryMetrics {\r\n  providerId: string;\r\n  totalAttempts: number;\r\n  successfulRetries: number;\r\n  failedRetries: number;\r\n  averageAttempts: number;\r\n  totalDelay: number;\r\n  lastRetryTime?: Date;\r\n}\r\n\r\n/**\r\n * AI-specific retry service for managing AI provider resilience\r\n */\r\n@Injectable()\r\nexport class AIRetryService {\r\n  private readonly logger = new Logger(AIRetryService.name);\r\n  private readonly retryStrategies = new Map<string, RetryStrategy>();\r\n  private readonly configs = new Map<string, AIRetryConfig>();\r\n  private readonly metrics = new Map<string, AIRetryMetrics>();\r\n\r\n  /**\r\n   * Register a retry strategy for an AI provider\r\n   */\r\n  registerProvider(\r\n    providerId: string,\r\n    providerType: string,\r\n    config: AIRetryConfig\r\n  ): void {\r\n    const retryOptions: RetryOptions = {\r\n      maxAttempts: config.maxAttempts,\r\n      baseDelay: config.baseDelay,\r\n      maxDelay: config.maxDelay,\r\n      backoffMultiplier: config.backoffMultiplier,\r\n      jitter: config.jitter,\r\n      retryOn: (error: Error) => this.shouldRetry(error, config.retryableErrors),\r\n      onRetry: (error: Error, attempt: number) => {\r\n        this.logger.warn(\r\n          `Retry attempt ${attempt} for ${providerType} provider ${providerId}: ${error.message}`\r\n        );\r\n      },\r\n    };\r\n\r\n    const retryStrategy = new RetryStrategy(retryOptions);\r\n    this.retryStrategies.set(providerId, retryStrategy);\r\n    this.configs.set(providerId, config);\r\n    \r\n    // Initialize metrics\r\n    this.metrics.set(providerId, {\r\n      providerId,\r\n      totalAttempts: 0,\r\n      successfulRetries: 0,\r\n      failedRetries: 0,\r\n      averageAttempts: 0,\r\n      totalDelay: 0,\r\n    });\r\n\r\n    this.logger.log(\r\n      `Registered retry strategy for ${providerType} provider ${providerId} with max ${config.maxAttempts} attempts`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Execute an AI operation with retry logic\r\n   */\r\n  async executeWithRetry<T>(\r\n    providerId: string,\r\n    operation: () => Promise<T>,\r\n    timeoutMs?: number\r\n  ): Promise<T> {\r\n    const retryStrategy = this.retryStrategies.get(providerId);\r\n    if (!retryStrategy) {\r\n      throw new Error(`Retry strategy not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    const config = this.configs.get(providerId);\r\n    const operationTimeout = timeoutMs || config?.timeoutMs;\r\n\r\n    try {\r\n      let wrappedOperation = operation;\r\n      \r\n      // Add timeout wrapper if specified\r\n      if (operationTimeout) {\r\n        wrappedOperation = () => this.withTimeout(operation(), operationTimeout);\r\n      }\r\n\r\n      const result = await retryStrategy.execute(wrappedOperation);\r\n      this.updateMetrics(providerId, 1, true);\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Retry execution failed for provider ${providerId} after all attempts`,\r\n        error\r\n      );\r\n      this.updateMetrics(providerId, config?.maxAttempts || 1, false);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute with detailed retry information\r\n   */\r\n  async executeWithRetryDetails<T>(\r\n    providerId: string,\r\n    operation: () => Promise<T>,\r\n    timeoutMs?: number\r\n  ): Promise<RetryResult<T>> {\r\n    const retryStrategy = this.retryStrategies.get(providerId);\r\n    if (!retryStrategy) {\r\n      throw new Error(`Retry strategy not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    const config = this.configs.get(providerId);\r\n    const operationTimeout = timeoutMs || config?.timeoutMs;\r\n\r\n    try {\r\n      let wrappedOperation = operation;\r\n      \r\n      // Add timeout wrapper if specified\r\n      if (operationTimeout) {\r\n        wrappedOperation = () => this.withTimeout(operation(), operationTimeout);\r\n      }\r\n\r\n      const result = await retryStrategy.executeWithDetails(wrappedOperation);\r\n      this.updateMetricsWithDetails(providerId, result);\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Retry execution with details failed for provider ${providerId}`,\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get retry metrics for a specific provider\r\n   */\r\n  getProviderMetrics(providerId: string): AIRetryMetrics | null {\r\n    return this.metrics.get(providerId) || null;\r\n  }\r\n\r\n  /**\r\n   * Get metrics for all registered providers\r\n   */\r\n  getAllProviderMetrics(): AIRetryMetrics[] {\r\n    return Array.from(this.metrics.values());\r\n  }\r\n\r\n  /**\r\n   * Reset metrics for a specific provider\r\n   */\r\n  resetProviderMetrics(providerId: string): void {\r\n    const metrics = this.metrics.get(providerId);\r\n    if (metrics) {\r\n      this.metrics.set(providerId, {\r\n        providerId,\r\n        totalAttempts: 0,\r\n        successfulRetries: 0,\r\n        failedRetries: 0,\r\n        averageAttempts: 0,\r\n        totalDelay: 0,\r\n      });\r\n      this.logger.log(`Reset retry metrics for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset metrics for all providers\r\n   */\r\n  resetAllMetrics(): void {\r\n    for (const [providerId] of this.metrics) {\r\n      this.resetProviderMetrics(providerId);\r\n    }\r\n    this.logger.log('Reset retry metrics for all providers');\r\n  }\r\n\r\n  /**\r\n   * Update provider configuration\r\n   */\r\n  updateProviderConfig(providerId: string, config: Partial<AIRetryConfig>): void {\r\n    const existingConfig = this.configs.get(providerId);\r\n    if (existingConfig) {\r\n      const updatedConfig = { ...existingConfig, ...config };\r\n      this.configs.set(providerId, updatedConfig);\r\n      \r\n      // Re-register with new config\r\n      const providerType = this.getProviderType(providerId);\r\n      this.registerProvider(providerId, providerType, updatedConfig);\r\n      \r\n      this.logger.log(`Updated retry configuration for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a provider's retry strategy\r\n   */\r\n  unregisterProvider(providerId: string): void {\r\n    this.retryStrategies.delete(providerId);\r\n    this.configs.delete(providerId);\r\n    this.metrics.delete(providerId);\r\n    this.logger.log(`Unregistered retry strategy for provider ${providerId}`);\r\n  }\r\n\r\n  /**\r\n   * Get provider configuration\r\n   */\r\n  getProviderConfig(providerId: string): AIRetryConfig | null {\r\n    return this.configs.get(providerId) || null;\r\n  }\r\n\r\n  /**\r\n   * Check if an error should be retried based on configuration\r\n   */\r\n  private shouldRetry(error: Error, retryableErrors: string[]): boolean {\r\n    // Always retry on specific exception types\r\n    if (error instanceof ServiceUnavailableException || \r\n        error instanceof RateLimitException) {\r\n      return true;\r\n    }\r\n\r\n    // Check if error message contains any retryable error patterns\r\n    const errorMessage = error.message.toLowerCase();\r\n    return retryableErrors.some(pattern => \r\n      errorMessage.includes(pattern.toLowerCase())\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Add timeout wrapper to operation\r\n   */\r\n  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {\r\n    return Promise.race([\r\n      promise,\r\n      new Promise<never>((_, reject) => {\r\n        setTimeout(() => {\r\n          reject(new Error(`Operation timed out after ${timeoutMs}ms`));\r\n        }, timeoutMs);\r\n      }),\r\n    ]);\r\n  }\r\n\r\n  /**\r\n   * Update metrics after retry attempt\r\n   */\r\n  private updateMetrics(providerId: string, attempts: number, success: boolean): void {\r\n    const metrics = this.metrics.get(providerId);\r\n    if (metrics) {\r\n      metrics.totalAttempts += attempts;\r\n      \r\n      if (success) {\r\n        metrics.successfulRetries++;\r\n      } else {\r\n        metrics.failedRetries++;\r\n      }\r\n      \r\n      metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);\r\n      metrics.lastRetryTime = new Date();\r\n      \r\n      this.metrics.set(providerId, metrics);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update metrics with detailed retry information\r\n   */\r\n  private updateMetricsWithDetails(providerId: string, result: RetryResult<any>): void {\r\n    const metrics = this.metrics.get(providerId);\r\n    if (metrics) {\r\n      metrics.totalAttempts += result.attempts;\r\n      metrics.successfulRetries++;\r\n      metrics.totalDelay += result.totalDelay;\r\n      metrics.averageAttempts = metrics.totalAttempts / (metrics.successfulRetries + metrics.failedRetries);\r\n      metrics.lastRetryTime = new Date();\r\n      \r\n      this.metrics.set(providerId, metrics);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract provider type from provider ID\r\n   */\r\n  private getProviderType(providerId: string): string {\r\n    return providerId.split('-')[0] || 'unknown';\r\n  }\r\n\r\n  /**\r\n   * Create predefined retry strategies\r\n   */\r\n  static createExponentialBackoffConfig(\r\n    maxAttempts: number = 3,\r\n    baseDelay: number = 1000,\r\n    retryableErrors: string[] = ['timeout', 'network', 'connection', 'unavailable']\r\n  ): AIRetryConfig {\r\n    return {\r\n      maxAttempts,\r\n      baseDelay,\r\n      maxDelay: baseDelay * Math.pow(2, maxAttempts - 1),\r\n      backoffMultiplier: 2,\r\n      jitter: true,\r\n      retryableErrors,\r\n    };\r\n  }\r\n\r\n  static createLinearBackoffConfig(\r\n    maxAttempts: number = 3,\r\n    delay: number = 1000,\r\n    retryableErrors: string[] = ['timeout', 'network', 'connection', 'unavailable']\r\n  ): AIRetryConfig {\r\n    return {\r\n      maxAttempts,\r\n      baseDelay: delay,\r\n      maxDelay: delay,\r\n      backoffMultiplier: 1,\r\n      jitter: false,\r\n      retryableErrors,\r\n    };\r\n  }\r\n\r\n  static createFixedDelayConfig(\r\n    maxAttempts: number = 3,\r\n    delay: number = 1000,\r\n    retryableErrors: string[] = ['timeout', 'network', 'connection', 'unavailable']\r\n  ): AIRetryConfig {\r\n    return {\r\n      maxAttempts,\r\n      baseDelay: delay,\r\n      maxDelay: delay,\r\n      backoffMultiplier: 1,\r\n      jitter: false,\r\n      retryableErrors,\r\n    };\r\n  }\r\n}"], "version": 3}