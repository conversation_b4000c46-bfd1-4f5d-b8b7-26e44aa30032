{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\circuit-breaker.service.ts", "mappings": ";;;;;;;;;;AAAA,2CAAoD;AACpD,8EAA6I;AAkB7I;;GAEG;AAEI,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAA7B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;QAClD,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;QACpD,YAAO,GAAG,IAAI,GAAG,EAAkC,CAAC;IA0NvE,CAAC;IAxNC;;OAEG;IACH,gBAAgB,CACd,UAAkB,EAClB,YAAoB,EACpB,MAA8B;QAE9B,MAAM,OAAO,GAA0B;YACrC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YACzC,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YACzC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YACzC,aAAa,EAAE,CAAC,KAA0B,EAAE,EAAE;gBAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,uBAAuB,YAAY,aAAa,UAAU,eAAe,KAAK,EAAE,CACjF,CAAC;YACJ,CAAC;SACF,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAErC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,kCAAkC,YAAY,aAAa,UAAU,EAAE,CACxE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,UAAkB,EAClB,SAA2B,EAC3B,UAA6B;QAE7B,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,gDAAgD,UAAU,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE5C,IAAI,CAAC;YACH,OAAO,MAAM,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iDAAiD,UAAU,EAAE,EAC7D,KAAK,CACN,CAAC;YAEF,wCAAwC;YACxC,IAAI,MAAM,EAAE,cAAc,IAAI,UAAU,EAAE,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,mCAAmC,UAAU,yBAAyB,CACvE,CAAC;gBACF,OAAO,MAAM,UAAU,EAAE,CAAC;YAC5B,CAAC;YAED,+CAA+C;YAC/C,IAAI,MAAM,EAAE,cAAc,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBACtD,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,mDAAmD,UAAU,EAAE,CAChE,CAAC;gBACF,OAAO,MAAM,CAAC,gBAAgB,CAAC;YACjC,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;QAC5C,OAAO;YACL,GAAG,OAAO;YACV,UAAU;YACV,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;SAC/C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,OAAO,GAAsC,EAAE,CAAC;QAEtD,KAAK,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChE,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC;gBACX,GAAG,eAAe;gBAClB,UAAU;gBACV,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;aAC/C,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,UAAkB;QACpC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;QAC5C,OAAO,OAAO,CAAC,KAAK,KAAK,qCAAmB,CAAC,IAAI,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,kBAAkB,GAAa,EAAE,CAAC;QAExC,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAkB;QAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,UAAU,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,KAAK,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChE,cAAc,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB,EAAE,MAAuC;QAC9E,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,MAAM,EAAE,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAE5C,8BAA8B;YAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,UAAU,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,eAAe;QACb,MAAM,MAAM,GAAqF,EAAE,CAAC;QAEpG,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAE,CAAC;YAErD,MAAM,CAAC,UAAU,CAAC,GAAG;gBACnB,SAAS;gBACT,OAAO;aACR,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,UAAkB;QACxC,2EAA2E;QAC3E,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;IAC/C,CAAC;CACF,CAAA;AA7NY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;GACA,uBAAuB,CA6NnC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\circuit-breaker.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { CircuitBreaker, CircuitBreakerOptions, CircuitBreakerState, CircuitBreakerMetrics } from '@/shared-kernel/patterns/circuit-breaker';\r\nimport { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';\r\n\r\nexport interface AICircuitBreakerConfig {\r\n  failureThreshold: number;\r\n  recoveryTimeout: number;\r\n  monitoringPeriod: number;\r\n  halfOpenMaxCalls: number;\r\n  enableFallback: boolean;\r\n  fallbackResponse?: any;\r\n}\r\n\r\nexport interface AIProviderCircuitBreakerMetrics extends CircuitBreakerMetrics {\r\n  providerId: string;\r\n  providerType: string;\r\n  lastError?: string;\r\n}\r\n\r\n/**\r\n * AI-specific circuit breaker service for managing AI provider resilience\r\n */\r\n@Injectable()\r\nexport class AICircuitBreakerService {\r\n  private readonly logger = new Logger(AICircuitBreakerService.name);\r\n  private readonly circuitBreakers = new Map<string, CircuitBreaker>();\r\n  private readonly configs = new Map<string, AICircuitBreakerConfig>();\r\n\r\n  /**\r\n   * Register a circuit breaker for an AI provider\r\n   */\r\n  registerProvider(\r\n    providerId: string,\r\n    providerType: string,\r\n    config: AICircuitBreakerConfig\r\n  ): void {\r\n    const options: CircuitBreakerOptions = {\r\n      failureThreshold: config.failureThreshold,\r\n      recoveryTimeout: config.recoveryTimeout,\r\n      monitoringPeriod: config.monitoringPeriod,\r\n      halfOpenMaxCalls: config.halfOpenMaxCalls,\r\n      onStateChange: (state: CircuitBreakerState) => {\r\n        this.logger.log(\r\n          `Circuit breaker for ${providerType} provider ${providerId} changed to ${state}`\r\n        );\r\n      },\r\n    };\r\n\r\n    const circuitBreaker = new CircuitBreaker(options);\r\n    this.circuitBreakers.set(providerId, circuitBreaker);\r\n    this.configs.set(providerId, config);\r\n\r\n    this.logger.log(\r\n      `Registered circuit breaker for ${providerType} provider ${providerId}`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Execute an AI operation with circuit breaker protection\r\n   */\r\n  async executeWithProtection<T>(\r\n    providerId: string,\r\n    operation: () => Promise<T>,\r\n    fallbackFn?: () => Promise<T>\r\n  ): Promise<T> {\r\n    const circuitBreaker = this.circuitBreakers.get(providerId);\r\n    if (!circuitBreaker) {\r\n      throw new Error(`Circuit breaker not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    const config = this.configs.get(providerId);\r\n    \r\n    try {\r\n      return await circuitBreaker.execute(operation);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Circuit breaker execution failed for provider ${providerId}`,\r\n        error\r\n      );\r\n\r\n      // Try fallback if enabled and available\r\n      if (config?.enableFallback && fallbackFn) {\r\n        this.logger.warn(\r\n          `Executing fallback for provider ${providerId} due to circuit breaker`\r\n        );\r\n        return await fallbackFn();\r\n      }\r\n\r\n      // Return cached fallback response if available\r\n      if (config?.enableFallback && config.fallbackResponse) {\r\n        this.logger.warn(\r\n          `Returning cached fallback response for provider ${providerId}`\r\n        );\r\n        return config.fallbackResponse;\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get circuit breaker metrics for a specific provider\r\n   */\r\n  getProviderMetrics(providerId: string): AIProviderCircuitBreakerMetrics | null {\r\n    const circuitBreaker = this.circuitBreakers.get(providerId);\r\n    if (!circuitBreaker) {\r\n      return null;\r\n    }\r\n\r\n    const metrics = circuitBreaker.getMetrics();\r\n    return {\r\n      ...metrics,\r\n      providerId,\r\n      providerType: this.getProviderType(providerId),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get metrics for all registered providers\r\n   */\r\n  getAllProviderMetrics(): AIProviderCircuitBreakerMetrics[] {\r\n    const metrics: AIProviderCircuitBreakerMetrics[] = [];\r\n    \r\n    for (const [providerId, circuitBreaker] of this.circuitBreakers) {\r\n      const providerMetrics = circuitBreaker.getMetrics();\r\n      metrics.push({\r\n        ...providerMetrics,\r\n        providerId,\r\n        providerType: this.getProviderType(providerId),\r\n      });\r\n    }\r\n\r\n    return metrics;\r\n  }\r\n\r\n  /**\r\n   * Check if a provider is available (circuit breaker is closed or half-open)\r\n   */\r\n  isProviderAvailable(providerId: string): boolean {\r\n    const circuitBreaker = this.circuitBreakers.get(providerId);\r\n    if (!circuitBreaker) {\r\n      return false;\r\n    }\r\n\r\n    const metrics = circuitBreaker.getMetrics();\r\n    return metrics.state !== CircuitBreakerState.OPEN;\r\n  }\r\n\r\n  /**\r\n   * Get all available providers\r\n   */\r\n  getAvailableProviders(): string[] {\r\n    const availableProviders: string[] = [];\r\n    \r\n    for (const [providerId] of this.circuitBreakers) {\r\n      if (this.isProviderAvailable(providerId)) {\r\n        availableProviders.push(providerId);\r\n      }\r\n    }\r\n\r\n    return availableProviders;\r\n  }\r\n\r\n  /**\r\n   * Reset circuit breaker for a specific provider\r\n   */\r\n  resetProvider(providerId: string): void {\r\n    const circuitBreaker = this.circuitBreakers.get(providerId);\r\n    if (circuitBreaker) {\r\n      circuitBreaker.reset();\r\n      this.logger.log(`Reset circuit breaker for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Force open circuit breaker for a specific provider\r\n   */\r\n  forceOpenProvider(providerId: string): void {\r\n    const circuitBreaker = this.circuitBreakers.get(providerId);\r\n    if (circuitBreaker) {\r\n      circuitBreaker.forceOpen();\r\n      this.logger.warn(`Forced open circuit breaker for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset all circuit breakers\r\n   */\r\n  resetAllProviders(): void {\r\n    for (const [providerId, circuitBreaker] of this.circuitBreakers) {\r\n      circuitBreaker.reset();\r\n      this.logger.log(`Reset circuit breaker for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update configuration for a provider\r\n   */\r\n  updateProviderConfig(providerId: string, config: Partial<AICircuitBreakerConfig>): void {\r\n    const existingConfig = this.configs.get(providerId);\r\n    if (existingConfig) {\r\n      const updatedConfig = { ...existingConfig, ...config };\r\n      this.configs.set(providerId, updatedConfig);\r\n      \r\n      // Re-register with new config\r\n      const providerType = this.getProviderType(providerId);\r\n      this.registerProvider(providerId, providerType, updatedConfig);\r\n      \r\n      this.logger.log(`Updated configuration for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a provider's circuit breaker\r\n   */\r\n  unregisterProvider(providerId: string): void {\r\n    this.circuitBreakers.delete(providerId);\r\n    this.configs.delete(providerId);\r\n    this.logger.log(`Unregistered circuit breaker for provider ${providerId}`);\r\n  }\r\n\r\n  /**\r\n   * Get health status of all providers\r\n   */\r\n  getHealthStatus(): Record<string, { available: boolean; metrics: AIProviderCircuitBreakerMetrics }> {\r\n    const status: Record<string, { available: boolean; metrics: AIProviderCircuitBreakerMetrics }> = {};\r\n    \r\n    for (const [providerId] of this.circuitBreakers) {\r\n      const available = this.isProviderAvailable(providerId);\r\n      const metrics = this.getProviderMetrics(providerId)!;\r\n      \r\n      status[providerId] = {\r\n        available,\r\n        metrics,\r\n      };\r\n    }\r\n\r\n    return status;\r\n  }\r\n\r\n  private getProviderType(providerId: string): string {\r\n    // Extract provider type from provider ID (e.g., \"openai-gpt4\" -> \"openai\")\r\n    return providerId.split('-')[0] || 'unknown';\r\n  }\r\n}"], "version": 3}