import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

// Import entities
import { AuditLog } from '../../../modules/user-management/domain/entities/audit-log.entity';

/**
 * Audit service for logging user actions and system events
 * Provides comprehensive audit trail for compliance and security
 */
@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
  ) {}

  /**
   * Log user action for audit trail
   * @param userId User ID performing the action
   * @param action Action performed
   * @param resourceType Type of resource affected
   * @param resourceId ID of the resource affected
   * @param details Additional details about the action
   * @param ipAddress User's IP address
   * @param userAgent User's browser/client information
   * @param correlationId Request correlation ID
   */
  async logUserAction(
    userId: string,
    action: string,
    resourceType: string,
    resourceId?: string,
    details?: any,
    ipAddress?: string,
    userAgent?: string,
    correlationId?: string,
  ): Promise<void> {
    try {
      const auditLog = this.auditLogRepository.create({
        userId,
        action,
        resourceType,
        resourceId,
        details: details ? JSON.stringify(details) : null,
        ipAddress,
        userAgent,
        correlationId,
        createdAt: new Date(),
      });

      await this.auditLogRepository.save(auditLog);

      this.logger.log('User action logged', {
        auditLogId: auditLog.id,
        userId,
        action,
        resourceType,
        resourceId,
        correlationId,
      });
    } catch (error) {
      this.logger.error('Failed to log user action', {
        userId,
        action,
        resourceType,
        resourceId,
        error: error.message,
      });
    }
  }

  /**
   * Log system event for audit trail
   * @param action System action performed
   * @param resourceType Type of resource affected
   * @param resourceId ID of the resource affected
   * @param details Additional details about the event
   * @param correlationId Request correlation ID
   */
  async logSystemEvent(
    action: string,
    resourceType: string,
    resourceId?: string,
    details?: any,
    correlationId?: string,
  ): Promise<void> {
    try {
      const auditLog = this.auditLogRepository.create({
        userId: null, // System events don't have a user
        action,
        resourceType,
        resourceId,
        details: details ? JSON.stringify(details) : null,
        ipAddress: null,
        userAgent: 'system',
        correlationId,
        createdAt: new Date(),
      });

      await this.auditLogRepository.save(auditLog);

      this.logger.log('System event logged', {
        auditLogId: auditLog.id,
        action,
        resourceType,
        resourceId,
        correlationId,
      });
    } catch (error) {
      this.logger.error('Failed to log system event', {
        action,
        resourceType,
        resourceId,
        error: error.message,
      });
    }
  }

  /**
   * Log authentication event
   * @param userId User ID (if known)
   * @param action Authentication action (login, logout, failed_login, etc.)
   * @param success Whether the action was successful
   * @param ipAddress User's IP address
   * @param userAgent User's browser/client information
   * @param details Additional details about the authentication attempt
   * @param correlationId Request correlation ID
   */
  async logAuthEvent(
    userId: string | null,
    action: string,
    success: boolean,
    ipAddress?: string,
    userAgent?: string,
    details?: any,
    correlationId?: string,
  ): Promise<void> {
    const auditDetails = {
      success,
      ...details,
    };

    await this.logUserAction(
      userId,
      action,
      'authentication',
      null,
      auditDetails,
      ipAddress,
      userAgent,
      correlationId,
    );
  }

  /**
   * Log data access event
   * @param userId User ID accessing the data
   * @param action Access action (read, export, etc.)
   * @param resourceType Type of data accessed
   * @param resourceId ID of the data accessed
   * @param recordCount Number of records accessed
   * @param ipAddress User's IP address
   * @param userAgent User's browser/client information
   * @param correlationId Request correlation ID
   */
  async logDataAccess(
    userId: string,
    action: string,
    resourceType: string,
    resourceId?: string,
    recordCount?: number,
    ipAddress?: string,
    userAgent?: string,
    correlationId?: string,
  ): Promise<void> {
    const details = {
      recordCount,
      accessTime: new Date().toISOString(),
    };

    await this.logUserAction(
      userId,
      action,
      resourceType,
      resourceId,
      details,
      ipAddress,
      userAgent,
      correlationId,
    );
  }

  /**
   * Log configuration change
   * @param userId User ID making the change
   * @param action Configuration action (create, update, delete)
   * @param configType Type of configuration changed
   * @param configId ID of the configuration
   * @param oldValue Previous value (for updates)
   * @param newValue New value
   * @param ipAddress User's IP address
   * @param userAgent User's browser/client information
   * @param correlationId Request correlation ID
   */
  async logConfigChange(
    userId: string,
    action: string,
    configType: string,
    configId: string,
    oldValue?: any,
    newValue?: any,
    ipAddress?: string,
    userAgent?: string,
    correlationId?: string,
  ): Promise<void> {
    const details = {
      oldValue: oldValue ? this.sanitizeValue(oldValue) : null,
      newValue: newValue ? this.sanitizeValue(newValue) : null,
      changeTime: new Date().toISOString(),
    };

    await this.logUserAction(
      userId,
      action,
      configType,
      configId,
      details,
      ipAddress,
      userAgent,
      correlationId,
    );
  }

  /**
   * Get audit logs for a specific user
   * @param userId User ID
   * @param limit Maximum number of logs to return
   * @param offset Number of logs to skip
   * @returns Promise<AuditLog[]>
   */
  async getUserAuditLogs(
    userId: string,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLog[]> {
    try {
      return await this.auditLogRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
        skip: offset,
      });
    } catch (error) {
      this.logger.error('Failed to get user audit logs', {
        userId,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * Get audit logs for a specific resource
   * @param resourceType Resource type
   * @param resourceId Resource ID
   * @param limit Maximum number of logs to return
   * @param offset Number of logs to skip
   * @returns Promise<AuditLog[]>
   */
  async getResourceAuditLogs(
    resourceType: string,
    resourceId: string,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLog[]> {
    try {
      return await this.auditLogRepository.find({
        where: { resourceType, resourceId },
        order: { createdAt: 'DESC' },
        take: limit,
        skip: offset,
      });
    } catch (error) {
      this.logger.error('Failed to get resource audit logs', {
        resourceType,
        resourceId,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * Get audit logs within a date range
   * @param startDate Start date
   * @param endDate End date
   * @param limit Maximum number of logs to return
   * @param offset Number of logs to skip
   * @returns Promise<AuditLog[]>
   */
  async getAuditLogsByDateRange(
    startDate: Date,
    endDate: Date,
    limit: number = 1000,
    offset: number = 0,
  ): Promise<AuditLog[]> {
    try {
      return await this.auditLogRepository
        .createQueryBuilder('audit_log')
        .where('audit_log.createdAt >= :startDate', { startDate })
        .andWhere('audit_log.createdAt <= :endDate', { endDate })
        .orderBy('audit_log.createdAt', 'DESC')
        .take(limit)
        .skip(offset)
        .getMany();
    } catch (error) {
      this.logger.error('Failed to get audit logs by date range', {
        startDate,
        endDate,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * Sanitize sensitive values before logging
   * @param value Value to sanitize
   * @returns Sanitized value
   */
  private sanitizeValue(value: any): any {
    if (typeof value !== 'object' || value === null) {
      return value;
    }

    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'credential',
    ];

    const sanitized = { ...value };
    
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
