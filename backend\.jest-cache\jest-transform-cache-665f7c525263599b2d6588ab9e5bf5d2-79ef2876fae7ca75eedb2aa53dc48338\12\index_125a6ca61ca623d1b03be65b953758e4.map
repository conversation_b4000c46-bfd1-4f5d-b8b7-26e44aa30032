{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\index.ts", "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;;;;;;;AAEH,sBAAsB;AACtB,oDAAkD;AAAzC,yGAAA,UAAU,OAAA;AACnB,oEAAiE;AAAxD,wHAAA,iBAAiB,OAAA;AAC1B,gEAA6D;AAApD,oHAAA,eAAe,OAAA;AACxB,sDAAoD;AAA3C,2GAAA,WAAW,OAAA;AAEpB,2BAA2B;AAC3B,+EAA4E;AAAnE,8HAAA,oBAAoB,OAAA;AAE7B,kEAKqC;AAJnC,uHAAA,iBAAiB,OAAA;AACjB,6HAAA,uBAAuB,OAAA;AACvB,8HAAA,wBAAwB,OAAA;AACxB,wHAAA,kBAAkB,OAAA;AAgBpB,gBAAgB;AAChB,uEAAoE;AAA3D,oHAAA,eAAe,OAAA;AACxB,+FAA+E;AAAtE,+HAAA,cAAc,OAAA;AACvB,iFAAmE;AAA1D,mHAAA,SAAS,OAAA;AAClB,2FAA4E;AAAnE,4HAAA,aAAa,OAAA;AACtB,iFAAkE;AAAzD,kHAAA,QAAQ,OAAA;AACjB,6EAA8D;AAArD,8GAAA,MAAM,OAAA;AACf,6EAA+D;AAAtD,+GAAA,OAAO,OAAA;AAEhB,6DAA6D;AAC7D,2CASsB;AARpB,6GAAA,eAAe,OAAA;AACf,iHAAA,mBAAmB,OAAA;AACnB,+GAAA,iBAAiB,OAAA;AACjB,mHAAA,qBAAqB,OAAA;AACrB,gHAAA,kBAAkB,OAAA;AAClB,+GAAA,iBAAiB,OAAA;AACjB,gHAAA,kBAAkB,OAAA;AAClB,yHAAA,2BAA2B,OAAA;AA8C7B,+BAA+B;AAC/B,0CAAwB;AAExB,6DAA6D;AAC7D,gEAMsC;AALpC,wGAAA,KAAK,OAAA;AACL,8GAAA,WAAW,OAAA;AACX,+GAAA,YAAY,OAAA;AACZ,8GAAA,WAAW,OAAA;AACX,2GAAA,QAAQ,OAAA;AAGV,gEAIsC;AAHpC,wGAAA,KAAK,OAAA;AACL,kHAAA,eAAe,OAAA;AACf,gHAAA,aAAa,OAAA;AAGf,gEAKsC;AAJpC,wGAAA,KAAK,OAAA;AACL,qHAAA,kBAAkB,OAAA;AAClB,gHAAA,aAAa,OAAA;AACb,+GAAA,YAAY,OAAA;AAGd,0EAK2C;AAJzC,iHAAA,SAAS,OAAA;AACT,qHAAA,aAAa,OAAA;AACb,uHAAA,eAAe,OAAA;AACf,mHAAA,WAAW,OAAA;AAGb,yDAAyD;AACzD,8DAKoC;AAJlC,iHAAA,cAAc,OAAA;AACd,2HAAA,wBAAwB,OAAA;AACxB,sHAAA,mBAAmB,OAAA;AACnB,wHAAA,qBAAqB,OAAA;AAGvB,4DAImC;AAHjC,+GAAA,aAAa,OAAA;AACb,iHAAA,eAAe,OAAA;AACf,6GAAA,WAAW,OAAA;AAGb,4DAOmC;AANjC,+GAAA,aAAa,OAAA;AACb,uHAAA,qBAAqB,OAAA;AACrB,oHAAA,kBAAkB,OAAA;AAClB,yHAAA,uBAAuB,OAAA;AACvB,4GAAA,UAAU,OAAA;AACV,8GAAA,YAAY,OAAA;AAGd,oEAKuC;AAJrC,sHAAA,gBAAgB,OAAA;AAChB,6HAAA,uBAAuB,OAAA;AACvB,qHAAA,eAAe,OAAA;AACf,iHAAA,WAAW,OAAA;AAGb,oEAOuC;AANrC,sHAAA,gBAAgB,OAAA;AAChB,uHAAA,iBAAiB,OAAA;AACjB,mHAAA,aAAa,OAAA;AACb,2HAAA,qBAAqB,OAAA;AACrB,kHAAA,YAAY,OAAA;AACZ,sHAAA,gBAAgB,OAAA;AAGlB,sEAWwC;AAVtC,yHAAA,kBAAkB,OAAA;AAClB,iHAAA,UAAU,OAAA;AACV,wHAAA,iBAAiB,OAAA;AACjB,mHAAA,YAAY,OAAA;AACZ,kHAAA,WAAW,OAAA;AACX,uHAAA,gBAAgB,OAAA;AAChB,uHAAA,gBAAgB,OAAA;AAChB,kHAAA,WAAW,OAAA;AACX,8GAAA,OAAO,OAAA;AACP,iHAAA,UAAU,OAAA;AAGZ,8EAM4C;AAL1C,iIAAA,sBAAsB,OAAA;AACtB,+HAAA,oBAAoB,OAAA;AACpB,mIAAA,wBAAwB,OAAA;AACxB,qHAAA,UAAU,OAAA;AACV,uHAAA,YAAY,OAAA", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\index.ts"], "sourcesContent": ["/**\r\n * Shared Kernel Index\r\n * \r\n * This file exports all the shared kernel components for easy importing\r\n * across the application. The shared kernel provides foundational domain-driven\r\n * design patterns and utilities used throughout the system.\r\n */\r\n\r\n// Domain Base Classes\r\nexport { BaseEntity } from './domain/base-entity';\r\nexport { BaseAggregateRoot } from './domain/base-aggregate-root';\r\nexport { BaseDomainEvent } from './domain/base-domain-event';\r\nexport { BaseService } from './domain/base-service';\r\n\r\n// Integration Base Classes\r\nexport { BaseIntegrationEvent } from './integration/base-integration-event';\r\nexport type { ServiceContext, ServiceResult } from './domain/base-service';\r\nexport { \r\n  BaseSpecification, \r\n  AlwaysTrueSpecification, \r\n  AlwaysFalseSpecification,\r\n  SpecificationUtils\r\n} from './domain/base-specification';\r\n\r\n// Repository Interfaces\r\nexport type {\r\n  BaseRepository,\r\n  AggregateRepository,\r\n  RepositoryFactory,\r\n  RepositoryConfig,\r\n  RepositoryMetrics,\r\n  PaginationOptions,\r\n  SortOptions,\r\n  QueryOptions,\r\n  PaginatedResult\r\n} from './domain/base-repository.interface';\r\n\r\n// Value Objects\r\nexport { BaseValueObject } from './value-objects/base-value-object';\r\nexport { UniqueEntityId } from './value-objects/unique-entity-id.value-object';\r\nexport { Timestamp } from './value-objects/timestamp.value-object';\r\nexport { CorrelationId } from './value-objects/correlation-id.value-object';\r\nexport { TenantId } from './value-objects/tenant-id.value-object';\r\nexport { UserId } from './value-objects/user-id.value-object';\r\nexport { Version } from './value-objects/version.value-object';\r\n\r\n// Exceptions - Export specific exceptions to avoid conflicts\r\nexport { \r\n  DomainException,\r\n  ValidationException,\r\n  NotFoundException,\r\n  UnauthorizedException,\r\n  ForbiddenException,\r\n  ConflictException,\r\n  RateLimitException,\r\n  ServiceUnavailableException\r\n} from './exceptions';\r\n\r\n// Types - Export specific types to avoid conflicts\r\nexport type {\r\n  PaginationParams,\r\n  PaginationMeta,\r\n  CursorPaginationParams,\r\n  CursorPaginatedResult\r\n} from './types/pagination.types';\r\n\r\nexport type {\r\n  FilterCondition,\r\n  FilterExpression,\r\n  FilterParams,\r\n  DateRangeFilter,\r\n  NumericRangeFilter\r\n} from './types/filter.types';\r\n\r\nexport type {\r\n  SortField,\r\n  SortParams,\r\n  SortDirection\r\n} from './types/sort.types';\r\n\r\nexport type {\r\n  BaseApiResponse,\r\n  SuccessResponse,\r\n  ErrorResponse,\r\n  PaginatedResponse,\r\n  ErrorDetail,\r\n  ValidationError\r\n} from './types/response.types';\r\n\r\nexport type {\r\n  AuditEntry,\r\n  AuditUserContext,\r\n  AuditSystemContext,\r\n  AuditResourceContext,\r\n  AuditQueryParams\r\n} from './types/audit.types';\r\n\r\nexport type {\r\n  SecurityContext\r\n} from './types/security.types';\r\n\r\n// Utils - Export all utilities\r\nexport * from './utils';\r\n\r\n// Decorators - Export specific decorators to avoid conflicts\r\nexport {\r\n  Audit,\r\n  SimpleAudit,\r\n  AuditContext,\r\n  AuditLogger,\r\n  AuditLog,\r\n} from './decorators/audit.decorator';\r\n\r\nexport {\r\n  Cache,\r\n  CacheInvalidate,\r\n  CacheEvictAll,\r\n} from './decorators/cache.decorator';\r\n\r\nexport {\r\n  Retry,\r\n  ExponentialBackoff,\r\n  LinearBackoff,\r\n  RetryOnError,\r\n} from './decorators/retry.decorator';\r\n\r\nexport {\r\n  RateLimit,\r\n  UserRateLimit,\r\n  GlobalRateLimit,\r\n  IpRateLimit,\r\n} from './decorators/rate-limit.decorator';\r\n\r\n// Patterns - Export specific patterns to avoid conflicts\r\nexport {\r\n  CircuitBreaker,\r\n  CircuitBreakerProtection,\r\n  CircuitBreakerState,\r\n  CircuitBreakerMetrics,\r\n} from './patterns/circuit-breaker';\r\n\r\nexport {\r\n  RetryStrategy,\r\n  RetryStrategies,\r\n  RetryResult,\r\n} from './patterns/retry-strategy';\r\n\r\nexport {\r\n  CacheStrategy,\r\n  InMemoryCacheStrategy,\r\n  RedisCacheStrategy,\r\n  MultiLevelCacheStrategy,\r\n  CacheEntry,\r\n  CacheMetrics,\r\n} from './patterns/cache-strategy';\r\n\r\nexport {\r\n  ValueObjectCache,\r\n  ValueObjectCacheManager,\r\n  CacheStatistics,\r\n  CacheConfig,\r\n} from './patterns/value-object-cache';\r\n\r\nexport {\r\n  DomainEventBatch,\r\n  EventBatchFactory,\r\n  EventPriority,\r\n  BatchProcessingResult,\r\n  BatchMetrics,\r\n  EventBatchConfig,\r\n} from './patterns/domain-event-batch';\r\n\r\nexport {\r\n  PerformanceMonitor,\r\n  MetricType,\r\n  PerformanceMetric,\r\n  TimingResult,\r\n  AlertConfig,\r\n  PerformanceAlert,\r\n  PerformanceStats,\r\n  MemoryUsage,\r\n  Monitor,\r\n  CountCalls,\r\n} from './patterns/performance-monitor';\r\n\r\nexport {\r\n  OptimizedSpecification,\r\n  SpecificationBuilder,\r\n  QueryPerformanceAnalyzer,\r\n  QueryHints,\r\n  QueryMetrics,\r\n} from './patterns/optimized-specification';"], "version": 3}