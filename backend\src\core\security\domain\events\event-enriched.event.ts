import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';
import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';

/**
 * Event Enriched Event Data
 */
export interface EventEnrichedEventData {
  /** ID of the normalized event */
  normalizedEventId: string;
  /** ID of the enriched event */
  enrichedEventId: string;
  /** Enrichment quality score (0-100) */
  enrichmentScore: number;
  /** Number of threat indicators found */
  indicatorCount: number;
  /** Enrichment sources used */
  sourcesUsed: string[];
  /** Whether enrichment had errors */
  hasErrors: boolean;
  /** Processing duration in milliseconds */
  processingDuration: number;
  /** When the enrichment occurred */
  timestamp: string;
  /** Enrichment metadata */
  metadata?: {
    engineVersion: string;
    cacheHitRate: number;
    sourceContributions: Record<string, number>;
    threatIntelligenceHits: number;
    geolocationHits: number;
    assetContextHits: number;
    userContextHits: number;
  };
}

/**
 * Event Enriched Domain Event
 * 
 * Published when a normalized event has been successfully enriched with additional context.
 * This event triggers correlation analysis and threat assessment workflows.
 * 
 * Key use cases:
 * - Initiate correlation processing
 * - Trigger threat assessment analysis
 * - Update enrichment metrics and dashboards
 * - Send notifications for high-risk indicators
 * - Begin behavioral analysis
 * - Update threat intelligence feeds
 */
export class EventEnrichedEvent extends BaseDomainEvent<EventEnrichedEventData> {
  constructor(
    aggregateId: UniqueEntityId,
    eventData: EventEnrichedEventData,
    options?: {
      eventId?: UniqueEntityId;
      occurredOn?: Date;
      correlationId?: string;
      causationId?: string;
      metadata?: Record<string, any>;
    }
  ) {
    super(aggregateId, eventData, {
      eventVersion: 1,
      ...options,
      metadata: {
        eventType: 'EventEnriched',
        domain: 'Security',
        aggregateType: 'EnrichedEvent',
        ...options?.metadata,
      },
    });
  }

  /**
   * Get the normalized event ID
   */
  get normalizedEventId(): string {
    return this.eventData.normalizedEventId;
  }

  /**
   * Get the enriched event ID
   */
  get enrichedEventId(): string {
    return this.eventData.enrichedEventId;
  }

  /**
   * Get the enrichment score
   */
  get enrichmentScore(): number {
    return this.eventData.enrichmentScore;
  }

  /**
   * Get the indicator count
   */
  get indicatorCount(): number {
    return this.eventData.indicatorCount;
  }

  /**
   * Get sources used for enrichment
   */
  get sourcesUsed(): string[] {
    return this.eventData.sourcesUsed;
  }

  /**
   * Check if enrichment had errors
   */
  get hasErrors(): boolean {
    return this.eventData.hasErrors;
  }

  /**
   * Get processing duration
   */
  get processingDuration(): number {
    return this.eventData.processingDuration;
  }

  /**
   * Get enrichment timestamp
   */
  get enrichmentTimestamp(): string {
    return this.eventData.timestamp;
  }

  /**
   * Check if enrichment was successful
   */
  isEnrichmentSuccessful(): boolean {
    return this.eventData.enrichmentScore >= 50 && !this.hasHighSeverityErrors();
  }

  /**
   * Check if enrichment quality is high
   */
  hasHighQualityEnrichment(): boolean {
    return this.eventData.enrichmentScore >= 80;
  }

  /**
   * Check if enrichment quality is low
   */
  hasLowQualityEnrichment(): boolean {
    return this.eventData.enrichmentScore < 30;
  }

  /**
   * Check if threat indicators were found
   */
  hasThreatIndicators(): boolean {
    return this.eventData.indicatorCount > 0;
  }

  /**
   * Check if multiple threat indicators were found
   */
  hasMultipleThreatIndicators(): boolean {
    return this.eventData.indicatorCount > 1;
  }

  /**
   * Check if high number of threat indicators were found
   */
  hasHighThreatIndicatorCount(): boolean {
    return this.eventData.indicatorCount >= 5;
  }

  /**
   * Check if processing was fast
   */
  wasFastProcessing(): boolean {
    return this.eventData.processingDuration < 2000; // Less than 2 seconds
  }

  /**
   * Check if processing was slow
   */
  wasSlowProcessing(): boolean {
    return this.eventData.processingDuration > 15000; // More than 15 seconds
  }

  /**
   * Check if enrichment had high severity errors
   */
  hasHighSeverityErrors(): boolean {
    // This would be determined by the error metadata if available
    return this.eventData.hasErrors && this.eventData.enrichmentScore < 20;
  }

  /**
   * Get processing performance category
   */
  getProcessingPerformance(): 'fast' | 'normal' | 'slow' | 'very_slow' {
    const duration = this.eventData.processingDuration;
    if (duration < 2000) return 'fast';
    if (duration < 8000) return 'normal';
    if (duration < 20000) return 'slow';
    return 'very_slow';
  }

  /**
   * Get enrichment quality category
   */
  getEnrichmentQuality(): 'excellent' | 'good' | 'fair' | 'poor' {
    const score = this.eventData.enrichmentScore;
    if (score >= 90) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'fair';
    return 'poor';
  }

  /**
   * Get threat indicator severity
   */
  getThreatIndicatorSeverity(): 'none' | 'low' | 'medium' | 'high' | 'critical' {
    if (this.eventData.indicatorCount === 0) return 'none';
    if (this.eventData.indicatorCount === 1) return 'low';
    if (this.eventData.indicatorCount <= 3) return 'medium';
    if (this.eventData.indicatorCount <= 7) return 'high';
    return 'critical';
  }

  /**
   * Check if event should proceed to correlation
   */
  shouldProceedToCorrelation(): boolean {
    return this.isEnrichmentSuccessful() && 
           (this.hasThreatIndicators() || this.eventData.enrichmentScore >= 60);
  }

  /**
   * Check if event requires immediate attention
   */
  requiresImmediateAttention(): boolean {
    return this.hasHighThreatIndicatorCount() || 
           (this.hasThreatIndicators() && this.hasHighQualityEnrichment());
  }

  /**
   * Check if event requires quality review
   */
  requiresQualityReview(): boolean {
    return this.hasLowQualityEnrichment() || this.hasHighSeverityErrors();
  }

  /**
   * Get correlation priority
   */
  getCorrelationPriority(): 'low' | 'normal' | 'high' | 'critical' {
    if (this.requiresImmediateAttention()) {
      return 'critical';
    }
    if (this.hasThreatIndicators() && this.hasHighQualityEnrichment()) {
      return 'high';
    }
    if (this.isEnrichmentSuccessful()) {
      return 'normal';
    }
    return 'low';
  }

  /**
   * Get threat assessment priority
   */
  getThreatAssessmentPriority(): 'low' | 'normal' | 'high' | 'critical' {
    const indicatorSeverity = this.getThreatIndicatorSeverity();
    
    if (indicatorSeverity === 'critical') return 'critical';
    if (indicatorSeverity === 'high') return 'high';
    if (this.hasThreatIndicators()) return 'normal';
    return 'low';
  }

  /**
   * Get notification configuration
   */
  getNotificationConfiguration(): {
    shouldNotify: boolean;
    channels: string[];
    urgency: 'low' | 'normal' | 'high' | 'critical';
    reason: string;
  } {
    if (this.requiresImmediateAttention()) {
      return {
        shouldNotify: true,
        channels: ['email', 'slack', 'webhook', 'sms'],
        urgency: 'critical',
        reason: 'High number of threat indicators detected',
      };
    }

    if (this.hasThreatIndicators() && this.hasHighQualityEnrichment()) {
      return {
        shouldNotify: true,
        channels: ['email', 'slack', 'webhook'],
        urgency: 'high',
        reason: 'Threat indicators found with high-quality enrichment',
      };
    }

    if (this.requiresQualityReview()) {
      return {
        shouldNotify: true,
        channels: ['email', 'webhook'],
        urgency: 'normal',
        reason: 'Low quality enrichment or errors detected',
      };
    }

    if (this.hasThreatIndicators()) {
      return {
        shouldNotify: true,
        channels: ['webhook'],
        urgency: 'normal',
        reason: 'Threat indicators detected',
      };
    }

    return {
      shouldNotify: false,
      channels: ['webhook'],
      urgency: 'low',
      reason: 'Standard enrichment completed',
    };
  }

  /**
   * Get metrics tags for this event
   */
  getMetricsTags(): Record<string, string> {
    return {
      event_type: 'event_enriched',
      enrichment_quality: this.getEnrichmentQuality(),
      performance_category: this.getProcessingPerformance(),
      threat_indicator_severity: this.getThreatIndicatorSeverity(),
      has_errors: this.eventData.hasErrors.toString(),
      is_successful: this.isEnrichmentSuccessful().toString(),
      has_threat_indicators: this.hasThreatIndicators().toString(),
      correlation_priority: this.getCorrelationPriority(),
      threat_assessment_priority: this.getThreatAssessmentPriority(),
      sources_count: this.eventData.sourcesUsed.length.toString(),
    };
  }

  /**
   * Get audit log entry
   */
  getAuditLogEntry(): {
    action: string;
    resource: string;
    resourceId: string;
    details: Record<string, any>;
    timestamp: string;
  } {
    return {
      action: 'event_enriched',
      resource: 'EnrichedEvent',
      resourceId: this.eventData.enrichedEventId,
      details: {
        normalizedEventId: this.eventData.normalizedEventId,
        enrichmentScore: this.eventData.enrichmentScore,
        indicatorCount: this.eventData.indicatorCount,
        sourcesUsed: this.eventData.sourcesUsed,
        hasErrors: this.eventData.hasErrors,
        processingDuration: this.eventData.processingDuration,
        isSuccessful: this.isEnrichmentSuccessful(),
        enrichmentQuality: this.getEnrichmentQuality(),
        threatIndicatorSeverity: this.getThreatIndicatorSeverity(),
        shouldProceedToCorrelation: this.shouldProceedToCorrelation(),
        requiresImmediateAttention: this.requiresImmediateAttention(),
      },
      timestamp: this.occurredOn.toISOString(),
    };
  }

  /**
   * Create integration event for external systems
   */
  toIntegrationEvent(): {
    eventType: string;
    version: string;
    timestamp: string;
    data: {
      normalizedEventId: string;
      enrichedEventId: string;
      enrichment: {
        score: number;
        indicatorCount: number;
        sourcesUsed: string[];
        hasErrors: boolean;
        processingDuration: number;
      };
      flags: {
        isSuccessful: boolean;
        hasHighQuality: boolean;
        hasThreatIndicators: boolean;
        shouldProceedToCorrelation: boolean;
        requiresImmediateAttention: boolean;
      };
      priority: {
        correlation: string;
        threatAssessment: string;
        quality: string;
        performance: string;
      };
    };
    metadata: {
      correlationId?: string;
      causationId?: string;
      domain: string;
      aggregateType: string;
    };
  } {
    return {
      eventType: 'EventEnriched',
      version: '1.0',
      timestamp: this.occurredOn.toISOString(),
      data: {
        normalizedEventId: this.eventData.normalizedEventId,
        enrichedEventId: this.eventData.enrichedEventId,
        enrichment: {
          score: this.eventData.enrichmentScore,
          indicatorCount: this.eventData.indicatorCount,
          sourcesUsed: this.eventData.sourcesUsed,
          hasErrors: this.eventData.hasErrors,
          processingDuration: this.eventData.processingDuration,
        },
        flags: {
          isSuccessful: this.isEnrichmentSuccessful(),
          hasHighQuality: this.hasHighQualityEnrichment(),
          hasThreatIndicators: this.hasThreatIndicators(),
          shouldProceedToCorrelation: this.shouldProceedToCorrelation(),
          requiresImmediateAttention: this.requiresImmediateAttention(),
        },
        priority: {
          correlation: this.getCorrelationPriority(),
          threatAssessment: this.getThreatAssessmentPriority(),
          quality: this.getEnrichmentQuality(),
          performance: this.getProcessingPerformance(),
        },
      },
      metadata: {
        correlationId: this.correlationId,
        causationId: this.causationId,
        domain: 'Security',
        aggregateType: 'EnrichedEvent',
      },
    };
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      eventData: this.eventData,
      analysis: {
        isEnrichmentSuccessful: this.isEnrichmentSuccessful(),
        hasHighQualityEnrichment: this.hasHighQualityEnrichment(),
        hasThreatIndicators: this.hasThreatIndicators(),
        shouldProceedToCorrelation: this.shouldProceedToCorrelation(),
        requiresImmediateAttention: this.requiresImmediateAttention(),
        requiresQualityReview: this.requiresQualityReview(),
        enrichmentQuality: this.getEnrichmentQuality(),
        performanceCategory: this.getProcessingPerformance(),
        threatIndicatorSeverity: this.getThreatIndicatorSeverity(),
        correlationPriority: this.getCorrelationPriority(),
        threatAssessmentPriority: this.getThreatAssessmentPriority(),
        notificationConfiguration: this.getNotificationConfiguration(),
        metricsTags: this.getMetricsTags(),
      },
    };
  }

  /**
   * Create from JSON representation
   */
  static fromJSON(json: Record<string, any>): EventEnrichedEvent {
    return new EventEnrichedEvent(
      UniqueEntityId.fromString(json.aggregateId),
      json.eventData,
      {
        eventId: UniqueEntityId.fromString(json.eventId),
        occurredOn: new Date(json.occurredOn),
        correlationId: json.correlationId,
        causationId: json.causationId,
        metadata: json.metadata,
      }
    );
  }

  /**
   * Get human-readable description
   */
  getDescription(): string {
    const quality = this.getEnrichmentQuality();
    const performance = this.getProcessingPerformance();
    const indicatorText = this.eventData.indicatorCount > 0 
      ? ` with ${this.eventData.indicatorCount} threat indicators` 
      : '';
    const errorText = this.eventData.hasErrors ? ' with errors' : '';
    
    return `Event enriched with ${quality} quality (${this.eventData.enrichmentScore}%) and ${performance} performance (${this.eventData.processingDuration}ms)${indicatorText}${errorText}`;
  }

  /**
   * Get event summary for logging
   */
  getSummary(): {
    eventType: string;
    normalizedEventId: string;
    enrichedEventId: string;
    enrichmentScore: number;
    indicatorCount: number;
    sourcesUsed: number;
    isSuccessful: boolean;
    processingDuration: number;
    timestamp: string;
  } {
    return {
      eventType: 'EventEnriched',
      normalizedEventId: this.eventData.normalizedEventId,
      enrichedEventId: this.eventData.enrichedEventId,
      enrichmentScore: this.eventData.enrichmentScore,
      indicatorCount: this.eventData.indicatorCount,
      sourcesUsed: this.eventData.sourcesUsed.length,
      isSuccessful: this.isEnrichmentSuccessful(),
      processingDuration: this.eventData.processingDuration,
      timestamp: this.occurredOn.toISOString(),
    };
  }
}
