import { Test, TestingModule } from '@nestjs/testing';
import { AIRetryService, AIRetryConfig } from '../retry.service';
import { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';
import { RateLimitException } from '@/shared-kernel/exceptions/rate-limit.exception';

describe('AIRetryService', () => {
  let service: AIRetryService;

  const defaultConfig: AIRetryConfig = {
    maxAttempts: 3,
    baseDelay: 100, // Use smaller delays for faster tests
    maxDelay: 1000,
    backoffMultiplier: 2,
    jitter: false, // Disable jitter for predictable tests
    retryableErrors: ['timeout', 'network', 'connection', 'unavailable'],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AIRetryService],
    }).compile();

    service = module.get<AIRetryService>(AIRetryService);
  });

  afterEach(() => {
    // Clean up all registered providers
    service.resetAllMetrics();
  });

  describe('registerProvider', () => {
    it('should register a new provider with retry strategy', () => {
      const providerId = 'test-provider';
      const providerType = 'openai';

      service.registerProvider(providerId, providerType, defaultConfig);

      const config = service.getProviderConfig(providerId);
      expect(config).toEqual(defaultConfig);
    });

    it('should initialize metrics for registered provider', () => {
      const providerId = 'test-provider';
      const providerType = 'openai';

      service.registerProvider(providerId, providerType, defaultConfig);

      const metrics = service.getProviderMetrics(providerId);
      expect(metrics).toBeDefined();
      expect(metrics!.providerId).toBe(providerId);
      expect(metrics!.totalAttempts).toBe(0);
      expect(metrics!.successfulRetries).toBe(0);
      expect(metrics!.failedRetries).toBe(0);
    });
  });

  describe('executeWithRetry', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should execute successful operation without retry', async () => {
      const operation = jest.fn().mockResolvedValue('success');

      const result = await service.executeWithRetry('test-provider', operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
      
      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.successfulRetries).toBe(1);
      expect(metrics!.totalAttempts).toBe(1);
    });

    it('should retry on retryable errors', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('network timeout'))
        .mockResolvedValue('success');

      const result = await service.executeWithRetry('test-provider', operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should retry on ServiceUnavailableException', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new ServiceUnavailableException('Service unavailable', 'test-service', 'overload'))
        .mockResolvedValue('success');

      const result = await service.executeWithRetry('test-provider', operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should retry on RateLimitException', async () => {
      const resetTime = new Date(Date.now() + 60000); // 1 minute from now
      const operation = jest.fn()
        .mockRejectedValueOnce(new RateLimitException('Rate limit exceeded', 'test-service', 100, 90, resetTime))
        .mockResolvedValue('success');

      const result = await service.executeWithRetry('test-provider', operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should not retry on non-retryable errors', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('invalid input'));

      await expect(
        service.executeWithRetry('test-provider', operation)
      ).rejects.toThrow('invalid input');

      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should fail after max attempts', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('network timeout'));

      await expect(
        service.executeWithRetry('test-provider', operation)
      ).rejects.toThrow('network timeout');

      expect(operation).toHaveBeenCalledTimes(3); // maxAttempts
      
      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.failedRetries).toBe(1);
      expect(metrics!.totalAttempts).toBe(3);
    });

    it('should throw error for unregistered provider', async () => {
      const operation = jest.fn().mockResolvedValue('success');

      await expect(
        service.executeWithRetry('unknown-provider', operation)
      ).rejects.toThrow('Retry strategy not registered for provider: unknown-provider');
    });

    it('should apply timeout when specified', async () => {
      const operation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve('success'), 200))
      );

      await expect(
        service.executeWithRetry('test-provider', operation, 100)
      ).rejects.toThrow('Operation timed out after 100ms');
    }, 10000);
  });

  describe('executeWithRetryDetails', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return detailed retry information', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('network timeout'))
        .mockRejectedValueOnce(new Error('network timeout'))
        .mockResolvedValue('success');

      const result = await service.executeWithRetryDetails('test-provider', operation);

      expect(result.result).toBe('success');
      expect(result.attempts).toBe(3);
      expect(result.errors).toHaveLength(2);
      expect(result.totalDelay).toBeGreaterThan(0);
    });

    it('should update metrics with detailed information', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('network timeout'))
        .mockResolvedValue('success');

      await service.executeWithRetryDetails('test-provider', operation);

      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.successfulRetries).toBe(1);
      expect(metrics!.totalAttempts).toBe(2);
      expect(metrics!.totalDelay).toBeGreaterThan(0);
    });
  });

  describe('getProviderMetrics', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return metrics for registered provider', () => {
      const metrics = service.getProviderMetrics('test-provider');

      expect(metrics).toBeDefined();
      expect(metrics!.providerId).toBe('test-provider');
    });

    it('should return null for unregistered provider', () => {
      const metrics = service.getProviderMetrics('unknown-provider');
      expect(metrics).toBeNull();
    });
  });

  describe('getAllProviderMetrics', () => {
    it('should return empty array when no providers registered', () => {
      const metrics = service.getAllProviderMetrics();
      expect(metrics).toEqual([]);
    });

    it('should return metrics for all registered providers', () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      const metrics = service.getAllProviderMetrics();
      expect(metrics).toHaveLength(2);
      expect(metrics.map(m => m.providerId)).toContain('provider1');
      expect(metrics.map(m => m.providerId)).toContain('provider2');
    });
  });

  describe('resetProviderMetrics', () => {
    beforeEach(async () => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
      
      // Generate some metrics
      const operation = jest.fn().mockResolvedValue('success');
      await service.executeWithRetry('test-provider', operation);
    });

    it('should reset metrics for specific provider', () => {
      // Verify metrics exist
      let metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.successfulRetries).toBe(1);

      // Reset metrics
      service.resetProviderMetrics('test-provider');

      // Verify metrics are reset
      metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.successfulRetries).toBe(0);
      expect(metrics!.totalAttempts).toBe(0);
    });
  });

  describe('resetAllMetrics', () => {
    beforeEach(async () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);
      
      // Generate some metrics
      const operation = jest.fn().mockResolvedValue('success');
      await service.executeWithRetry('provider1', operation);
      await service.executeWithRetry('provider2', operation);
    });

    it('should reset metrics for all providers', () => {
      // Verify metrics exist
      expect(service.getProviderMetrics('provider1')!.successfulRetries).toBe(1);
      expect(service.getProviderMetrics('provider2')!.successfulRetries).toBe(1);

      // Reset all metrics
      service.resetAllMetrics();

      // Verify all metrics are reset
      expect(service.getProviderMetrics('provider1')!.successfulRetries).toBe(0);
      expect(service.getProviderMetrics('provider2')!.successfulRetries).toBe(0);
    });
  });

  describe('updateProviderConfig', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should update provider configuration', () => {
      const newConfig = { maxAttempts: 5, baseDelay: 200 };
      
      service.updateProviderConfig('test-provider', newConfig);

      const config = service.getProviderConfig('test-provider');
      expect(config!.maxAttempts).toBe(5);
      expect(config!.baseDelay).toBe(200);
      expect(config!.backoffMultiplier).toBe(2); // Should keep existing values
    });
  });

  describe('unregisterProvider', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should remove provider configuration and metrics', () => {
      expect(service.getProviderConfig('test-provider')).toBeDefined();
      expect(service.getProviderMetrics('test-provider')).toBeDefined();

      service.unregisterProvider('test-provider');

      expect(service.getProviderConfig('test-provider')).toBeNull();
      expect(service.getProviderMetrics('test-provider')).toBeNull();
    });
  });

  describe('static factory methods', () => {
    it('should create exponential backoff config', () => {
      const config = AIRetryService.createExponentialBackoffConfig(5, 500);

      expect(config.maxAttempts).toBe(5);
      expect(config.baseDelay).toBe(500);
      expect(config.backoffMultiplier).toBe(2);
      expect(config.jitter).toBe(true);
      expect(config.retryableErrors).toContain('timeout');
    });

    it('should create linear backoff config', () => {
      const config = AIRetryService.createLinearBackoffConfig(4, 300);

      expect(config.maxAttempts).toBe(4);
      expect(config.baseDelay).toBe(300);
      expect(config.maxDelay).toBe(300);
      expect(config.backoffMultiplier).toBe(1);
      expect(config.jitter).toBe(false);
    });

    it('should create fixed delay config', () => {
      const config = AIRetryService.createFixedDelayConfig(2, 1000);

      expect(config.maxAttempts).toBe(2);
      expect(config.baseDelay).toBe(1000);
      expect(config.maxDelay).toBe(1000);
      expect(config.backoffMultiplier).toBe(1);
      expect(config.jitter).toBe(false);
    });
  });

  describe('error classification', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', {
        ...defaultConfig,
        retryableErrors: ['custom-error', 'api-timeout'],
      });
    });

    it('should retry on custom retryable errors', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('custom-error occurred'))
        .mockResolvedValue('success');

      const result = await service.executeWithRetry('test-provider', operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should not retry on non-configured errors', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('validation error'));

      await expect(
        service.executeWithRetry('test-provider', operation)
      ).rejects.toThrow('validation error');

      expect(operation).toHaveBeenCalledTimes(1);
    });
  });
});