cb73cab34f8421106748a621cb7cc54a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const circuit_breaker_service_1 = require("../circuit-breaker.service");
const circuit_breaker_1 = require("@/shared-kernel/patterns/circuit-breaker");
const service_unavailable_exception_1 = require("@/shared-kernel/exceptions/service-unavailable.exception");
describe('AICircuitBreakerService', () => {
    let service;
    const defaultConfig = {
        failureThreshold: 3,
        recoveryTimeout: 5000,
        monitoringPeriod: 10000,
        halfOpenMaxCalls: 2,
        enableFallback: true,
        fallbackResponse: { message: 'fallback response' },
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [circuit_breaker_service_1.AICircuitBreakerService],
        }).compile();
        service = module.get(circuit_breaker_service_1.AICircuitBreakerService);
    });
    afterEach(() => {
        // Clean up all registered providers
        service.resetAllProviders();
    });
    describe('registerProvider', () => {
        it('should register a new provider with circuit breaker', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            expect(service.isProviderAvailable(providerId)).toBe(true);
        });
        it('should register multiple providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            expect(service.isProviderAvailable('provider1')).toBe(true);
            expect(service.isProviderAvailable('provider2')).toBe(true);
            expect(service.getAvailableProviders()).toHaveLength(2);
        });
    });
    describe('executeWithProtection', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should execute successful operation', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toBe('success');
            expect(operation).toHaveBeenCalledTimes(1);
        });
        it('should handle single failure and continue', async () => {
            const operation = jest.fn()
                .mockRejectedValueOnce(new Error('failure'))
                .mockResolvedValue('success');
            // First call should fail
            await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            // Second call should succeed
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toBe('success');
        });
        it('should open circuit breaker after threshold failures', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail 3 times to reach threshold
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            // Circuit should now be open
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            // Next call should fail with circuit breaker exception
            await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
        });
        it('should use fallback function when circuit is open', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            const fallback = jest.fn().mockResolvedValue('fallback result');
            // Fail enough times to open circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            // Execute with fallback
            const result = await service.executeWithProtection('test-provider', operation, fallback);
            expect(result).toBe('fallback result');
            expect(fallback).toHaveBeenCalledTimes(1);
        });
        it('should use cached fallback response when available', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail enough times to open circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            // Execute without fallback function - should use cached response
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toEqual({ message: 'fallback response' });
        });
        it('should throw error for unregistered provider', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await expect(service.executeWithProtection('unknown-provider', operation)).rejects.toThrow('Circuit breaker not registered for provider: unknown-provider');
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
            expect(metrics.providerType).toBe('test');
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(metrics.failureCount).toBe(0);
            expect(metrics.successCount).toBe(0);
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after operations', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await service.executeWithProtection('test-provider', operation);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.successCount).toBe(1);
            expect(metrics.totalCalls).toBe(1);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('isProviderAvailable', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return true for available provider', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
        });
        it('should return false for unregistered provider', () => {
            expect(service.isProviderAvailable('unknown-provider')).toBe(false);
        });
        it('should return false when circuit is open', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail enough times to open circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            expect(service.isProviderAvailable('test-provider')).toBe(false);
        });
    });
    describe('getAvailableProviders', () => {
        it('should return empty array when no providers available', () => {
            expect(service.getAvailableProviders()).toEqual([]);
        });
        it('should return all available providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const available = service.getAvailableProviders();
            expect(available).toHaveLength(2);
            expect(available).toContain('provider1');
            expect(available).toContain('provider2');
        });
        it('should exclude providers with open circuits', async () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            // Open circuit for provider1
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('provider1', operation)).rejects.toThrow('failure');
            }
            const available = service.getAvailableProviders();
            expect(available).toHaveLength(1);
            expect(available).toContain('provider2');
            expect(available).not.toContain('provider1');
        });
    });
    describe('resetProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should reset circuit breaker state', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Open the circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            // Reset the circuit
            service.resetProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.failureCount).toBe(0);
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
        });
    });
    describe('forceOpenProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should force circuit breaker to open state', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            service.forceOpenProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { failureThreshold: 5 };
            service.updateProviderConfig('test-provider', newConfig);
            // Verify the provider is still registered and available
            expect(service.isProviderAvailable('test-provider')).toBe(true);
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider circuit breaker', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            service.unregisterProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
    });
    describe('getHealthStatus', () => {
        it('should return empty object when no providers registered', () => {
            const status = service.getHealthStatus();
            expect(status).toEqual({});
        });
        it('should return health status for all providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const status = service.getHealthStatus();
            expect(Object.keys(status)).toHaveLength(2);
            expect(status['provider1']).toBeDefined();
            expect(status['provider1'].available).toBe(true);
            expect(status['provider1'].metrics).toBeDefined();
            expect(status['provider2']).toBeDefined();
            expect(status['provider2'].available).toBe(true);
            expect(status['provider2'].metrics).toBeDefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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