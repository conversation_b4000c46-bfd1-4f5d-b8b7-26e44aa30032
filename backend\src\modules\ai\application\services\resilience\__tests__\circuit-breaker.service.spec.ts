import { Test, TestingModule } from '@nestjs/testing';
import { AICircuitBreakerService, AICircuitBreakerConfig } from '../circuit-breaker.service';
import { CircuitBreakerState } from '@/shared-kernel/patterns/circuit-breaker';
import { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';

describe('AICircuitBreakerService', () => {
  let service: AICircuitBreakerService;

  const defaultConfig: AICircuitBreakerConfig = {
    failureThreshold: 3,
    recoveryTimeout: 5000,
    monitoringPeriod: 10000,
    halfOpenMaxCalls: 2,
    enableFallback: false,
  };

  const fallbackConfig: AICircuitBreakerConfig = {
    failureThreshold: 3,
    recoveryTimeout: 5000,
    monitoringPeriod: 10000,
    halfOpenMaxCalls: 2,
    enableFallback: true,
    fallbackResponse: { message: 'fallback response' },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AICircuitBreakerService],
    }).compile();

    service = module.get<AICircuitBreakerService>(AICircuitBreakerService);
  });

  afterEach(() => {
    // Clean up all registered providers
    service.resetAllProviders();
  });

  describe('registerProvider', () => {
    it('should register a new provider with circuit breaker', () => {
      const providerId = 'test-provider';
      const providerType = 'openai';

      service.registerProvider(providerId, providerType, defaultConfig);

      expect(service.isProviderAvailable(providerId)).toBe(true);
    });

    it('should register multiple providers', () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      expect(service.isProviderAvailable('provider1')).toBe(true);
      expect(service.isProviderAvailable('provider2')).toBe(true);
      expect(service.getAvailableProviders()).toHaveLength(2);
    });
  });

  describe('executeWithProtection', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should execute successful operation', async () => {
      const operation = jest.fn().mockResolvedValue('success');

      const result = await service.executeWithProtection('test-provider', operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should handle single failure and continue', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('failure'))
        .mockResolvedValue('success');

      // First call should fail
      await expect(
        service.executeWithProtection('test-provider', operation)
      ).rejects.toThrow('failure');

      // Second call should succeed
      const result = await service.executeWithProtection('test-provider', operation);
      expect(result).toBe('success');
    });

    it('should open circuit breaker after threshold failures', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('failure'));

      // Fail 3 times to reach threshold
      for (let i = 0; i < 3; i++) {
        await expect(
          service.executeWithProtection('test-provider', operation)
        ).rejects.toThrow('failure');
      }

      // Circuit should now be open
      expect(service.isProviderAvailable('test-provider')).toBe(false);

      // Next call should fail with circuit breaker exception
      await expect(
        service.executeWithProtection('test-provider', operation)
      ).rejects.toThrow(ServiceUnavailableException);
    });

    it('should use fallback function when circuit is open', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('failure'));
      const fallback = jest.fn().mockResolvedValue('fallback result');

      // Fail enough times to open circuit
      for (let i = 0; i < 3; i++) {
        await expect(
          service.executeWithProtection('test-provider', operation)
        ).rejects.toThrow('failure');
      }

      // Execute with fallback - should use fallback when circuit is open
      const result = await service.executeWithProtection('test-provider', operation, fallback);
      expect(result).toBe('fallback result');
      expect(fallback).toHaveBeenCalledTimes(1);
    });

    it('should use cached fallback response when available', async () => {
      // Register provider with fallback enabled
      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', fallbackConfig);
      
      const operation = jest.fn().mockRejectedValue(new Error('failure'));

      // Fail enough times to open circuit - with fallback enabled, these should return fallback responses
      for (let i = 0; i < 3; i++) {
        const result = await service.executeWithProtection('test-provider', operation);
        expect(result).toEqual({ message: 'fallback response' });
      }

      // Circuit should now be open, and subsequent calls should still return fallback
      const result = await service.executeWithProtection('test-provider', operation);
      expect(result).toEqual({ message: 'fallback response' });
    });

    it('should throw error for unregistered provider', async () => {
      const operation = jest.fn().mockResolvedValue('success');

      await expect(
        service.executeWithProtection('unknown-provider', operation)
      ).rejects.toThrow('Circuit breaker not registered for provider: unknown-provider');
    });
  });

  describe('getProviderMetrics', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return metrics for registered provider', () => {
      const metrics = service.getProviderMetrics('test-provider');

      expect(metrics).toBeDefined();
      expect(metrics!.providerId).toBe('test-provider');
      expect(metrics!.providerType).toBe('test');
      expect(metrics!.state).toBe(CircuitBreakerState.CLOSED);
      expect(metrics!.failureCount).toBe(0);
      expect(metrics!.successCount).toBe(0);
    });

    it('should return null for unregistered provider', () => {
      const metrics = service.getProviderMetrics('unknown-provider');
      expect(metrics).toBeNull();
    });

    it('should update metrics after operations', async () => {
      const operation = jest.fn().mockResolvedValue('success');

      await service.executeWithProtection('test-provider', operation);

      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.successCount).toBe(1);
      expect(metrics!.totalCalls).toBe(1);
    });
  });

  describe('getAllProviderMetrics', () => {
    it('should return empty array when no providers registered', () => {
      const metrics = service.getAllProviderMetrics();
      expect(metrics).toEqual([]);
    });

    it('should return metrics for all registered providers', () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      const metrics = service.getAllProviderMetrics();
      expect(metrics).toHaveLength(2);
      expect(metrics.map(m => m.providerId)).toContain('provider1');
      expect(metrics.map(m => m.providerId)).toContain('provider2');
    });
  });

  describe('isProviderAvailable', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return true for available provider', () => {
      expect(service.isProviderAvailable('test-provider')).toBe(true);
    });

    it('should return false for unregistered provider', () => {
      expect(service.isProviderAvailable('unknown-provider')).toBe(false);
    });

    it('should return false when circuit is open', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('failure'));

      // Fail enough times to open circuit
      for (let i = 0; i < 3; i++) {
        await expect(
          service.executeWithProtection('test-provider', operation)
        ).rejects.toThrow('failure');
      }

      expect(service.isProviderAvailable('test-provider')).toBe(false);
    });
  });

  describe('getAvailableProviders', () => {
    it('should return empty array when no providers available', () => {
      expect(service.getAvailableProviders()).toEqual([]);
    });

    it('should return all available providers', () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      const available = service.getAvailableProviders();
      expect(available).toHaveLength(2);
      expect(available).toContain('provider1');
      expect(available).toContain('provider2');
    });

    it('should exclude providers with open circuits', async () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      // Open circuit for provider1
      const operation = jest.fn().mockRejectedValue(new Error('failure'));
      for (let i = 0; i < 3; i++) {
        await expect(
          service.executeWithProtection('provider1', operation)
        ).rejects.toThrow('failure');
      }

      const available = service.getAvailableProviders();
      expect(available).toHaveLength(1);
      expect(available).toContain('provider2');
      expect(available).not.toContain('provider1');
    });
  });

  describe('resetProvider', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should reset circuit breaker state', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('failure'));

      // Open the circuit
      for (let i = 0; i < 3; i++) {
        await expect(
          service.executeWithProtection('test-provider', operation)
        ).rejects.toThrow('failure');
      }

      expect(service.isProviderAvailable('test-provider')).toBe(false);

      // Reset the circuit
      service.resetProvider('test-provider');

      expect(service.isProviderAvailable('test-provider')).toBe(true);
      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.failureCount).toBe(0);
      expect(metrics!.state).toBe(CircuitBreakerState.CLOSED);
    });
  });

  describe('forceOpenProvider', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should force circuit breaker to open state', () => {
      expect(service.isProviderAvailable('test-provider')).toBe(true);

      service.forceOpenProvider('test-provider');

      expect(service.isProviderAvailable('test-provider')).toBe(false);
      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.state).toBe(CircuitBreakerState.OPEN);
    });
  });

  describe('updateProviderConfig', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should update provider configuration', () => {
      const newConfig = { failureThreshold: 5 };
      
      service.updateProviderConfig('test-provider', newConfig);

      // Verify the provider is still registered and available
      expect(service.isProviderAvailable('test-provider')).toBe(true);
    });
  });

  describe('unregisterProvider', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should remove provider circuit breaker', () => {
      expect(service.isProviderAvailable('test-provider')).toBe(true);

      service.unregisterProvider('test-provider');

      expect(service.isProviderAvailable('test-provider')).toBe(false);
      expect(service.getProviderMetrics('test-provider')).toBeNull();
    });
  });

  describe('getHealthStatus', () => {
    it('should return empty object when no providers registered', () => {
      const status = service.getHealthStatus();
      expect(status).toEqual({});
    });

    it('should return health status for all providers', () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      const status = service.getHealthStatus();
      
      expect(Object.keys(status)).toHaveLength(2);
      expect(status['provider1']).toBeDefined();
      expect(status['provider1'].available).toBe(true);
      expect(status['provider1'].metrics).toBeDefined();
      expect(status['provider2']).toBeDefined();
      expect(status['provider2'].available).toBe(true);
      expect(status['provider2'].metrics).toBeDefined();
    });
  });
});