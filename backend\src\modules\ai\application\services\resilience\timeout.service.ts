import { Injectable, Logger } from '@nestjs/common';

export interface TimeoutConfig {
  defaultTimeout: number; // Default timeout in milliseconds
  operationTimeouts: Record<string, number>; // Operation-specific timeouts
  escalationTimeouts: number[]; // Escalation timeouts for retries
  enableEscalation: boolean;
}

export interface TimeoutMetrics {
  providerId: string;
  totalOperations: number;
  timeoutCount: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
  timeoutRate: number;
  lastTimeoutTime?: Date;
}

export interface TimeoutResult<T> {
  result: T;
  executionTime: number;
  timedOut: boolean;
  escalationLevel?: number;
}

/**
 * AI-specific timeout service for managing operation timeouts
 */
@Injectable()
export class AITimeoutService {
  private readonly logger = new Logger(AITimeoutService.name);
  private readonly configs = new Map<string, TimeoutConfig>();
  private readonly metrics = new Map<string, TimeoutMetrics>();
  private readonly activeOperations = new Map<string, AbortController>();

  /**
   * Register timeout configuration for an AI provider
   */
  registerProvider(
    providerId: string,
    providerType: string,
    config: TimeoutConfig
  ): void {
    this.configs.set(providerId, config);
    
    // Initialize metrics
    this.metrics.set(providerId, {
      providerId,
      totalOperations: 0,
      timeoutCount: 0,
      averageExecutionTime: 0,
      maxExecutionTime: 0,
      minExecutionTime: Infinity,
      timeoutRate: 0,
    });

    this.logger.log(
      `Registered timeout configuration for ${providerType} provider ${providerId} with default timeout ${config.defaultTimeout}ms`
    );
  }

  /**
   * Execute an operation with timeout protection
   */
  async executeWithTimeout<T>(
    providerId: string,
    operation: (signal?: AbortSignal) => Promise<T>,
    operationType?: string,
    customTimeout?: number
  ): Promise<T> {
    const config = this.configs.get(providerId);
    if (!config) {
      throw new Error(`Timeout configuration not registered for provider: ${providerId}`);
    }

    const timeout = this.getTimeout(config, operationType, customTimeout);
    const operationId = this.generateOperationId(providerId);
    const abortController = new AbortController();
    
    this.activeOperations.set(operationId, abortController);

    const startTime = Date.now();

    try {
      const result = await this.executeWithTimeoutInternal(
        operation,
        timeout,
        abortController.signal,
        operationId
      );

      const executionTime = Date.now() - startTime;
      this.updateMetrics(providerId, executionTime, false);
      
      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const isTimeout = error instanceof Error && error.name === 'TimeoutError';
      
      if (isTimeout) {
        this.logger.warn(
          `Operation timed out for provider ${providerId} after ${timeout}ms`
        );
        this.updateMetrics(providerId, executionTime, true);
      } else {
        this.updateMetrics(providerId, executionTime, false);
      }
      
      throw error;
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  /**
   * Execute with timeout and detailed result information
   */
  async executeWithTimeoutDetails<T>(
    providerId: string,
    operation: (signal?: AbortSignal) => Promise<T>,
    operationType?: string,
    customTimeout?: number
  ): Promise<TimeoutResult<T>> {
    const config = this.configs.get(providerId);
    if (!config) {
      throw new Error(`Timeout configuration not registered for provider: ${providerId}`);
    }

    const timeout = this.getTimeout(config, operationType, customTimeout);
    const operationId = this.generateOperationId(providerId);
    const abortController = new AbortController();
    
    this.activeOperations.set(operationId, abortController);

    const startTime = Date.now();
    let timedOut = false;

    try {
      const result = await this.executeWithTimeoutInternal(
        operation,
        timeout,
        abortController.signal,
        operationId
      );

      const executionTime = Date.now() - startTime;
      this.updateMetrics(providerId, executionTime, false);
      
      return {
        result,
        executionTime,
        timedOut: false,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      timedOut = error instanceof Error && error.name === 'TimeoutError';
      
      if (timedOut) {
        this.updateMetrics(providerId, executionTime, true);
        
        return {
          result: undefined as any,
          executionTime,
          timedOut: true,
        };
      } else {
        this.updateMetrics(providerId, executionTime, false);
        throw error;
      }
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  /**
   * Execute with escalating timeouts for retry scenarios
   */
  async executeWithEscalation<T>(
    providerId: string,
    operation: (signal?: AbortSignal) => Promise<T>,
    attempt: number,
    operationType?: string
  ): Promise<T> {
    const config = this.configs.get(providerId);
    if (!config) {
      throw new Error(`Timeout configuration not registered for provider: ${providerId}`);
    }

    if (!config.enableEscalation || !config.escalationTimeouts.length) {
      return this.executeWithTimeout(providerId, operation, operationType);
    }

    const escalationLevel = Math.min(attempt - 1, config.escalationTimeouts.length - 1);
    const timeout = config.escalationTimeouts[escalationLevel];
    
    this.logger.debug(
      `Executing with escalation level ${escalationLevel}, timeout ${timeout}ms for provider ${providerId}`
    );

    const operationId = this.generateOperationId(providerId);
    const abortController = new AbortController();
    
    this.activeOperations.set(operationId, abortController);

    const startTime = Date.now();

    try {
      const result = await this.executeWithTimeoutInternal(
        operation,
        timeout,
        abortController.signal,
        operationId
      );

      const executionTime = Date.now() - startTime;
      this.updateMetrics(providerId, executionTime, false);
      
      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const isTimeout = error instanceof Error && error.name === 'TimeoutError';
      
      if (isTimeout) {
        this.logger.warn(
          `Escalated operation timed out for provider ${providerId} at level ${escalationLevel} after ${timeout}ms`
        );
        this.updateMetrics(providerId, executionTime, true);
      } else {
        this.updateMetrics(providerId, executionTime, false);
      }
      
      throw error;
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  /**
   * Cancel all active operations for a provider
   */
  cancelProviderOperations(providerId: string): number {
    let cancelledCount = 0;
    
    for (const [operationId, controller] of this.activeOperations) {
      if (operationId.startsWith(providerId)) {
        controller.abort();
        this.activeOperations.delete(operationId);
        cancelledCount++;
      }
    }

    if (cancelledCount > 0) {
      this.logger.log(`Cancelled ${cancelledCount} active operations for provider ${providerId}`);
    }

    return cancelledCount;
  }

  /**
   * Cancel all active operations
   */
  cancelAllOperations(): number {
    const totalOperations = this.activeOperations.size;
    
    for (const [operationId, controller] of this.activeOperations) {
      controller.abort();
    }
    
    this.activeOperations.clear();

    if (totalOperations > 0) {
      this.logger.log(`Cancelled ${totalOperations} active operations`);
    }

    return totalOperations;
  }

  /**
   * Get timeout metrics for a specific provider
   */
  getProviderMetrics(providerId: string): TimeoutMetrics | null {
    return this.metrics.get(providerId) || null;
  }

  /**
   * Get metrics for all registered providers
   */
  getAllProviderMetrics(): TimeoutMetrics[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Reset metrics for a specific provider
   */
  resetProviderMetrics(providerId: string): void {
    const metrics = this.metrics.get(providerId);
    if (metrics) {
      this.metrics.set(providerId, {
        providerId,
        totalOperations: 0,
        timeoutCount: 0,
        averageExecutionTime: 0,
        maxExecutionTime: 0,
        minExecutionTime: Infinity,
        timeoutRate: 0,
      });
      this.logger.log(`Reset timeout metrics for provider ${providerId}`);
    }
  }

  /**
   * Reset metrics for all providers
   */
  resetAllMetrics(): void {
    for (const [providerId] of this.metrics) {
      this.resetProviderMetrics(providerId);
    }
    this.logger.log('Reset timeout metrics for all providers');
  }

  /**
   * Update provider configuration
   */
  updateProviderConfig(providerId: string, config: Partial<TimeoutConfig>): void {
    const existingConfig = this.configs.get(providerId);
    if (existingConfig) {
      const updatedConfig = { ...existingConfig, ...config };
      this.configs.set(providerId, updatedConfig);
      
      this.logger.log(`Updated timeout configuration for provider ${providerId}`);
    }
  }

  /**
   * Remove a provider's timeout configuration
   */
  unregisterProvider(providerId: string): void {
    // Cancel any active operations for this provider
    this.cancelProviderOperations(providerId);
    
    this.configs.delete(providerId);
    this.metrics.delete(providerId);
    this.logger.log(`Unregistered timeout configuration for provider ${providerId}`);
  }

  /**
   * Get provider configuration
   */
  getProviderConfig(providerId: string): TimeoutConfig | null {
    return this.configs.get(providerId) || null;
  }

  /**
   * Get active operation count for a provider
   */
  getActiveOperationCount(providerId: string): number {
    let count = 0;
    for (const operationId of this.activeOperations.keys()) {
      if (operationId.startsWith(providerId)) {
        count++;
      }
    }
    return count;
  }

  /**
   * Get total active operation count
   */
  getTotalActiveOperationCount(): number {
    return this.activeOperations.size;
  }

  /**
   * Execute operation with timeout protection
   */
  private async executeWithTimeoutInternal<T>(
    operation: (signal?: AbortSignal) => Promise<T>,
    timeoutMs: number,
    signal: AbortSignal,
    operationId: string
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timeoutHandle = setTimeout(() => {
        const error = new Error(`Operation timed out after ${timeoutMs}ms`);
        error.name = 'TimeoutError';
        reject(error);
      }, timeoutMs);

      // Handle abort signal
      const abortHandler = () => {
        clearTimeout(timeoutHandle);
        const error = new Error('Operation was cancelled');
        error.name = 'AbortError';
        reject(error);
      };

      if (signal.aborted) {
        abortHandler();
        return;
      }

      signal.addEventListener('abort', abortHandler);

      operation(signal)
        .then((result) => {
          clearTimeout(timeoutHandle);
          signal.removeEventListener('abort', abortHandler);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timeoutHandle);
          signal.removeEventListener('abort', abortHandler);
          reject(error);
        });
    });
  }

  /**
   * Get timeout value based on configuration
   */
  private getTimeout(
    config: TimeoutConfig,
    operationType?: string,
    customTimeout?: number
  ): number {
    if (customTimeout !== undefined) {
      return customTimeout;
    }

    if (operationType && config.operationTimeouts[operationType]) {
      return config.operationTimeouts[operationType];
    }

    return config.defaultTimeout;
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(providerId: string): string {
    return `${providerId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update timeout metrics
   */
  private updateMetrics(providerId: string, executionTime: number, timedOut: boolean): void {
    const metrics = this.metrics.get(providerId);
    if (metrics) {
      metrics.totalOperations++;
      
      if (timedOut) {
        metrics.timeoutCount++;
        metrics.lastTimeoutTime = new Date();
      }
      
      // Update execution time statistics
      metrics.averageExecutionTime = 
        (metrics.averageExecutionTime * (metrics.totalOperations - 1) + executionTime) / 
        metrics.totalOperations;
      
      metrics.maxExecutionTime = Math.max(metrics.maxExecutionTime, executionTime);
      metrics.minExecutionTime = Math.min(metrics.minExecutionTime, executionTime);
      
      // Calculate timeout rate
      metrics.timeoutRate = metrics.timeoutCount / metrics.totalOperations;
      
      this.metrics.set(providerId, metrics);
    }
  }

  /**
   * Create predefined timeout configurations
   */
  static createDefaultConfig(defaultTimeout: number = 30000): TimeoutConfig {
    return {
      defaultTimeout,
      operationTimeouts: {},
      escalationTimeouts: [defaultTimeout, defaultTimeout * 1.5, defaultTimeout * 2],
      enableEscalation: true,
    };
  }

  static createQuickConfig(defaultTimeout: number = 5000): TimeoutConfig {
    return {
      defaultTimeout,
      operationTimeouts: {
        'quick-analysis': 2000,
        'simple-query': 3000,
      },
      escalationTimeouts: [defaultTimeout, defaultTimeout * 1.2, defaultTimeout * 1.5],
      enableEscalation: true,
    };
  }

  static createLongRunningConfig(defaultTimeout: number = 120000): TimeoutConfig {
    return {
      defaultTimeout,
      operationTimeouts: {
        'model-training': 300000, // 5 minutes
        'large-analysis': 180000, // 3 minutes
      },
      escalationTimeouts: [defaultTimeout, defaultTimeout * 1.5, defaultTimeout * 2, defaultTimeout * 3],
      enableEscalation: true,
    };
  }
}