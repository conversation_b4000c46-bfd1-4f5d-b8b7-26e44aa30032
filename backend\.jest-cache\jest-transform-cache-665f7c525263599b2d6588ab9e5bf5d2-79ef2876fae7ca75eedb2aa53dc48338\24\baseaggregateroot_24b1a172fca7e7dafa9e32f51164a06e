f3008a4a02d9f8fd45e4d2142baa62b9
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAggregateRoot = void 0;
const base_entity_1 = require("./base-entity");
/**
 * Base Aggregate Root
 *
 * Abstract base class for all aggregate roots in the domain layer.
 * Provides event handling capabilities and ensures domain invariants.
 *
 * Key responsibilities:
 * - Manage domain events within the aggregate boundary
 * - Ensure consistency and invariants across the aggregate
 * - Provide event publishing capabilities
 * - Handle aggregate lifecycle management
 *
 * @template T The props type for the aggregate root
 */
class BaseAggregateRoot extends base_entity_1.BaseEntity {
    constructor(props, id) {
        super(props, id);
        this._domainEvents = [];
    }
    /**
     * Get all domain events for this aggregate
     */
    get domainEvents() {
        return this._domainEvents.slice(); // Return a copy to prevent external modification
    }
    /**
     * Add a domain event to the aggregate
     *
     * @param domainEvent The domain event to add
     */
    addDomainEvent(domainEvent) {
        // Check if event already exists to prevent duplicates
        const existingEvent = this._domainEvents.find(event => event.eventId.equals(domainEvent.eventId));
        if (!existingEvent) {
            this._domainEvents.push(domainEvent);
        }
    }
    /**
     * Remove a specific domain event
     *
     * @param domainEvent The domain event to remove
     */
    removeDomainEvent(domainEvent) {
        const index = this._domainEvents.findIndex(event => event.eventId.equals(domainEvent.eventId));
        if (index !== -1) {
            this._domainEvents.splice(index, 1);
        }
    }
    /**
     * Clear all domain events
     * This is typically called after events have been published
     */
    clearEvents() {
        this._domainEvents = [];
    }
    /**
     * Mark events as dispatched
     * This is called by the event dispatcher after successful publishing
     */
    markEventsForDispatch() {
        this._domainEvents.forEach(event => {
            event.markAsDispatched();
        });
    }
    /**
     * Check if the aggregate has any unpublished events
     */
    hasUnpublishedEvents() {
        return this._domainEvents.some(event => !event.isDispatched);
    }
    /**
     * Get only unpublished events
     */
    getUnpublishedEvents() {
        return this._domainEvents.filter(event => !event.isDispatched);
    }
    /**
     * Get events by type
     *
     * @param eventType The type of events to retrieve
     */
    getEventsByType(eventType) {
        return this._domainEvents.filter(event => event instanceof eventType);
    }
    /**
     * Check if aggregate has events of a specific type
     *
     * @param eventType The type of events to check for
     */
    hasEventsOfType(eventType) {
        return this._domainEvents.some(event => event instanceof eventType);
    }
    /**
     * Get the aggregate version based on the number of events
     * This can be used for optimistic concurrency control
     */
    getVersion() {
        return this._domainEvents.length;
    }
    /**
     * Create a snapshot of the aggregate state
     * Useful for event sourcing scenarios
     */
    createSnapshot() {
        return {
            aggregateId: this._id.toString(),
            version: this.getVersion(),
            state: { ...this.props },
            timestamp: new Date(),
        };
    }
    /**
     * Validate aggregate invariants
     * Override in concrete implementations to add specific business rules
     */
    validateInvariants() {
        // Base implementation - override in concrete classes
        if (!this._id) {
            throw new Error('Aggregate must have a valid ID');
        }
    }
    /**
     * Apply business rules and validate state
     * Called before any state changes
     */
    applyBusinessRules() {
        this.validateInvariants();
    }
    /**
     * Convert aggregate to JSON representation
     * Includes domain events for debugging purposes
     */
    toJSON() {
        return {
            ...super.toJSON(),
            domainEvents: this._domainEvents.map(event => ({
                eventType: event.constructor.name,
                eventId: event.eventId.toString(),
                occurredOn: event.occurredOn,
                isDispatched: event.isDispatched,
            })),
            version: this.getVersion(),
        };
    }
    /**
     * Create a deep copy of the aggregate
     * Useful for testing and state management
     */
    clone() {
        const clonedProps = JSON.parse(JSON.stringify(this.props));
        const cloned = Object.create(Object.getPrototypeOf(this));
        cloned.props = clonedProps;
        cloned._id = this._id;
        cloned._domainEvents = [...this._domainEvents];
        return cloned;
    }
    /**
     * Compare two aggregates for equality
     * Aggregates are equal if they have the same ID and version
     */
    equals(object) {
        if (!super.equals(object)) {
            return false;
        }
        return this.getVersion() === object.getVersion();
    }
    /**
     * Get domain events (alias for domainEvents getter)
     */
    getDomainEvents() {
        return this.domainEvents;
    }
    /**
     * Clear domain events (alias for clearEvents)
     */
    clearDomainEvents() {
        this.clearEvents();
    }
}
exports.BaseAggregateRoot = BaseAggregateRoot;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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