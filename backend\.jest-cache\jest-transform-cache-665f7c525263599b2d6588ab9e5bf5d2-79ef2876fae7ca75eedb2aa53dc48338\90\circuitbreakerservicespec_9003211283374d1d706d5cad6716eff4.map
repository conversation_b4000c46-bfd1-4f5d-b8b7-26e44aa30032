{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\circuit-breaker.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,wEAA6F;AAC7F,8EAA+E;AAC/E,4GAAuG;AAEvG,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,IAAI,OAAgC,CAAC;IAErC,MAAM,aAAa,GAA2B;QAC5C,gBAAgB,EAAE,CAAC;QACnB,eAAe,EAAE,IAAI;QACrB,gBAAgB,EAAE,KAAK;QACvB,gBAAgB,EAAE,CAAC;QACnB,cAAc,EAAE,IAAI;QACpB,gBAAgB,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE;KACnD,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE,CAAC,iDAAuB,CAAC;SACrC,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA0B,iDAAuB,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,oCAAoC;QACpC,OAAO,CAAC,iBAAiB,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,UAAU,GAAG,eAAe,CAAC;YACnC,MAAM,YAAY,GAAG,QAAQ,CAAC;YAE9B,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE/E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;iBAC3C,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,yBAAyB;YACzB,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE7B,6BAA6B;YAC7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAC/E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAEpE,kCAAkC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YAED,6BAA6B;YAC7B,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjE,uDAAuD;YACvD,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAEhE,oCAAoC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YAED,wBAAwB;YACxB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YACzF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAEpE,oCAAoC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YAED,iEAAiE;YACjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAC/E,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAC7D,CAAC,OAAO,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5D,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEhE,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAEpE,oCAAoC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,SAAS,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,6BAA6B;YAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,WAAW,EAAE,SAAS,CAAC,CACtD,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YAED,MAAM,SAAS,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAEpE,mBAAmB;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,MAAM,CACV,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjE,oBAAoB;YACpB,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;YAEvC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,OAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhE,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;YAE1C,OAAO,CAAC,oBAAoB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEzD,wDAAwD;YACxD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhE,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,MAAM,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,MAAM,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\circuit-breaker.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { AICircuitBreakerService, AICircuitBreakerConfig } from '../circuit-breaker.service';\r\nimport { CircuitBreakerState } from '@/shared-kernel/patterns/circuit-breaker';\r\nimport { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';\r\n\r\ndescribe('AICircuitBreakerService', () => {\r\n  let service: AICircuitBreakerService;\r\n\r\n  const defaultConfig: AICircuitBreakerConfig = {\r\n    failureThreshold: 3,\r\n    recoveryTimeout: 5000,\r\n    monitoringPeriod: 10000,\r\n    halfOpenMaxCalls: 2,\r\n    enableFallback: true,\r\n    fallbackResponse: { message: 'fallback response' },\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [AICircuitBreakerService],\r\n    }).compile();\r\n\r\n    service = module.get<AICircuitBreakerService>(AICircuitBreakerService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    // Clean up all registered providers\r\n    service.resetAllProviders();\r\n  });\r\n\r\n  describe('registerProvider', () => {\r\n    it('should register a new provider with circuit breaker', () => {\r\n      const providerId = 'test-provider';\r\n      const providerType = 'openai';\r\n\r\n      service.registerProvider(providerId, providerType, defaultConfig);\r\n\r\n      expect(service.isProviderAvailable(providerId)).toBe(true);\r\n    });\r\n\r\n    it('should register multiple providers', () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      expect(service.isProviderAvailable('provider1')).toBe(true);\r\n      expect(service.isProviderAvailable('provider2')).toBe(true);\r\n      expect(service.getAvailableProviders()).toHaveLength(2);\r\n    });\r\n  });\r\n\r\n  describe('executeWithProtection', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should execute successful operation', async () => {\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      const result = await service.executeWithProtection('test-provider', operation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should handle single failure and continue', async () => {\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new Error('failure'))\r\n        .mockResolvedValue('success');\r\n\r\n      // First call should fail\r\n      await expect(\r\n        service.executeWithProtection('test-provider', operation)\r\n      ).rejects.toThrow('failure');\r\n\r\n      // Second call should succeed\r\n      const result = await service.executeWithProtection('test-provider', operation);\r\n      expect(result).toBe('success');\r\n    });\r\n\r\n    it('should open circuit breaker after threshold failures', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('failure'));\r\n\r\n      // Fail 3 times to reach threshold\r\n      for (let i = 0; i < 3; i++) {\r\n        await expect(\r\n          service.executeWithProtection('test-provider', operation)\r\n        ).rejects.toThrow('failure');\r\n      }\r\n\r\n      // Circuit should now be open\r\n      expect(service.isProviderAvailable('test-provider')).toBe(false);\r\n\r\n      // Next call should fail with circuit breaker exception\r\n      await expect(\r\n        service.executeWithProtection('test-provider', operation)\r\n      ).rejects.toThrow(ServiceUnavailableException);\r\n    });\r\n\r\n    it('should use fallback function when circuit is open', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('failure'));\r\n      const fallback = jest.fn().mockResolvedValue('fallback result');\r\n\r\n      // Fail enough times to open circuit\r\n      for (let i = 0; i < 3; i++) {\r\n        await expect(\r\n          service.executeWithProtection('test-provider', operation)\r\n        ).rejects.toThrow('failure');\r\n      }\r\n\r\n      // Execute with fallback\r\n      const result = await service.executeWithProtection('test-provider', operation, fallback);\r\n      expect(result).toBe('fallback result');\r\n      expect(fallback).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should use cached fallback response when available', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('failure'));\r\n\r\n      // Fail enough times to open circuit\r\n      for (let i = 0; i < 3; i++) {\r\n        await expect(\r\n          service.executeWithProtection('test-provider', operation)\r\n        ).rejects.toThrow('failure');\r\n      }\r\n\r\n      // Execute without fallback function - should use cached response\r\n      const result = await service.executeWithProtection('test-provider', operation);\r\n      expect(result).toEqual({ message: 'fallback response' });\r\n    });\r\n\r\n    it('should throw error for unregistered provider', async () => {\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      await expect(\r\n        service.executeWithProtection('unknown-provider', operation)\r\n      ).rejects.toThrow('Circuit breaker not registered for provider: unknown-provider');\r\n    });\r\n  });\r\n\r\n  describe('getProviderMetrics', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return metrics for registered provider', () => {\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n\r\n      expect(metrics).toBeDefined();\r\n      expect(metrics!.providerId).toBe('test-provider');\r\n      expect(metrics!.providerType).toBe('test');\r\n      expect(metrics!.state).toBe(CircuitBreakerState.CLOSED);\r\n      expect(metrics!.failureCount).toBe(0);\r\n      expect(metrics!.successCount).toBe(0);\r\n    });\r\n\r\n    it('should return null for unregistered provider', () => {\r\n      const metrics = service.getProviderMetrics('unknown-provider');\r\n      expect(metrics).toBeNull();\r\n    });\r\n\r\n    it('should update metrics after operations', async () => {\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      await service.executeWithProtection('test-provider', operation);\r\n\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.successCount).toBe(1);\r\n      expect(metrics!.totalCalls).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('getAllProviderMetrics', () => {\r\n    it('should return empty array when no providers registered', () => {\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toEqual([]);\r\n    });\r\n\r\n    it('should return metrics for all registered providers', () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toHaveLength(2);\r\n      expect(metrics.map(m => m.providerId)).toContain('provider1');\r\n      expect(metrics.map(m => m.providerId)).toContain('provider2');\r\n    });\r\n  });\r\n\r\n  describe('isProviderAvailable', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return true for available provider', () => {\r\n      expect(service.isProviderAvailable('test-provider')).toBe(true);\r\n    });\r\n\r\n    it('should return false for unregistered provider', () => {\r\n      expect(service.isProviderAvailable('unknown-provider')).toBe(false);\r\n    });\r\n\r\n    it('should return false when circuit is open', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('failure'));\r\n\r\n      // Fail enough times to open circuit\r\n      for (let i = 0; i < 3; i++) {\r\n        await expect(\r\n          service.executeWithProtection('test-provider', operation)\r\n        ).rejects.toThrow('failure');\r\n      }\r\n\r\n      expect(service.isProviderAvailable('test-provider')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('getAvailableProviders', () => {\r\n    it('should return empty array when no providers available', () => {\r\n      expect(service.getAvailableProviders()).toEqual([]);\r\n    });\r\n\r\n    it('should return all available providers', () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      const available = service.getAvailableProviders();\r\n      expect(available).toHaveLength(2);\r\n      expect(available).toContain('provider1');\r\n      expect(available).toContain('provider2');\r\n    });\r\n\r\n    it('should exclude providers with open circuits', async () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      // Open circuit for provider1\r\n      const operation = jest.fn().mockRejectedValue(new Error('failure'));\r\n      for (let i = 0; i < 3; i++) {\r\n        await expect(\r\n          service.executeWithProtection('provider1', operation)\r\n        ).rejects.toThrow('failure');\r\n      }\r\n\r\n      const available = service.getAvailableProviders();\r\n      expect(available).toHaveLength(1);\r\n      expect(available).toContain('provider2');\r\n      expect(available).not.toContain('provider1');\r\n    });\r\n  });\r\n\r\n  describe('resetProvider', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should reset circuit breaker state', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('failure'));\r\n\r\n      // Open the circuit\r\n      for (let i = 0; i < 3; i++) {\r\n        await expect(\r\n          service.executeWithProtection('test-provider', operation)\r\n        ).rejects.toThrow('failure');\r\n      }\r\n\r\n      expect(service.isProviderAvailable('test-provider')).toBe(false);\r\n\r\n      // Reset the circuit\r\n      service.resetProvider('test-provider');\r\n\r\n      expect(service.isProviderAvailable('test-provider')).toBe(true);\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.failureCount).toBe(0);\r\n      expect(metrics!.state).toBe(CircuitBreakerState.CLOSED);\r\n    });\r\n  });\r\n\r\n  describe('forceOpenProvider', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should force circuit breaker to open state', () => {\r\n      expect(service.isProviderAvailable('test-provider')).toBe(true);\r\n\r\n      service.forceOpenProvider('test-provider');\r\n\r\n      expect(service.isProviderAvailable('test-provider')).toBe(false);\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.state).toBe(CircuitBreakerState.OPEN);\r\n    });\r\n  });\r\n\r\n  describe('updateProviderConfig', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should update provider configuration', () => {\r\n      const newConfig = { failureThreshold: 5 };\r\n      \r\n      service.updateProviderConfig('test-provider', newConfig);\r\n\r\n      // Verify the provider is still registered and available\r\n      expect(service.isProviderAvailable('test-provider')).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('unregisterProvider', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should remove provider circuit breaker', () => {\r\n      expect(service.isProviderAvailable('test-provider')).toBe(true);\r\n\r\n      service.unregisterProvider('test-provider');\r\n\r\n      expect(service.isProviderAvailable('test-provider')).toBe(false);\r\n      expect(service.getProviderMetrics('test-provider')).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('getHealthStatus', () => {\r\n    it('should return empty object when no providers registered', () => {\r\n      const status = service.getHealthStatus();\r\n      expect(status).toEqual({});\r\n    });\r\n\r\n    it('should return health status for all providers', () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      const status = service.getHealthStatus();\r\n      \r\n      expect(Object.keys(status)).toHaveLength(2);\r\n      expect(status['provider1']).toBeDefined();\r\n      expect(status['provider1'].available).toBe(true);\r\n      expect(status['provider1'].metrics).toBeDefined();\r\n      expect(status['provider2']).toBeDefined();\r\n      expect(status['provider2'].available).toBe(true);\r\n      expect(status['provider2'].metrics).toBeDefined();\r\n    });\r\n  });\r\n});"], "version": 3}