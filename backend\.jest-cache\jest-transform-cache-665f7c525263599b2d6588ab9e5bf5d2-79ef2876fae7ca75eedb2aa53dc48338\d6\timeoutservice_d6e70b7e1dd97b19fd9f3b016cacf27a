669e87f413bf4640a25471fc84e87b93
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AITimeoutService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AITimeoutService = void 0;
const common_1 = require("@nestjs/common");
/**
 * AI-specific timeout service for managing operation timeouts
 */
let AITimeoutService = AITimeoutService_1 = class AITimeoutService {
    constructor() {
        this.logger = new common_1.Logger(AITimeoutService_1.name);
        this.configs = new Map();
        this.metrics = new Map();
        this.activeOperations = new Map();
    }
    /**
     * Register timeout configuration for an AI provider
     */
    registerProvider(providerId, providerType, config) {
        this.configs.set(providerId, config);
        // Initialize metrics
        this.metrics.set(providerId, {
            providerId,
            totalOperations: 0,
            timeoutCount: 0,
            averageExecutionTime: 0,
            maxExecutionTime: 0,
            minExecutionTime: Infinity,
            timeoutRate: 0,
        });
        this.logger.log(`Registered timeout configuration for ${providerType} provider ${providerId} with default timeout ${config.defaultTimeout}ms`);
    }
    /**
     * Execute an operation with timeout protection
     */
    async executeWithTimeout(providerId, operation, operationType, customTimeout) {
        const config = this.configs.get(providerId);
        if (!config) {
            throw new Error(`Timeout configuration not registered for provider: ${providerId}`);
        }
        const timeout = this.getTimeout(config, operationType, customTimeout);
        const operationId = this.generateOperationId(providerId);
        const abortController = new AbortController();
        this.activeOperations.set(operationId, abortController);
        const startTime = Date.now();
        try {
            const result = await this.executeWithTimeoutInternal(operation, timeout, abortController.signal, operationId);
            const executionTime = Date.now() - startTime;
            this.updateMetrics(providerId, executionTime, false);
            return result;
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            const isTimeout = error instanceof Error && error.name === 'TimeoutError';
            if (isTimeout) {
                this.logger.warn(`Operation timed out for provider ${providerId} after ${timeout}ms`);
                this.updateMetrics(providerId, executionTime, true);
            }
            else {
                this.updateMetrics(providerId, executionTime, false);
            }
            throw error;
        }
        finally {
            this.activeOperations.delete(operationId);
        }
    }
    /**
     * Execute with timeout and detailed result information
     */
    async executeWithTimeoutDetails(providerId, operation, operationType, customTimeout) {
        const config = this.configs.get(providerId);
        if (!config) {
            throw new Error(`Timeout configuration not registered for provider: ${providerId}`);
        }
        const timeout = this.getTimeout(config, operationType, customTimeout);
        const operationId = this.generateOperationId(providerId);
        const abortController = new AbortController();
        this.activeOperations.set(operationId, abortController);
        const startTime = Date.now();
        let timedOut = false;
        try {
            const result = await this.executeWithTimeoutInternal(operation, timeout, abortController.signal, operationId);
            const executionTime = Date.now() - startTime;
            this.updateMetrics(providerId, executionTime, false);
            return {
                result,
                executionTime,
                timedOut: false,
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            timedOut = error instanceof Error && error.name === 'TimeoutError';
            if (timedOut) {
                this.updateMetrics(providerId, executionTime, true);
                return {
                    result: undefined,
                    executionTime,
                    timedOut: true,
                };
            }
            else {
                this.updateMetrics(providerId, executionTime, false);
                throw error;
            }
        }
        finally {
            this.activeOperations.delete(operationId);
        }
    }
    /**
     * Execute with escalating timeouts for retry scenarios
     */
    async executeWithEscalation(providerId, operation, attempt, operationType) {
        const config = this.configs.get(providerId);
        if (!config) {
            throw new Error(`Timeout configuration not registered for provider: ${providerId}`);
        }
        if (!config.enableEscalation || !config.escalationTimeouts.length) {
            return this.executeWithTimeout(providerId, operation, operationType);
        }
        const escalationLevel = Math.min(attempt - 1, config.escalationTimeouts.length - 1);
        const timeout = config.escalationTimeouts[escalationLevel];
        this.logger.debug(`Executing with escalation level ${escalationLevel}, timeout ${timeout}ms for provider ${providerId}`);
        const operationId = this.generateOperationId(providerId);
        const abortController = new AbortController();
        this.activeOperations.set(operationId, abortController);
        const startTime = Date.now();
        try {
            const result = await this.executeWithTimeoutInternal(operation, timeout, abortController.signal, operationId);
            const executionTime = Date.now() - startTime;
            this.updateMetrics(providerId, executionTime, false);
            return result;
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            const isTimeout = error instanceof Error && error.name === 'TimeoutError';
            if (isTimeout) {
                this.logger.warn(`Escalated operation timed out for provider ${providerId} at level ${escalationLevel} after ${timeout}ms`);
                this.updateMetrics(providerId, executionTime, true);
            }
            else {
                this.updateMetrics(providerId, executionTime, false);
            }
            throw error;
        }
        finally {
            this.activeOperations.delete(operationId);
        }
    }
    /**
     * Cancel all active operations for a provider
     */
    cancelProviderOperations(providerId) {
        let cancelledCount = 0;
        for (const [operationId, controller] of this.activeOperations) {
            if (operationId.startsWith(providerId)) {
                controller.abort();
                this.activeOperations.delete(operationId);
                cancelledCount++;
            }
        }
        if (cancelledCount > 0) {
            this.logger.log(`Cancelled ${cancelledCount} active operations for provider ${providerId}`);
        }
        return cancelledCount;
    }
    /**
     * Cancel all active operations
     */
    cancelAllOperations() {
        const totalOperations = this.activeOperations.size;
        for (const [operationId, controller] of this.activeOperations) {
            controller.abort();
        }
        this.activeOperations.clear();
        if (totalOperations > 0) {
            this.logger.log(`Cancelled ${totalOperations} active operations`);
        }
        return totalOperations;
    }
    /**
     * Get timeout metrics for a specific provider
     */
    getProviderMetrics(providerId) {
        return this.metrics.get(providerId) || null;
    }
    /**
     * Get metrics for all registered providers
     */
    getAllProviderMetrics() {
        return Array.from(this.metrics.values());
    }
    /**
     * Reset metrics for a specific provider
     */
    resetProviderMetrics(providerId) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            this.metrics.set(providerId, {
                providerId,
                totalOperations: 0,
                timeoutCount: 0,
                averageExecutionTime: 0,
                maxExecutionTime: 0,
                minExecutionTime: Infinity,
                timeoutRate: 0,
            });
            this.logger.log(`Reset timeout metrics for provider ${providerId}`);
        }
    }
    /**
     * Reset metrics for all providers
     */
    resetAllMetrics() {
        for (const [providerId] of this.metrics) {
            this.resetProviderMetrics(providerId);
        }
        this.logger.log('Reset timeout metrics for all providers');
    }
    /**
     * Update provider configuration
     */
    updateProviderConfig(providerId, config) {
        const existingConfig = this.configs.get(providerId);
        if (existingConfig) {
            const updatedConfig = { ...existingConfig, ...config };
            this.configs.set(providerId, updatedConfig);
            this.logger.log(`Updated timeout configuration for provider ${providerId}`);
        }
    }
    /**
     * Remove a provider's timeout configuration
     */
    unregisterProvider(providerId) {
        // Cancel any active operations for this provider
        this.cancelProviderOperations(providerId);
        this.configs.delete(providerId);
        this.metrics.delete(providerId);
        this.logger.log(`Unregistered timeout configuration for provider ${providerId}`);
    }
    /**
     * Get provider configuration
     */
    getProviderConfig(providerId) {
        return this.configs.get(providerId) || null;
    }
    /**
     * Get active operation count for a provider
     */
    getActiveOperationCount(providerId) {
        let count = 0;
        for (const operationId of this.activeOperations.keys()) {
            if (operationId.startsWith(providerId)) {
                count++;
            }
        }
        return count;
    }
    /**
     * Get total active operation count
     */
    getTotalActiveOperationCount() {
        return this.activeOperations.size;
    }
    /**
     * Execute operation with timeout protection
     */
    async executeWithTimeoutInternal(operation, timeoutMs, signal, operationId) {
        return new Promise((resolve, reject) => {
            const timeoutHandle = setTimeout(() => {
                const error = new Error(`Operation timed out after ${timeoutMs}ms`);
                error.name = 'TimeoutError';
                reject(error);
            }, timeoutMs);
            // Handle abort signal
            const abortHandler = () => {
                clearTimeout(timeoutHandle);
                const error = new Error('Operation was cancelled');
                error.name = 'AbortError';
                reject(error);
            };
            if (signal.aborted) {
                abortHandler();
                return;
            }
            signal.addEventListener('abort', abortHandler);
            operation(signal)
                .then((result) => {
                clearTimeout(timeoutHandle);
                signal.removeEventListener('abort', abortHandler);
                resolve(result);
            })
                .catch((error) => {
                clearTimeout(timeoutHandle);
                signal.removeEventListener('abort', abortHandler);
                reject(error);
            });
        });
    }
    /**
     * Get timeout value based on configuration
     */
    getTimeout(config, operationType, customTimeout) {
        if (customTimeout !== undefined) {
            return customTimeout;
        }
        if (operationType && config.operationTimeouts[operationType]) {
            return config.operationTimeouts[operationType];
        }
        return config.defaultTimeout;
    }
    /**
     * Generate unique operation ID
     */
    generateOperationId(providerId) {
        return `${providerId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Update timeout metrics
     */
    updateMetrics(providerId, executionTime, timedOut) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            metrics.totalOperations++;
            if (timedOut) {
                metrics.timeoutCount++;
                metrics.lastTimeoutTime = new Date();
            }
            // Update execution time statistics
            metrics.averageExecutionTime =
                (metrics.averageExecutionTime * (metrics.totalOperations - 1) + executionTime) /
                    metrics.totalOperations;
            metrics.maxExecutionTime = Math.max(metrics.maxExecutionTime, executionTime);
            metrics.minExecutionTime = Math.min(metrics.minExecutionTime, executionTime);
            // Calculate timeout rate
            metrics.timeoutRate = metrics.timeoutCount / metrics.totalOperations;
            this.metrics.set(providerId, metrics);
        }
    }
    /**
     * Create predefined timeout configurations
     */
    static createDefaultConfig(defaultTimeout = 30000) {
        return {
            defaultTimeout,
            operationTimeouts: {},
            escalationTimeouts: [defaultTimeout, defaultTimeout * 1.5, defaultTimeout * 2],
            enableEscalation: true,
        };
    }
    static createQuickConfig(defaultTimeout = 5000) {
        return {
            defaultTimeout,
            operationTimeouts: {
                'quick-analysis': 2000,
                'simple-query': 3000,
            },
            escalationTimeouts: [defaultTimeout, defaultTimeout * 1.2, defaultTimeout * 1.5],
            enableEscalation: true,
        };
    }
    static createLongRunningConfig(defaultTimeout = 120000) {
        return {
            defaultTimeout,
            operationTimeouts: {
                'model-training': 300000, // 5 minutes
                'large-analysis': 180000, // 3 minutes
            },
            escalationTimeouts: [defaultTimeout, defaultTimeout * 1.5, defaultTimeout * 2, defaultTimeout * 3],
            enableEscalation: true,
        };
    }
};
exports.AITimeoutService = AITimeoutService;
exports.AITimeoutService = AITimeoutService = AITimeoutService_1 = __decorate([
    (0, common_1.Injectable)()
], AITimeoutService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxhaVxcYXBwbGljYXRpb25cXHNlcnZpY2VzXFxyZXNpbGllbmNlXFx0aW1lb3V0LnNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLDJDQUFvRDtBQTJCcEQ7O0dBRUc7QUFFSSxJQUFNLGdCQUFnQix3QkFBdEIsTUFBTSxnQkFBZ0I7SUFBdEI7UUFDWSxXQUFNLEdBQUcsSUFBSSxlQUFNLENBQUMsa0JBQWdCLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDM0MsWUFBTyxHQUFHLElBQUksR0FBRyxFQUF5QixDQUFDO1FBQzNDLFlBQU8sR0FBRyxJQUFJLEdBQUcsRUFBMEIsQ0FBQztRQUM1QyxxQkFBZ0IsR0FBRyxJQUFJLEdBQUcsRUFBMkIsQ0FBQztJQXlkekUsQ0FBQztJQXZkQzs7T0FFRztJQUNILGdCQUFnQixDQUNkLFVBQWtCLEVBQ2xCLFlBQW9CLEVBQ3BCLE1BQXFCO1FBRXJCLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUVyQyxxQkFBcUI7UUFDckIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxFQUFFO1lBQzNCLFVBQVU7WUFDVixlQUFlLEVBQUUsQ0FBQztZQUNsQixZQUFZLEVBQUUsQ0FBQztZQUNmLG9CQUFvQixFQUFFLENBQUM7WUFDdkIsZ0JBQWdCLEVBQUUsQ0FBQztZQUNuQixnQkFBZ0IsRUFBRSxRQUFRO1lBQzFCLFdBQVcsRUFBRSxDQUFDO1NBQ2YsQ0FBQyxDQUFDO1FBRUgsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQ2Isd0NBQXdDLFlBQVksYUFBYSxVQUFVLHlCQUF5QixNQUFNLENBQUMsY0FBYyxJQUFJLENBQzlILENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsa0JBQWtCLENBQ3RCLFVBQWtCLEVBQ2xCLFNBQStDLEVBQy9DLGFBQXNCLEVBQ3RCLGFBQXNCO1FBRXRCLE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQzVDLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQztZQUNaLE1BQU0sSUFBSSxLQUFLLENBQUMsc0RBQXNELFVBQVUsRUFBRSxDQUFDLENBQUM7UUFDdEYsQ0FBQztRQUVELE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxFQUFFLGFBQWEsRUFBRSxhQUFhLENBQUMsQ0FBQztRQUN0RSxNQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsbUJBQW1CLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDekQsTUFBTSxlQUFlLEdBQUcsSUFBSSxlQUFlLEVBQUUsQ0FBQztRQUU5QyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxDQUFDLFdBQVcsRUFBRSxlQUFlLENBQUMsQ0FBQztRQUV4RCxNQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLENBQUM7UUFFN0IsSUFBSSxDQUFDO1lBQ0gsTUFBTSxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUMsMEJBQTBCLENBQ2xELFNBQVMsRUFDVCxPQUFPLEVBQ1AsZUFBZSxDQUFDLE1BQU0sRUFDdEIsV0FBVyxDQUNaLENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsU0FBUyxDQUFDO1lBQzdDLElBQUksQ0FBQyxhQUFhLENBQUMsVUFBVSxFQUFFLGFBQWEsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUVyRCxPQUFPLE1BQU0sQ0FBQztRQUNoQixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE1BQU0sYUFBYSxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxTQUFTLENBQUM7WUFDN0MsTUFBTSxTQUFTLEdBQUcsS0FBSyxZQUFZLEtBQUssSUFBSSxLQUFLLENBQUMsSUFBSSxLQUFLLGNBQWMsQ0FBQztZQUUxRSxJQUFJLFNBQVMsRUFBRSxDQUFDO2dCQUNkLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUNkLG9DQUFvQyxVQUFVLFVBQVUsT0FBTyxJQUFJLENBQ3BFLENBQUM7Z0JBQ0YsSUFBSSxDQUFDLGFBQWEsQ0FBQyxVQUFVLEVBQUUsYUFBYSxFQUFFLElBQUksQ0FBQyxDQUFDO1lBQ3RELENBQUM7aUJBQU0sQ0FBQztnQkFDTixJQUFJLENBQUMsYUFBYSxDQUFDLFVBQVUsRUFBRSxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDdkQsQ0FBQztZQUVELE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztnQkFBUyxDQUFDO1lBQ1QsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUM1QyxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLHlCQUF5QixDQUM3QixVQUFrQixFQUNsQixTQUErQyxFQUMvQyxhQUFzQixFQUN0QixhQUFzQjtRQUV0QixNQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUM1QyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDWixNQUFNLElBQUksS0FBSyxDQUFDLHNEQUFzRCxVQUFVLEVBQUUsQ0FBQyxDQUFDO1FBQ3RGLENBQUM7UUFFRCxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sRUFBRSxhQUFhLEVBQUUsYUFBYSxDQUFDLENBQUM7UUFDdEUsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3pELE1BQU0sZUFBZSxHQUFHLElBQUksZUFBZSxFQUFFLENBQUM7UUFFOUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsZUFBZSxDQUFDLENBQUM7UUFFeEQsTUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQzdCLElBQUksUUFBUSxHQUFHLEtBQUssQ0FBQztRQUVyQixJQUFJLENBQUM7WUFDSCxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQywwQkFBMEIsQ0FDbEQsU0FBUyxFQUNULE9BQU8sRUFDUCxlQUFlLENBQUMsTUFBTSxFQUN0QixXQUFXLENBQ1osQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxTQUFTLENBQUM7WUFDN0MsSUFBSSxDQUFDLGFBQWEsQ0FBQyxVQUFVLEVBQUUsYUFBYSxFQUFFLEtBQUssQ0FBQyxDQUFDO1lBRXJELE9BQU87Z0JBQ0wsTUFBTTtnQkFDTixhQUFhO2dCQUNiLFFBQVEsRUFBRSxLQUFLO2FBQ2hCLENBQUM7UUFDSixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE1BQU0sYUFBYSxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxTQUFTLENBQUM7WUFDN0MsUUFBUSxHQUFHLEtBQUssWUFBWSxLQUFLLElBQUksS0FBSyxDQUFDLElBQUksS0FBSyxjQUFjLENBQUM7WUFFbkUsSUFBSSxRQUFRLEVBQUUsQ0FBQztnQkFDYixJQUFJLENBQUMsYUFBYSxDQUFDLFVBQVUsRUFBRSxhQUFhLEVBQUUsSUFBSSxDQUFDLENBQUM7Z0JBRXBELE9BQU87b0JBQ0wsTUFBTSxFQUFFLFNBQWdCO29CQUN4QixhQUFhO29CQUNiLFFBQVEsRUFBRSxJQUFJO2lCQUNmLENBQUM7WUFDSixDQUFDO2lCQUFNLENBQUM7Z0JBQ04sSUFBSSxDQUFDLGFBQWEsQ0FBQyxVQUFVLEVBQUUsYUFBYSxFQUFFLEtBQUssQ0FBQyxDQUFDO2dCQUNyRCxNQUFNLEtBQUssQ0FBQztZQUNkLENBQUM7UUFDSCxDQUFDO2dCQUFTLENBQUM7WUFDVCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQzVDLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMscUJBQXFCLENBQ3pCLFVBQWtCLEVBQ2xCLFNBQStDLEVBQy9DLE9BQWUsRUFDZixhQUFzQjtRQUV0QixNQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUM1QyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDWixNQUFNLElBQUksS0FBSyxDQUFDLHNEQUFzRCxVQUFVLEVBQUUsQ0FBQyxDQUFDO1FBQ3RGLENBQUM7UUFFRCxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixJQUFJLENBQUMsTUFBTSxDQUFDLGtCQUFrQixDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ2xFLE9BQU8sSUFBSSxDQUFDLGtCQUFrQixDQUFDLFVBQVUsRUFBRSxTQUFTLEVBQUUsYUFBYSxDQUFDLENBQUM7UUFDdkUsQ0FBQztRQUVELE1BQU0sZUFBZSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsT0FBTyxHQUFHLENBQUMsRUFBRSxNQUFNLENBQUMsa0JBQWtCLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQ3BGLE1BQU0sT0FBTyxHQUFHLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUUzRCxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FDZixtQ0FBbUMsZUFBZSxhQUFhLE9BQU8sbUJBQW1CLFVBQVUsRUFBRSxDQUN0RyxDQUFDO1FBRUYsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3pELE1BQU0sZUFBZSxHQUFHLElBQUksZUFBZSxFQUFFLENBQUM7UUFFOUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsZUFBZSxDQUFDLENBQUM7UUFFeEQsTUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBRTdCLElBQUksQ0FBQztZQUNILE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLDBCQUEwQixDQUNsRCxTQUFTLEVBQ1QsT0FBTyxFQUNQLGVBQWUsQ0FBQyxNQUFNLEVBQ3RCLFdBQVcsQ0FDWixDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLFNBQVMsQ0FBQztZQUM3QyxJQUFJLENBQUMsYUFBYSxDQUFDLFVBQVUsRUFBRSxhQUFhLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFFckQsT0FBTyxNQUFNLENBQUM7UUFDaEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixNQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsU0FBUyxDQUFDO1lBQzdDLE1BQU0sU0FBUyxHQUFHLEtBQUssWUFBWSxLQUFLLElBQUksS0FBSyxDQUFDLElBQUksS0FBSyxjQUFjLENBQUM7WUFFMUUsSUFBSSxTQUFTLEVBQUUsQ0FBQztnQkFDZCxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FDZCw4Q0FBOEMsVUFBVSxhQUFhLGVBQWUsVUFBVSxPQUFPLElBQUksQ0FDMUcsQ0FBQztnQkFDRixJQUFJLENBQUMsYUFBYSxDQUFDLFVBQVUsRUFBRSxhQUFhLEVBQUUsSUFBSSxDQUFDLENBQUM7WUFDdEQsQ0FBQztpQkFBTSxDQUFDO2dCQUNOLElBQUksQ0FBQyxhQUFhLENBQUMsVUFBVSxFQUFFLGFBQWEsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUN2RCxDQUFDO1lBRUQsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO2dCQUFTLENBQUM7WUFDVCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQzVDLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCx3QkFBd0IsQ0FBQyxVQUFrQjtRQUN6QyxJQUFJLGNBQWMsR0FBRyxDQUFDLENBQUM7UUFFdkIsS0FBSyxNQUFNLENBQUMsV0FBVyxFQUFFLFVBQVUsQ0FBQyxJQUFJLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1lBQzlELElBQUksV0FBVyxDQUFDLFVBQVUsQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDO2dCQUN2QyxVQUFVLENBQUMsS0FBSyxFQUFFLENBQUM7Z0JBQ25CLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLENBQUM7Z0JBQzFDLGNBQWMsRUFBRSxDQUFDO1lBQ25CLENBQUM7UUFDSCxDQUFDO1FBRUQsSUFBSSxjQUFjLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDdkIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsYUFBYSxjQUFjLG1DQUFtQyxVQUFVLEVBQUUsQ0FBQyxDQUFDO1FBQzlGLENBQUM7UUFFRCxPQUFPLGNBQWMsQ0FBQztJQUN4QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxtQkFBbUI7UUFDakIsTUFBTSxlQUFlLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQztRQUVuRCxLQUFLLE1BQU0sQ0FBQyxXQUFXLEVBQUUsVUFBVSxDQUFDLElBQUksSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUM7WUFDOUQsVUFBVSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ3JCLENBQUM7UUFFRCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxFQUFFLENBQUM7UUFFOUIsSUFBSSxlQUFlLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDeEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsYUFBYSxlQUFlLG9CQUFvQixDQUFDLENBQUM7UUFDcEUsQ0FBQztRQUVELE9BQU8sZUFBZSxDQUFDO0lBQ3pCLENBQUM7SUFFRDs7T0FFRztJQUNILGtCQUFrQixDQUFDLFVBQWtCO1FBQ25DLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLElBQUksSUFBSSxDQUFDO0lBQzlDLENBQUM7SUFFRDs7T0FFRztJQUNILHFCQUFxQjtRQUNuQixPQUFPLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDO0lBQzNDLENBQUM7SUFFRDs7T0FFRztJQUNILG9CQUFvQixDQUFDLFVBQWtCO1FBQ3JDLE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQzdDLElBQUksT0FBTyxFQUFFLENBQUM7WUFDWixJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUU7Z0JBQzNCLFVBQVU7Z0JBQ1YsZUFBZSxFQUFFLENBQUM7Z0JBQ2xCLFlBQVksRUFBRSxDQUFDO2dCQUNmLG9CQUFvQixFQUFFLENBQUM7Z0JBQ3ZCLGdCQUFnQixFQUFFLENBQUM7Z0JBQ25CLGdCQUFnQixFQUFFLFFBQVE7Z0JBQzFCLFdBQVcsRUFBRSxDQUFDO2FBQ2YsQ0FBQyxDQUFDO1lBQ0gsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsc0NBQXNDLFVBQVUsRUFBRSxDQUFDLENBQUM7UUFDdEUsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILGVBQWU7UUFDYixLQUFLLE1BQU0sQ0FBQyxVQUFVLENBQUMsSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDeEMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3hDLENBQUM7UUFDRCxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyx5Q0FBeUMsQ0FBQyxDQUFDO0lBQzdELENBQUM7SUFFRDs7T0FFRztJQUNILG9CQUFvQixDQUFDLFVBQWtCLEVBQUUsTUFBOEI7UUFDckUsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDcEQsSUFBSSxjQUFjLEVBQUUsQ0FBQztZQUNuQixNQUFNLGFBQWEsR0FBRyxFQUFFLEdBQUcsY0FBYyxFQUFFLEdBQUcsTUFBTSxFQUFFLENBQUM7WUFDdkQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxFQUFFLGFBQWEsQ0FBQyxDQUFDO1lBRTVDLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLDhDQUE4QyxVQUFVLEVBQUUsQ0FBQyxDQUFDO1FBQzlFLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxrQkFBa0IsQ0FBQyxVQUFrQjtRQUNuQyxpREFBaUQ7UUFDakQsSUFBSSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBRTFDLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ2hDLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ2hDLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLG1EQUFtRCxVQUFVLEVBQUUsQ0FBQyxDQUFDO0lBQ25GLENBQUM7SUFFRDs7T0FFRztJQUNILGlCQUFpQixDQUFDLFVBQWtCO1FBQ2xDLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLElBQUksSUFBSSxDQUFDO0lBQzlDLENBQUM7SUFFRDs7T0FFRztJQUNILHVCQUF1QixDQUFDLFVBQWtCO1FBQ3hDLElBQUksS0FBSyxHQUFHLENBQUMsQ0FBQztRQUNkLEtBQUssTUFBTSxXQUFXLElBQUksSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksRUFBRSxFQUFFLENBQUM7WUFDdkQsSUFBSSxXQUFXLENBQUMsVUFBVSxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUM7Z0JBQ3ZDLEtBQUssRUFBRSxDQUFDO1lBQ1YsQ0FBQztRQUNILENBQUM7UUFDRCxPQUFPLEtBQUssQ0FBQztJQUNmLENBQUM7SUFFRDs7T0FFRztJQUNILDRCQUE0QjtRQUMxQixPQUFPLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUM7SUFDcEMsQ0FBQztJQUVEOztPQUVHO0lBQ0ssS0FBSyxDQUFDLDBCQUEwQixDQUN0QyxTQUErQyxFQUMvQyxTQUFpQixFQUNqQixNQUFtQixFQUNuQixXQUFtQjtRQUVuQixPQUFPLElBQUksT0FBTyxDQUFJLENBQUMsT0FBTyxFQUFFLE1BQU0sRUFBRSxFQUFFO1lBQ3hDLE1BQU0sYUFBYSxHQUFHLFVBQVUsQ0FBQyxHQUFHLEVBQUU7Z0JBQ3BDLE1BQU0sS0FBSyxHQUFHLElBQUksS0FBSyxDQUFDLDZCQUE2QixTQUFTLElBQUksQ0FBQyxDQUFDO2dCQUNwRSxLQUFLLENBQUMsSUFBSSxHQUFHLGNBQWMsQ0FBQztnQkFDNUIsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ2hCLENBQUMsRUFBRSxTQUFTLENBQUMsQ0FBQztZQUVkLHNCQUFzQjtZQUN0QixNQUFNLFlBQVksR0FBRyxHQUFHLEVBQUU7Z0JBQ3hCLFlBQVksQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFDNUIsTUFBTSxLQUFLLEdBQUcsSUFBSSxLQUFLLENBQUMseUJBQXlCLENBQUMsQ0FBQztnQkFDbkQsS0FBSyxDQUFDLElBQUksR0FBRyxZQUFZLENBQUM7Z0JBQzFCLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNoQixDQUFDLENBQUM7WUFFRixJQUFJLE1BQU0sQ0FBQyxPQUFPLEVBQUUsQ0FBQztnQkFDbkIsWUFBWSxFQUFFLENBQUM7Z0JBQ2YsT0FBTztZQUNULENBQUM7WUFFRCxNQUFNLENBQUMsZ0JBQWdCLENBQUMsT0FBTyxFQUFFLFlBQVksQ0FBQyxDQUFDO1lBRS9DLFNBQVMsQ0FBQyxNQUFNLENBQUM7aUJBQ2QsSUFBSSxDQUFDLENBQUMsTUFBTSxFQUFFLEVBQUU7Z0JBQ2YsWUFBWSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUM1QixNQUFNLENBQUMsbUJBQW1CLENBQUMsT0FBTyxFQUFFLFlBQVksQ0FBQyxDQUFDO2dCQUNsRCxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDbEIsQ0FBQyxDQUFDO2lCQUNELEtBQUssQ0FBQyxDQUFDLEtBQUssRUFBRSxFQUFFO2dCQUNmLFlBQVksQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFDNUIsTUFBTSxDQUFDLG1CQUFtQixDQUFDLE9BQU8sRUFBRSxZQUFZLENBQUMsQ0FBQztnQkFDbEQsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ2hCLENBQUMsQ0FBQyxDQUFDO1FBQ1AsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxVQUFVLENBQ2hCLE1BQXFCLEVBQ3JCLGFBQXNCLEVBQ3RCLGFBQXNCO1FBRXRCLElBQUksYUFBYSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ2hDLE9BQU8sYUFBYSxDQUFDO1FBQ3ZCLENBQUM7UUFFRCxJQUFJLGFBQWEsSUFBSSxNQUFNLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLEVBQUUsQ0FBQztZQUM3RCxPQUFPLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUNqRCxDQUFDO1FBRUQsT0FBTyxNQUFNLENBQUMsY0FBYyxDQUFDO0lBQy9CLENBQUM7SUFFRDs7T0FFRztJQUNLLG1CQUFtQixDQUFDLFVBQWtCO1FBQzVDLE9BQU8sR0FBRyxVQUFVLElBQUksSUFBSSxDQUFDLEdBQUcsRUFBRSxJQUFJLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDO0lBQ2xGLENBQUM7SUFFRDs7T0FFRztJQUNLLGFBQWEsQ0FBQyxVQUFrQixFQUFFLGFBQXFCLEVBQUUsUUFBaUI7UUFDaEYsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDN0MsSUFBSSxPQUFPLEVBQUUsQ0FBQztZQUNaLE9BQU8sQ0FBQyxlQUFlLEVBQUUsQ0FBQztZQUUxQixJQUFJLFFBQVEsRUFBRSxDQUFDO2dCQUNiLE9BQU8sQ0FBQyxZQUFZLEVBQUUsQ0FBQztnQkFDdkIsT0FBTyxDQUFDLGVBQWUsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1lBQ3ZDLENBQUM7WUFFRCxtQ0FBbUM7WUFDbkMsT0FBTyxDQUFDLG9CQUFvQjtnQkFDMUIsQ0FBQyxPQUFPLENBQUMsb0JBQW9CLEdBQUcsQ0FBQyxPQUFPLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQyxHQUFHLGFBQWEsQ0FBQztvQkFDOUUsT0FBTyxDQUFDLGVBQWUsQ0FBQztZQUUxQixPQUFPLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLEVBQUUsYUFBYSxDQUFDLENBQUM7WUFDN0UsT0FBTyxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLGdCQUFnQixFQUFFLGFBQWEsQ0FBQyxDQUFDO1lBRTdFLHlCQUF5QjtZQUN6QixPQUFPLENBQUMsV0FBVyxHQUFHLE9BQU8sQ0FBQyxZQUFZLEdBQUcsT0FBTyxDQUFDLGVBQWUsQ0FBQztZQUVyRSxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFDeEMsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxtQkFBbUIsQ0FBQyxpQkFBeUIsS0FBSztRQUN2RCxPQUFPO1lBQ0wsY0FBYztZQUNkLGlCQUFpQixFQUFFLEVBQUU7WUFDckIsa0JBQWtCLEVBQUUsQ0FBQyxjQUFjLEVBQUUsY0FBYyxHQUFHLEdBQUcsRUFBRSxjQUFjLEdBQUcsQ0FBQyxDQUFDO1lBQzlFLGdCQUFnQixFQUFFLElBQUk7U0FDdkIsQ0FBQztJQUNKLENBQUM7SUFFRCxNQUFNLENBQUMsaUJBQWlCLENBQUMsaUJBQXlCLElBQUk7UUFDcEQsT0FBTztZQUNMLGNBQWM7WUFDZCxpQkFBaUIsRUFBRTtnQkFDakIsZ0JBQWdCLEVBQUUsSUFBSTtnQkFDdEIsY0FBYyxFQUFFLElBQUk7YUFDckI7WUFDRCxrQkFBa0IsRUFBRSxDQUFDLGNBQWMsRUFBRSxjQUFjLEdBQUcsR0FBRyxFQUFFLGNBQWMsR0FBRyxHQUFHLENBQUM7WUFDaEYsZ0JBQWdCLEVBQUUsSUFBSTtTQUN2QixDQUFDO0lBQ0osQ0FBQztJQUVELE1BQU0sQ0FBQyx1QkFBdUIsQ0FBQyxpQkFBeUIsTUFBTTtRQUM1RCxPQUFPO1lBQ0wsY0FBYztZQUNkLGlCQUFpQixFQUFFO2dCQUNqQixnQkFBZ0IsRUFBRSxNQUFNLEVBQUUsWUFBWTtnQkFDdEMsZ0JBQWdCLEVBQUUsTUFBTSxFQUFFLFlBQVk7YUFDdkM7WUFDRCxrQkFBa0IsRUFBRSxDQUFDLGNBQWMsRUFBRSxjQUFjLEdBQUcsR0FBRyxFQUFFLGNBQWMsR0FBRyxDQUFDLEVBQUUsY0FBYyxHQUFHLENBQUMsQ0FBQztZQUNsRyxnQkFBZ0IsRUFBRSxJQUFJO1NBQ3ZCLENBQUM7SUFDSixDQUFDO0NBQ0YsQ0FBQTtBQTdkWSw0Q0FBZ0I7MkJBQWhCLGdCQUFnQjtJQUQ1QixJQUFBLG1CQUFVLEdBQUU7R0FDQSxnQkFBZ0IsQ0E2ZDVCIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcbW9kdWxlc1xcYWlcXGFwcGxpY2F0aW9uXFxzZXJ2aWNlc1xccmVzaWxpZW5jZVxcdGltZW91dC5zZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluamVjdGFibGUsIExvZ2dlciB9IGZyb20gJ0BuZXN0anMvY29tbW9uJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVGltZW91dENvbmZpZyB7XHJcbiAgZGVmYXVsdFRpbWVvdXQ6IG51bWJlcjsgLy8gRGVmYXVsdCB0aW1lb3V0IGluIG1pbGxpc2Vjb25kc1xyXG4gIG9wZXJhdGlvblRpbWVvdXRzOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+OyAvLyBPcGVyYXRpb24tc3BlY2lmaWMgdGltZW91dHNcclxuICBlc2NhbGF0aW9uVGltZW91dHM6IG51bWJlcltdOyAvLyBFc2NhbGF0aW9uIHRpbWVvdXRzIGZvciByZXRyaWVzXHJcbiAgZW5hYmxlRXNjYWxhdGlvbjogYm9vbGVhbjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBUaW1lb3V0TWV0cmljcyB7XHJcbiAgcHJvdmlkZXJJZDogc3RyaW5nO1xyXG4gIHRvdGFsT3BlcmF0aW9uczogbnVtYmVyO1xyXG4gIHRpbWVvdXRDb3VudDogbnVtYmVyO1xyXG4gIGF2ZXJhZ2VFeGVjdXRpb25UaW1lOiBudW1iZXI7XHJcbiAgbWF4RXhlY3V0aW9uVGltZTogbnVtYmVyO1xyXG4gIG1pbkV4ZWN1dGlvblRpbWU6IG51bWJlcjtcclxuICB0aW1lb3V0UmF0ZTogbnVtYmVyO1xyXG4gIGxhc3RUaW1lb3V0VGltZT86IERhdGU7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVGltZW91dFJlc3VsdDxUPiB7XHJcbiAgcmVzdWx0OiBUO1xyXG4gIGV4ZWN1dGlvblRpbWU6IG51bWJlcjtcclxuICB0aW1lZE91dDogYm9vbGVhbjtcclxuICBlc2NhbGF0aW9uTGV2ZWw/OiBudW1iZXI7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBBSS1zcGVjaWZpYyB0aW1lb3V0IHNlcnZpY2UgZm9yIG1hbmFnaW5nIG9wZXJhdGlvbiB0aW1lb3V0c1xyXG4gKi9cclxuQEluamVjdGFibGUoKVxyXG5leHBvcnQgY2xhc3MgQUlUaW1lb3V0U2VydmljZSB7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBsb2dnZXIgPSBuZXcgTG9nZ2VyKEFJVGltZW91dFNlcnZpY2UubmFtZSk7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBjb25maWdzID0gbmV3IE1hcDxzdHJpbmcsIFRpbWVvdXRDb25maWc+KCk7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBtZXRyaWNzID0gbmV3IE1hcDxzdHJpbmcsIFRpbWVvdXRNZXRyaWNzPigpO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgYWN0aXZlT3BlcmF0aW9ucyA9IG5ldyBNYXA8c3RyaW5nLCBBYm9ydENvbnRyb2xsZXI+KCk7XHJcblxyXG4gIC8qKlxyXG4gICAqIFJlZ2lzdGVyIHRpbWVvdXQgY29uZmlndXJhdGlvbiBmb3IgYW4gQUkgcHJvdmlkZXJcclxuICAgKi9cclxuICByZWdpc3RlclByb3ZpZGVyKFxyXG4gICAgcHJvdmlkZXJJZDogc3RyaW5nLFxyXG4gICAgcHJvdmlkZXJUeXBlOiBzdHJpbmcsXHJcbiAgICBjb25maWc6IFRpbWVvdXRDb25maWdcclxuICApOiB2b2lkIHtcclxuICAgIHRoaXMuY29uZmlncy5zZXQocHJvdmlkZXJJZCwgY29uZmlnKTtcclxuICAgIFxyXG4gICAgLy8gSW5pdGlhbGl6ZSBtZXRyaWNzXHJcbiAgICB0aGlzLm1ldHJpY3Muc2V0KHByb3ZpZGVySWQsIHtcclxuICAgICAgcHJvdmlkZXJJZCxcclxuICAgICAgdG90YWxPcGVyYXRpb25zOiAwLFxyXG4gICAgICB0aW1lb3V0Q291bnQ6IDAsXHJcbiAgICAgIGF2ZXJhZ2VFeGVjdXRpb25UaW1lOiAwLFxyXG4gICAgICBtYXhFeGVjdXRpb25UaW1lOiAwLFxyXG4gICAgICBtaW5FeGVjdXRpb25UaW1lOiBJbmZpbml0eSxcclxuICAgICAgdGltZW91dFJhdGU6IDAsXHJcbiAgICB9KTtcclxuXHJcbiAgICB0aGlzLmxvZ2dlci5sb2coXHJcbiAgICAgIGBSZWdpc3RlcmVkIHRpbWVvdXQgY29uZmlndXJhdGlvbiBmb3IgJHtwcm92aWRlclR5cGV9IHByb3ZpZGVyICR7cHJvdmlkZXJJZH0gd2l0aCBkZWZhdWx0IHRpbWVvdXQgJHtjb25maWcuZGVmYXVsdFRpbWVvdXR9bXNgXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRXhlY3V0ZSBhbiBvcGVyYXRpb24gd2l0aCB0aW1lb3V0IHByb3RlY3Rpb25cclxuICAgKi9cclxuICBhc3luYyBleGVjdXRlV2l0aFRpbWVvdXQ8VD4oXHJcbiAgICBwcm92aWRlcklkOiBzdHJpbmcsXHJcbiAgICBvcGVyYXRpb246IChzaWduYWw/OiBBYm9ydFNpZ25hbCkgPT4gUHJvbWlzZTxUPixcclxuICAgIG9wZXJhdGlvblR5cGU/OiBzdHJpbmcsXHJcbiAgICBjdXN0b21UaW1lb3V0PzogbnVtYmVyXHJcbiAgKTogUHJvbWlzZTxUPiB7XHJcbiAgICBjb25zdCBjb25maWcgPSB0aGlzLmNvbmZpZ3MuZ2V0KHByb3ZpZGVySWQpO1xyXG4gICAgaWYgKCFjb25maWcpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUaW1lb3V0IGNvbmZpZ3VyYXRpb24gbm90IHJlZ2lzdGVyZWQgZm9yIHByb3ZpZGVyOiAke3Byb3ZpZGVySWR9YCk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgdGltZW91dCA9IHRoaXMuZ2V0VGltZW91dChjb25maWcsIG9wZXJhdGlvblR5cGUsIGN1c3RvbVRpbWVvdXQpO1xyXG4gICAgY29uc3Qgb3BlcmF0aW9uSWQgPSB0aGlzLmdlbmVyYXRlT3BlcmF0aW9uSWQocHJvdmlkZXJJZCk7XHJcbiAgICBjb25zdCBhYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XHJcbiAgICBcclxuICAgIHRoaXMuYWN0aXZlT3BlcmF0aW9ucy5zZXQob3BlcmF0aW9uSWQsIGFib3J0Q29udHJvbGxlcik7XHJcblxyXG4gICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmV4ZWN1dGVXaXRoVGltZW91dEludGVybmFsKFxyXG4gICAgICAgIG9wZXJhdGlvbixcclxuICAgICAgICB0aW1lb3V0LFxyXG4gICAgICAgIGFib3J0Q29udHJvbGxlci5zaWduYWwsXHJcbiAgICAgICAgb3BlcmF0aW9uSWRcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGNvbnN0IGV4ZWN1dGlvblRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xyXG4gICAgICB0aGlzLnVwZGF0ZU1ldHJpY3MocHJvdmlkZXJJZCwgZXhlY3V0aW9uVGltZSwgZmFsc2UpO1xyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHJlc3VsdDtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnN0IGV4ZWN1dGlvblRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xyXG4gICAgICBjb25zdCBpc1RpbWVvdXQgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIGVycm9yLm5hbWUgPT09ICdUaW1lb3V0RXJyb3InO1xyXG4gICAgICBcclxuICAgICAgaWYgKGlzVGltZW91dCkge1xyXG4gICAgICAgIHRoaXMubG9nZ2VyLndhcm4oXHJcbiAgICAgICAgICBgT3BlcmF0aW9uIHRpbWVkIG91dCBmb3IgcHJvdmlkZXIgJHtwcm92aWRlcklkfSBhZnRlciAke3RpbWVvdXR9bXNgXHJcbiAgICAgICAgKTtcclxuICAgICAgICB0aGlzLnVwZGF0ZU1ldHJpY3MocHJvdmlkZXJJZCwgZXhlY3V0aW9uVGltZSwgdHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhpcy51cGRhdGVNZXRyaWNzKHByb3ZpZGVySWQsIGV4ZWN1dGlvblRpbWUsIGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICB0aGlzLmFjdGl2ZU9wZXJhdGlvbnMuZGVsZXRlKG9wZXJhdGlvbklkKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEV4ZWN1dGUgd2l0aCB0aW1lb3V0IGFuZCBkZXRhaWxlZCByZXN1bHQgaW5mb3JtYXRpb25cclxuICAgKi9cclxuICBhc3luYyBleGVjdXRlV2l0aFRpbWVvdXREZXRhaWxzPFQ+KFxyXG4gICAgcHJvdmlkZXJJZDogc3RyaW5nLFxyXG4gICAgb3BlcmF0aW9uOiAoc2lnbmFsPzogQWJvcnRTaWduYWwpID0+IFByb21pc2U8VD4sXHJcbiAgICBvcGVyYXRpb25UeXBlPzogc3RyaW5nLFxyXG4gICAgY3VzdG9tVGltZW91dD86IG51bWJlclxyXG4gICk6IFByb21pc2U8VGltZW91dFJlc3VsdDxUPj4ge1xyXG4gICAgY29uc3QgY29uZmlnID0gdGhpcy5jb25maWdzLmdldChwcm92aWRlcklkKTtcclxuICAgIGlmICghY29uZmlnKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGltZW91dCBjb25maWd1cmF0aW9uIG5vdCByZWdpc3RlcmVkIGZvciBwcm92aWRlcjogJHtwcm92aWRlcklkfWApO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRpbWVvdXQgPSB0aGlzLmdldFRpbWVvdXQoY29uZmlnLCBvcGVyYXRpb25UeXBlLCBjdXN0b21UaW1lb3V0KTtcclxuICAgIGNvbnN0IG9wZXJhdGlvbklkID0gdGhpcy5nZW5lcmF0ZU9wZXJhdGlvbklkKHByb3ZpZGVySWQpO1xyXG4gICAgY29uc3QgYWJvcnRDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xyXG4gICAgXHJcbiAgICB0aGlzLmFjdGl2ZU9wZXJhdGlvbnMuc2V0KG9wZXJhdGlvbklkLCBhYm9ydENvbnRyb2xsZXIpO1xyXG5cclxuICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XHJcbiAgICBsZXQgdGltZWRPdXQgPSBmYWxzZTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmV4ZWN1dGVXaXRoVGltZW91dEludGVybmFsKFxyXG4gICAgICAgIG9wZXJhdGlvbixcclxuICAgICAgICB0aW1lb3V0LFxyXG4gICAgICAgIGFib3J0Q29udHJvbGxlci5zaWduYWwsXHJcbiAgICAgICAgb3BlcmF0aW9uSWRcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGNvbnN0IGV4ZWN1dGlvblRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xyXG4gICAgICB0aGlzLnVwZGF0ZU1ldHJpY3MocHJvdmlkZXJJZCwgZXhlY3V0aW9uVGltZSwgZmFsc2UpO1xyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICByZXN1bHQsXHJcbiAgICAgICAgZXhlY3V0aW9uVGltZSxcclxuICAgICAgICB0aW1lZE91dDogZmFsc2UsXHJcbiAgICAgIH07XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zdCBleGVjdXRpb25UaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcclxuICAgICAgdGltZWRPdXQgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIGVycm9yLm5hbWUgPT09ICdUaW1lb3V0RXJyb3InO1xyXG4gICAgICBcclxuICAgICAgaWYgKHRpbWVkT3V0KSB7XHJcbiAgICAgICAgdGhpcy51cGRhdGVNZXRyaWNzKHByb3ZpZGVySWQsIGV4ZWN1dGlvblRpbWUsIHRydWUpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICByZXN1bHQ6IHVuZGVmaW5lZCBhcyBhbnksXHJcbiAgICAgICAgICBleGVjdXRpb25UaW1lLFxyXG4gICAgICAgICAgdGltZWRPdXQ6IHRydWUsXHJcbiAgICAgICAgfTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0aGlzLnVwZGF0ZU1ldHJpY3MocHJvdmlkZXJJZCwgZXhlY3V0aW9uVGltZSwgZmFsc2UpO1xyXG4gICAgICAgIHRocm93IGVycm9yO1xyXG4gICAgICB9XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICB0aGlzLmFjdGl2ZU9wZXJhdGlvbnMuZGVsZXRlKG9wZXJhdGlvbklkKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEV4ZWN1dGUgd2l0aCBlc2NhbGF0aW5nIHRpbWVvdXRzIGZvciByZXRyeSBzY2VuYXJpb3NcclxuICAgKi9cclxuICBhc3luYyBleGVjdXRlV2l0aEVzY2FsYXRpb248VD4oXHJcbiAgICBwcm92aWRlcklkOiBzdHJpbmcsXHJcbiAgICBvcGVyYXRpb246IChzaWduYWw/OiBBYm9ydFNpZ25hbCkgPT4gUHJvbWlzZTxUPixcclxuICAgIGF0dGVtcHQ6IG51bWJlcixcclxuICAgIG9wZXJhdGlvblR5cGU/OiBzdHJpbmdcclxuICApOiBQcm9taXNlPFQ+IHtcclxuICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuY29uZmlncy5nZXQocHJvdmlkZXJJZCk7XHJcbiAgICBpZiAoIWNvbmZpZykge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRpbWVvdXQgY29uZmlndXJhdGlvbiBub3QgcmVnaXN0ZXJlZCBmb3IgcHJvdmlkZXI6ICR7cHJvdmlkZXJJZH1gKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIWNvbmZpZy5lbmFibGVFc2NhbGF0aW9uIHx8ICFjb25maWcuZXNjYWxhdGlvblRpbWVvdXRzLmxlbmd0aCkge1xyXG4gICAgICByZXR1cm4gdGhpcy5leGVjdXRlV2l0aFRpbWVvdXQocHJvdmlkZXJJZCwgb3BlcmF0aW9uLCBvcGVyYXRpb25UeXBlKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBlc2NhbGF0aW9uTGV2ZWwgPSBNYXRoLm1pbihhdHRlbXB0IC0gMSwgY29uZmlnLmVzY2FsYXRpb25UaW1lb3V0cy5sZW5ndGggLSAxKTtcclxuICAgIGNvbnN0IHRpbWVvdXQgPSBjb25maWcuZXNjYWxhdGlvblRpbWVvdXRzW2VzY2FsYXRpb25MZXZlbF07XHJcbiAgICBcclxuICAgIHRoaXMubG9nZ2VyLmRlYnVnKFxyXG4gICAgICBgRXhlY3V0aW5nIHdpdGggZXNjYWxhdGlvbiBsZXZlbCAke2VzY2FsYXRpb25MZXZlbH0sIHRpbWVvdXQgJHt0aW1lb3V0fW1zIGZvciBwcm92aWRlciAke3Byb3ZpZGVySWR9YFxyXG4gICAgKTtcclxuXHJcbiAgICBjb25zdCBvcGVyYXRpb25JZCA9IHRoaXMuZ2VuZXJhdGVPcGVyYXRpb25JZChwcm92aWRlcklkKTtcclxuICAgIGNvbnN0IGFib3J0Q29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcclxuICAgIFxyXG4gICAgdGhpcy5hY3RpdmVPcGVyYXRpb25zLnNldChvcGVyYXRpb25JZCwgYWJvcnRDb250cm9sbGVyKTtcclxuXHJcbiAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMuZXhlY3V0ZVdpdGhUaW1lb3V0SW50ZXJuYWwoXHJcbiAgICAgICAgb3BlcmF0aW9uLFxyXG4gICAgICAgIHRpbWVvdXQsXHJcbiAgICAgICAgYWJvcnRDb250cm9sbGVyLnNpZ25hbCxcclxuICAgICAgICBvcGVyYXRpb25JZFxyXG4gICAgICApO1xyXG5cclxuICAgICAgY29uc3QgZXhlY3V0aW9uVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XHJcbiAgICAgIHRoaXMudXBkYXRlTWV0cmljcyhwcm92aWRlcklkLCBleGVjdXRpb25UaW1lLCBmYWxzZSk7XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gcmVzdWx0O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc3QgZXhlY3V0aW9uVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XHJcbiAgICAgIGNvbnN0IGlzVGltZW91dCA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubmFtZSA9PT0gJ1RpbWVvdXRFcnJvcic7XHJcbiAgICAgIFxyXG4gICAgICBpZiAoaXNUaW1lb3V0KSB7XHJcbiAgICAgICAgdGhpcy5sb2dnZXIud2FybihcclxuICAgICAgICAgIGBFc2NhbGF0ZWQgb3BlcmF0aW9uIHRpbWVkIG91dCBmb3IgcHJvdmlkZXIgJHtwcm92aWRlcklkfSBhdCBsZXZlbCAke2VzY2FsYXRpb25MZXZlbH0gYWZ0ZXIgJHt0aW1lb3V0fW1zYFxyXG4gICAgICAgICk7XHJcbiAgICAgICAgdGhpcy51cGRhdGVNZXRyaWNzKHByb3ZpZGVySWQsIGV4ZWN1dGlvblRpbWUsIHRydWUpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRoaXMudXBkYXRlTWV0cmljcyhwcm92aWRlcklkLCBleGVjdXRpb25UaW1lLCBmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgdGhpcy5hY3RpdmVPcGVyYXRpb25zLmRlbGV0ZShvcGVyYXRpb25JZCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYW5jZWwgYWxsIGFjdGl2ZSBvcGVyYXRpb25zIGZvciBhIHByb3ZpZGVyXHJcbiAgICovXHJcbiAgY2FuY2VsUHJvdmlkZXJPcGVyYXRpb25zKHByb3ZpZGVySWQ6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgICBsZXQgY2FuY2VsbGVkQ291bnQgPSAwO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IFtvcGVyYXRpb25JZCwgY29udHJvbGxlcl0gb2YgdGhpcy5hY3RpdmVPcGVyYXRpb25zKSB7XHJcbiAgICAgIGlmIChvcGVyYXRpb25JZC5zdGFydHNXaXRoKHByb3ZpZGVySWQpKSB7XHJcbiAgICAgICAgY29udHJvbGxlci5hYm9ydCgpO1xyXG4gICAgICAgIHRoaXMuYWN0aXZlT3BlcmF0aW9ucy5kZWxldGUob3BlcmF0aW9uSWQpO1xyXG4gICAgICAgIGNhbmNlbGxlZENvdW50Kys7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAoY2FuY2VsbGVkQ291bnQgPiAwKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmxvZyhgQ2FuY2VsbGVkICR7Y2FuY2VsbGVkQ291bnR9IGFjdGl2ZSBvcGVyYXRpb25zIGZvciBwcm92aWRlciAke3Byb3ZpZGVySWR9YCk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGNhbmNlbGxlZENvdW50O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FuY2VsIGFsbCBhY3RpdmUgb3BlcmF0aW9uc1xyXG4gICAqL1xyXG4gIGNhbmNlbEFsbE9wZXJhdGlvbnMoKTogbnVtYmVyIHtcclxuICAgIGNvbnN0IHRvdGFsT3BlcmF0aW9ucyA9IHRoaXMuYWN0aXZlT3BlcmF0aW9ucy5zaXplO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IFtvcGVyYXRpb25JZCwgY29udHJvbGxlcl0gb2YgdGhpcy5hY3RpdmVPcGVyYXRpb25zKSB7XHJcbiAgICAgIGNvbnRyb2xsZXIuYWJvcnQoKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgdGhpcy5hY3RpdmVPcGVyYXRpb25zLmNsZWFyKCk7XHJcblxyXG4gICAgaWYgKHRvdGFsT3BlcmF0aW9ucyA+IDApIHtcclxuICAgICAgdGhpcy5sb2dnZXIubG9nKGBDYW5jZWxsZWQgJHt0b3RhbE9wZXJhdGlvbnN9IGFjdGl2ZSBvcGVyYXRpb25zYCk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHRvdGFsT3BlcmF0aW9ucztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aW1lb3V0IG1ldHJpY3MgZm9yIGEgc3BlY2lmaWMgcHJvdmlkZXJcclxuICAgKi9cclxuICBnZXRQcm92aWRlck1ldHJpY3MocHJvdmlkZXJJZDogc3RyaW5nKTogVGltZW91dE1ldHJpY3MgfCBudWxsIHtcclxuICAgIHJldHVybiB0aGlzLm1ldHJpY3MuZ2V0KHByb3ZpZGVySWQpIHx8IG51bGw7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgbWV0cmljcyBmb3IgYWxsIHJlZ2lzdGVyZWQgcHJvdmlkZXJzXHJcbiAgICovXHJcbiAgZ2V0QWxsUHJvdmlkZXJNZXRyaWNzKCk6IFRpbWVvdXRNZXRyaWNzW10ge1xyXG4gICAgcmV0dXJuIEFycmF5LmZyb20odGhpcy5tZXRyaWNzLnZhbHVlcygpKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFJlc2V0IG1ldHJpY3MgZm9yIGEgc3BlY2lmaWMgcHJvdmlkZXJcclxuICAgKi9cclxuICByZXNldFByb3ZpZGVyTWV0cmljcyhwcm92aWRlcklkOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIGNvbnN0IG1ldHJpY3MgPSB0aGlzLm1ldHJpY3MuZ2V0KHByb3ZpZGVySWQpO1xyXG4gICAgaWYgKG1ldHJpY3MpIHtcclxuICAgICAgdGhpcy5tZXRyaWNzLnNldChwcm92aWRlcklkLCB7XHJcbiAgICAgICAgcHJvdmlkZXJJZCxcclxuICAgICAgICB0b3RhbE9wZXJhdGlvbnM6IDAsXHJcbiAgICAgICAgdGltZW91dENvdW50OiAwLFxyXG4gICAgICAgIGF2ZXJhZ2VFeGVjdXRpb25UaW1lOiAwLFxyXG4gICAgICAgIG1heEV4ZWN1dGlvblRpbWU6IDAsXHJcbiAgICAgICAgbWluRXhlY3V0aW9uVGltZTogSW5maW5pdHksXHJcbiAgICAgICAgdGltZW91dFJhdGU6IDAsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aGlzLmxvZ2dlci5sb2coYFJlc2V0IHRpbWVvdXQgbWV0cmljcyBmb3IgcHJvdmlkZXIgJHtwcm92aWRlcklkfWApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogUmVzZXQgbWV0cmljcyBmb3IgYWxsIHByb3ZpZGVyc1xyXG4gICAqL1xyXG4gIHJlc2V0QWxsTWV0cmljcygpOiB2b2lkIHtcclxuICAgIGZvciAoY29uc3QgW3Byb3ZpZGVySWRdIG9mIHRoaXMubWV0cmljcykge1xyXG4gICAgICB0aGlzLnJlc2V0UHJvdmlkZXJNZXRyaWNzKHByb3ZpZGVySWQpO1xyXG4gICAgfVxyXG4gICAgdGhpcy5sb2dnZXIubG9nKCdSZXNldCB0aW1lb3V0IG1ldHJpY3MgZm9yIGFsbCBwcm92aWRlcnMnKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFVwZGF0ZSBwcm92aWRlciBjb25maWd1cmF0aW9uXHJcbiAgICovXHJcbiAgdXBkYXRlUHJvdmlkZXJDb25maWcocHJvdmlkZXJJZDogc3RyaW5nLCBjb25maWc6IFBhcnRpYWw8VGltZW91dENvbmZpZz4pOiB2b2lkIHtcclxuICAgIGNvbnN0IGV4aXN0aW5nQ29uZmlnID0gdGhpcy5jb25maWdzLmdldChwcm92aWRlcklkKTtcclxuICAgIGlmIChleGlzdGluZ0NvbmZpZykge1xyXG4gICAgICBjb25zdCB1cGRhdGVkQ29uZmlnID0geyAuLi5leGlzdGluZ0NvbmZpZywgLi4uY29uZmlnIH07XHJcbiAgICAgIHRoaXMuY29uZmlncy5zZXQocHJvdmlkZXJJZCwgdXBkYXRlZENvbmZpZyk7XHJcbiAgICAgIFxyXG4gICAgICB0aGlzLmxvZ2dlci5sb2coYFVwZGF0ZWQgdGltZW91dCBjb25maWd1cmF0aW9uIGZvciBwcm92aWRlciAke3Byb3ZpZGVySWR9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBSZW1vdmUgYSBwcm92aWRlcidzIHRpbWVvdXQgY29uZmlndXJhdGlvblxyXG4gICAqL1xyXG4gIHVucmVnaXN0ZXJQcm92aWRlcihwcm92aWRlcklkOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIC8vIENhbmNlbCBhbnkgYWN0aXZlIG9wZXJhdGlvbnMgZm9yIHRoaXMgcHJvdmlkZXJcclxuICAgIHRoaXMuY2FuY2VsUHJvdmlkZXJPcGVyYXRpb25zKHByb3ZpZGVySWQpO1xyXG4gICAgXHJcbiAgICB0aGlzLmNvbmZpZ3MuZGVsZXRlKHByb3ZpZGVySWQpO1xyXG4gICAgdGhpcy5tZXRyaWNzLmRlbGV0ZShwcm92aWRlcklkKTtcclxuICAgIHRoaXMubG9nZ2VyLmxvZyhgVW5yZWdpc3RlcmVkIHRpbWVvdXQgY29uZmlndXJhdGlvbiBmb3IgcHJvdmlkZXIgJHtwcm92aWRlcklkfWApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHByb3ZpZGVyIGNvbmZpZ3VyYXRpb25cclxuICAgKi9cclxuICBnZXRQcm92aWRlckNvbmZpZyhwcm92aWRlcklkOiBzdHJpbmcpOiBUaW1lb3V0Q29uZmlnIHwgbnVsbCB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWdzLmdldChwcm92aWRlcklkKSB8fCBudWxsO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGFjdGl2ZSBvcGVyYXRpb24gY291bnQgZm9yIGEgcHJvdmlkZXJcclxuICAgKi9cclxuICBnZXRBY3RpdmVPcGVyYXRpb25Db3VudChwcm92aWRlcklkOiBzdHJpbmcpOiBudW1iZXIge1xyXG4gICAgbGV0IGNvdW50ID0gMDtcclxuICAgIGZvciAoY29uc3Qgb3BlcmF0aW9uSWQgb2YgdGhpcy5hY3RpdmVPcGVyYXRpb25zLmtleXMoKSkge1xyXG4gICAgICBpZiAob3BlcmF0aW9uSWQuc3RhcnRzV2l0aChwcm92aWRlcklkKSkge1xyXG4gICAgICAgIGNvdW50Kys7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBjb3VudDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0b3RhbCBhY3RpdmUgb3BlcmF0aW9uIGNvdW50XHJcbiAgICovXHJcbiAgZ2V0VG90YWxBY3RpdmVPcGVyYXRpb25Db3VudCgpOiBudW1iZXIge1xyXG4gICAgcmV0dXJuIHRoaXMuYWN0aXZlT3BlcmF0aW9ucy5zaXplO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRXhlY3V0ZSBvcGVyYXRpb24gd2l0aCB0aW1lb3V0IHByb3RlY3Rpb25cclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIGV4ZWN1dGVXaXRoVGltZW91dEludGVybmFsPFQ+KFxyXG4gICAgb3BlcmF0aW9uOiAoc2lnbmFsPzogQWJvcnRTaWduYWwpID0+IFByb21pc2U8VD4sXHJcbiAgICB0aW1lb3V0TXM6IG51bWJlcixcclxuICAgIHNpZ25hbDogQWJvcnRTaWduYWwsXHJcbiAgICBvcGVyYXRpb25JZDogc3RyaW5nXHJcbiAgKTogUHJvbWlzZTxUPiB7XHJcbiAgICByZXR1cm4gbmV3IFByb21pc2U8VD4oKHJlc29sdmUsIHJlamVjdCkgPT4ge1xyXG4gICAgICBjb25zdCB0aW1lb3V0SGFuZGxlID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXJyb3IgPSBuZXcgRXJyb3IoYE9wZXJhdGlvbiB0aW1lZCBvdXQgYWZ0ZXIgJHt0aW1lb3V0TXN9bXNgKTtcclxuICAgICAgICBlcnJvci5uYW1lID0gJ1RpbWVvdXRFcnJvcic7XHJcbiAgICAgICAgcmVqZWN0KGVycm9yKTtcclxuICAgICAgfSwgdGltZW91dE1zKTtcclxuXHJcbiAgICAgIC8vIEhhbmRsZSBhYm9ydCBzaWduYWxcclxuICAgICAgY29uc3QgYWJvcnRIYW5kbGVyID0gKCkgPT4ge1xyXG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SGFuZGxlKTtcclxuICAgICAgICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcignT3BlcmF0aW9uIHdhcyBjYW5jZWxsZWQnKTtcclxuICAgICAgICBlcnJvci5uYW1lID0gJ0Fib3J0RXJyb3InO1xyXG4gICAgICAgIHJlamVjdChlcnJvcik7XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBpZiAoc2lnbmFsLmFib3J0ZWQpIHtcclxuICAgICAgICBhYm9ydEhhbmRsZXIoKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNpZ25hbC5hZGRFdmVudExpc3RlbmVyKCdhYm9ydCcsIGFib3J0SGFuZGxlcik7XHJcblxyXG4gICAgICBvcGVyYXRpb24oc2lnbmFsKVxyXG4gICAgICAgIC50aGVuKChyZXN1bHQpID0+IHtcclxuICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SGFuZGxlKTtcclxuICAgICAgICAgIHNpZ25hbC5yZW1vdmVFdmVudExpc3RlbmVyKCdhYm9ydCcsIGFib3J0SGFuZGxlcik7XHJcbiAgICAgICAgICByZXNvbHZlKHJlc3VsdCk7XHJcbiAgICAgICAgfSlcclxuICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dEhhbmRsZSk7XHJcbiAgICAgICAgICBzaWduYWwucmVtb3ZlRXZlbnRMaXN0ZW5lcignYWJvcnQnLCBhYm9ydEhhbmRsZXIpO1xyXG4gICAgICAgICAgcmVqZWN0KGVycm9yKTtcclxuICAgICAgICB9KTtcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRpbWVvdXQgdmFsdWUgYmFzZWQgb24gY29uZmlndXJhdGlvblxyXG4gICAqL1xyXG4gIHByaXZhdGUgZ2V0VGltZW91dChcclxuICAgIGNvbmZpZzogVGltZW91dENvbmZpZyxcclxuICAgIG9wZXJhdGlvblR5cGU/OiBzdHJpbmcsXHJcbiAgICBjdXN0b21UaW1lb3V0PzogbnVtYmVyXHJcbiAgKTogbnVtYmVyIHtcclxuICAgIGlmIChjdXN0b21UaW1lb3V0ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgcmV0dXJuIGN1c3RvbVRpbWVvdXQ7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKG9wZXJhdGlvblR5cGUgJiYgY29uZmlnLm9wZXJhdGlvblRpbWVvdXRzW29wZXJhdGlvblR5cGVdKSB7XHJcbiAgICAgIHJldHVybiBjb25maWcub3BlcmF0aW9uVGltZW91dHNbb3BlcmF0aW9uVHlwZV07XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGNvbmZpZy5kZWZhdWx0VGltZW91dDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdlbmVyYXRlIHVuaXF1ZSBvcGVyYXRpb24gSURcclxuICAgKi9cclxuICBwcml2YXRlIGdlbmVyYXRlT3BlcmF0aW9uSWQocHJvdmlkZXJJZDogc3RyaW5nKTogc3RyaW5nIHtcclxuICAgIHJldHVybiBgJHtwcm92aWRlcklkfS0ke0RhdGUubm93KCl9LSR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWA7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBVcGRhdGUgdGltZW91dCBtZXRyaWNzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSB1cGRhdGVNZXRyaWNzKHByb3ZpZGVySWQ6IHN0cmluZywgZXhlY3V0aW9uVGltZTogbnVtYmVyLCB0aW1lZE91dDogYm9vbGVhbik6IHZvaWQge1xyXG4gICAgY29uc3QgbWV0cmljcyA9IHRoaXMubWV0cmljcy5nZXQocHJvdmlkZXJJZCk7XHJcbiAgICBpZiAobWV0cmljcykge1xyXG4gICAgICBtZXRyaWNzLnRvdGFsT3BlcmF0aW9ucysrO1xyXG4gICAgICBcclxuICAgICAgaWYgKHRpbWVkT3V0KSB7XHJcbiAgICAgICAgbWV0cmljcy50aW1lb3V0Q291bnQrKztcclxuICAgICAgICBtZXRyaWNzLmxhc3RUaW1lb3V0VGltZSA9IG5ldyBEYXRlKCk7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIFVwZGF0ZSBleGVjdXRpb24gdGltZSBzdGF0aXN0aWNzXHJcbiAgICAgIG1ldHJpY3MuYXZlcmFnZUV4ZWN1dGlvblRpbWUgPSBcclxuICAgICAgICAobWV0cmljcy5hdmVyYWdlRXhlY3V0aW9uVGltZSAqIChtZXRyaWNzLnRvdGFsT3BlcmF0aW9ucyAtIDEpICsgZXhlY3V0aW9uVGltZSkgLyBcclxuICAgICAgICBtZXRyaWNzLnRvdGFsT3BlcmF0aW9ucztcclxuICAgICAgXHJcbiAgICAgIG1ldHJpY3MubWF4RXhlY3V0aW9uVGltZSA9IE1hdGgubWF4KG1ldHJpY3MubWF4RXhlY3V0aW9uVGltZSwgZXhlY3V0aW9uVGltZSk7XHJcbiAgICAgIG1ldHJpY3MubWluRXhlY3V0aW9uVGltZSA9IE1hdGgubWluKG1ldHJpY3MubWluRXhlY3V0aW9uVGltZSwgZXhlY3V0aW9uVGltZSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBDYWxjdWxhdGUgdGltZW91dCByYXRlXHJcbiAgICAgIG1ldHJpY3MudGltZW91dFJhdGUgPSBtZXRyaWNzLnRpbWVvdXRDb3VudCAvIG1ldHJpY3MudG90YWxPcGVyYXRpb25zO1xyXG4gICAgICBcclxuICAgICAgdGhpcy5tZXRyaWNzLnNldChwcm92aWRlcklkLCBtZXRyaWNzKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBwcmVkZWZpbmVkIHRpbWVvdXQgY29uZmlndXJhdGlvbnNcclxuICAgKi9cclxuICBzdGF0aWMgY3JlYXRlRGVmYXVsdENvbmZpZyhkZWZhdWx0VGltZW91dDogbnVtYmVyID0gMzAwMDApOiBUaW1lb3V0Q29uZmlnIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGRlZmF1bHRUaW1lb3V0LFxyXG4gICAgICBvcGVyYXRpb25UaW1lb3V0czoge30sXHJcbiAgICAgIGVzY2FsYXRpb25UaW1lb3V0czogW2RlZmF1bHRUaW1lb3V0LCBkZWZhdWx0VGltZW91dCAqIDEuNSwgZGVmYXVsdFRpbWVvdXQgKiAyXSxcclxuICAgICAgZW5hYmxlRXNjYWxhdGlvbjogdHJ1ZSxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICBzdGF0aWMgY3JlYXRlUXVpY2tDb25maWcoZGVmYXVsdFRpbWVvdXQ6IG51bWJlciA9IDUwMDApOiBUaW1lb3V0Q29uZmlnIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGRlZmF1bHRUaW1lb3V0LFxyXG4gICAgICBvcGVyYXRpb25UaW1lb3V0czoge1xyXG4gICAgICAgICdxdWljay1hbmFseXNpcyc6IDIwMDAsXHJcbiAgICAgICAgJ3NpbXBsZS1xdWVyeSc6IDMwMDAsXHJcbiAgICAgIH0sXHJcbiAgICAgIGVzY2FsYXRpb25UaW1lb3V0czogW2RlZmF1bHRUaW1lb3V0LCBkZWZhdWx0VGltZW91dCAqIDEuMiwgZGVmYXVsdFRpbWVvdXQgKiAxLjVdLFxyXG4gICAgICBlbmFibGVFc2NhbGF0aW9uOiB0cnVlLFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIHN0YXRpYyBjcmVhdGVMb25nUnVubmluZ0NvbmZpZyhkZWZhdWx0VGltZW91dDogbnVtYmVyID0gMTIwMDAwKTogVGltZW91dENvbmZpZyB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBkZWZhdWx0VGltZW91dCxcclxuICAgICAgb3BlcmF0aW9uVGltZW91dHM6IHtcclxuICAgICAgICAnbW9kZWwtdHJhaW5pbmcnOiAzMDAwMDAsIC8vIDUgbWludXRlc1xyXG4gICAgICAgICdsYXJnZS1hbmFseXNpcyc6IDE4MDAwMCwgLy8gMyBtaW51dGVzXHJcbiAgICAgIH0sXHJcbiAgICAgIGVzY2FsYXRpb25UaW1lb3V0czogW2RlZmF1bHRUaW1lb3V0LCBkZWZhdWx0VGltZW91dCAqIDEuNSwgZGVmYXVsdFRpbWVvdXQgKiAyLCBkZWZhdWx0VGltZW91dCAqIDNdLFxyXG4gICAgICBlbmFibGVFc2NhbGF0aW9uOiB0cnVlLFxyXG4gICAgfTtcclxuICB9XHJcbn0iXSwidmVyc2lvbiI6M30=