{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\retry.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,oDAAiE;AACjE,4GAAuG;AACvG,0FAAqF;AAErF,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,IAAI,OAAuB,CAAC;IAE5B,MAAM,aAAa,GAAkB;QACnC,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,GAAG,EAAE,sCAAsC;QACtD,QAAQ,EAAE,IAAI;QACd,iBAAiB,EAAE,CAAC;QACpB,MAAM,EAAE,KAAK,EAAE,uCAAuC;QACtD,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,CAAC;KACrE,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE,CAAC,8BAAc,CAAC;SAC5B,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAiB,8BAAc,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,oCAAoC;QACpC,OAAO,CAAC,eAAe,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,UAAU,GAAG,eAAe,CAAC;YACnC,MAAM,YAAY,GAAG,QAAQ,CAAC;YAE9B,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,UAAU,GAAG,eAAe,CAAC;YACnC,MAAM,YAAY,GAAG,QAAQ,CAAC;YAE9B,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,OAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE1E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;iBACnD,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE1E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,2DAA2B,CAAC,qBAAqB,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;iBACzG,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE1E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,oBAAoB;YACpE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,yCAAkB,CAAC,qBAAqB,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;iBACxH,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE1E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE1E,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CACrD,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAEnC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAE5E,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CACrD,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAErC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc;YAE1D,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,SAAS,CAAC,CACxD,CAAC,OAAO,CAAC,OAAO,CAAC,8DAA8D,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAClD,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,CAClE,CAAC;YAEF,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,EAAE,GAAG,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;iBACnD,qBAAqB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;iBACnD,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEjF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;iBACnD,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,OAAO,CAAC,uBAAuB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5D,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAEnE,wBAAwB;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACzD,MAAM,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,uBAAuB;YACvB,IAAI,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE3C,gBAAgB;YAChB,OAAO,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAE9C,2BAA2B;YAC3B,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YACtD,MAAM,CAAC,OAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,wBAAwB;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACzD,MAAM,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACvD,MAAM,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,uBAAuB;YACvB,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAE,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAE,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE3E,oBAAoB;YACpB,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,+BAA+B;YAC/B,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAE,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAE,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;YAErD,OAAO,CAAC,oBAAoB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,MAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,CAAC,MAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAElE,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,MAAM,GAAG,8BAAc,CAAC,8BAA8B,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAErE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,8BAAc,CAAC,yBAAyB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,MAAM,GAAG,8BAAc,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAE9D,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAClD,GAAG,aAAa;gBAChB,eAAe,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC;aACjD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;iBACzD,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE1E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAE7E,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,CACrD,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAEtC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\retry.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { AIRetryService, AIRetryConfig } from '../retry.service';\r\nimport { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';\r\nimport { RateLimitException } from '@/shared-kernel/exceptions/rate-limit.exception';\r\n\r\ndescribe('AIRetryService', () => {\r\n  let service: AIRetryService;\r\n\r\n  const defaultConfig: AIRetryConfig = {\r\n    maxAttempts: 3,\r\n    baseDelay: 100, // Use smaller delays for faster tests\r\n    maxDelay: 1000,\r\n    backoffMultiplier: 2,\r\n    jitter: false, // Disable jitter for predictable tests\r\n    retryableErrors: ['timeout', 'network', 'connection', 'unavailable'],\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [AIRetryService],\r\n    }).compile();\r\n\r\n    service = module.get<AIRetryService>(AIRetryService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    // Clean up all registered providers\r\n    service.resetAllMetrics();\r\n  });\r\n\r\n  describe('registerProvider', () => {\r\n    it('should register a new provider with retry strategy', () => {\r\n      const providerId = 'test-provider';\r\n      const providerType = 'openai';\r\n\r\n      service.registerProvider(providerId, providerType, defaultConfig);\r\n\r\n      const config = service.getProviderConfig(providerId);\r\n      expect(config).toEqual(defaultConfig);\r\n    });\r\n\r\n    it('should initialize metrics for registered provider', () => {\r\n      const providerId = 'test-provider';\r\n      const providerType = 'openai';\r\n\r\n      service.registerProvider(providerId, providerType, defaultConfig);\r\n\r\n      const metrics = service.getProviderMetrics(providerId);\r\n      expect(metrics).toBeDefined();\r\n      expect(metrics!.providerId).toBe(providerId);\r\n      expect(metrics!.totalAttempts).toBe(0);\r\n      expect(metrics!.successfulRetries).toBe(0);\r\n      expect(metrics!.failedRetries).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('executeWithRetry', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should execute successful operation without retry', async () => {\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      const result = await service.executeWithRetry('test-provider', operation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n      \r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.successfulRetries).toBe(1);\r\n      expect(metrics!.totalAttempts).toBe(1);\r\n    });\r\n\r\n    it('should retry on retryable errors', async () => {\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new Error('network timeout'))\r\n        .mockResolvedValue('success');\r\n\r\n      const result = await service.executeWithRetry('test-provider', operation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should retry on ServiceUnavailableException', async () => {\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new ServiceUnavailableException('Service unavailable', 'test-service', 'overload'))\r\n        .mockResolvedValue('success');\r\n\r\n      const result = await service.executeWithRetry('test-provider', operation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should retry on RateLimitException', async () => {\r\n      const resetTime = new Date(Date.now() + 60000); // 1 minute from now\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new RateLimitException('Rate limit exceeded', 'requests', 'test-service', 100, 90, resetTime, 60))\r\n        .mockResolvedValue('success');\r\n\r\n      const result = await service.executeWithRetry('test-provider', operation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should not retry on non-retryable errors', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('invalid input'));\r\n\r\n      await expect(\r\n        service.executeWithRetry('test-provider', operation)\r\n      ).rejects.toThrow('invalid input');\r\n\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should fail after max attempts', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('network timeout'));\r\n\r\n      await expect(\r\n        service.executeWithRetry('test-provider', operation)\r\n      ).rejects.toThrow('network timeout');\r\n\r\n      expect(operation).toHaveBeenCalledTimes(3); // maxAttempts\r\n      \r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.failedRetries).toBe(1);\r\n      expect(metrics!.totalAttempts).toBe(3);\r\n    });\r\n\r\n    it('should throw error for unregistered provider', async () => {\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      await expect(\r\n        service.executeWithRetry('unknown-provider', operation)\r\n      ).rejects.toThrow('Retry strategy not registered for provider: unknown-provider');\r\n    });\r\n\r\n    it('should apply timeout when specified', async () => {\r\n      const operation = jest.fn().mockImplementation(() => \r\n        new Promise(resolve => setTimeout(() => resolve('success'), 200))\r\n      );\r\n\r\n      await expect(\r\n        service.executeWithRetry('test-provider', operation, 100)\r\n      ).rejects.toThrow('Operation timed out after 100ms');\r\n    }, 10000);\r\n  });\r\n\r\n  describe('executeWithRetryDetails', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return detailed retry information', async () => {\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new Error('network timeout'))\r\n        .mockRejectedValueOnce(new Error('network timeout'))\r\n        .mockResolvedValue('success');\r\n\r\n      const result = await service.executeWithRetryDetails('test-provider', operation);\r\n\r\n      expect(result.result).toBe('success');\r\n      expect(result.attempts).toBe(3);\r\n      expect(result.errors).toHaveLength(2);\r\n      expect(result.totalDelay).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should update metrics with detailed information', async () => {\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new Error('network timeout'))\r\n        .mockResolvedValue('success');\r\n\r\n      await service.executeWithRetryDetails('test-provider', operation);\r\n\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.successfulRetries).toBe(1);\r\n      expect(metrics!.totalAttempts).toBe(2);\r\n      expect(metrics!.totalDelay).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('getProviderMetrics', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return metrics for registered provider', () => {\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n\r\n      expect(metrics).toBeDefined();\r\n      expect(metrics!.providerId).toBe('test-provider');\r\n    });\r\n\r\n    it('should return null for unregistered provider', () => {\r\n      const metrics = service.getProviderMetrics('unknown-provider');\r\n      expect(metrics).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('getAllProviderMetrics', () => {\r\n    it('should return empty array when no providers registered', () => {\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toEqual([]);\r\n    });\r\n\r\n    it('should return metrics for all registered providers', () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toHaveLength(2);\r\n      expect(metrics.map(m => m.providerId)).toContain('provider1');\r\n      expect(metrics.map(m => m.providerId)).toContain('provider2');\r\n    });\r\n  });\r\n\r\n  describe('resetProviderMetrics', () => {\r\n    beforeEach(async () => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n      \r\n      // Generate some metrics\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n      await service.executeWithRetry('test-provider', operation);\r\n    });\r\n\r\n    it('should reset metrics for specific provider', () => {\r\n      // Verify metrics exist\r\n      let metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.successfulRetries).toBe(1);\r\n\r\n      // Reset metrics\r\n      service.resetProviderMetrics('test-provider');\r\n\r\n      // Verify metrics are reset\r\n      metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.successfulRetries).toBe(0);\r\n      expect(metrics!.totalAttempts).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('resetAllMetrics', () => {\r\n    beforeEach(async () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n      \r\n      // Generate some metrics\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n      await service.executeWithRetry('provider1', operation);\r\n      await service.executeWithRetry('provider2', operation);\r\n    });\r\n\r\n    it('should reset metrics for all providers', () => {\r\n      // Verify metrics exist\r\n      expect(service.getProviderMetrics('provider1')!.successfulRetries).toBe(1);\r\n      expect(service.getProviderMetrics('provider2')!.successfulRetries).toBe(1);\r\n\r\n      // Reset all metrics\r\n      service.resetAllMetrics();\r\n\r\n      // Verify all metrics are reset\r\n      expect(service.getProviderMetrics('provider1')!.successfulRetries).toBe(0);\r\n      expect(service.getProviderMetrics('provider2')!.successfulRetries).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('updateProviderConfig', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should update provider configuration', () => {\r\n      const newConfig = { maxAttempts: 5, baseDelay: 200 };\r\n      \r\n      service.updateProviderConfig('test-provider', newConfig);\r\n\r\n      const config = service.getProviderConfig('test-provider');\r\n      expect(config!.maxAttempts).toBe(5);\r\n      expect(config!.baseDelay).toBe(200);\r\n      expect(config!.backoffMultiplier).toBe(2); // Should keep existing values\r\n    });\r\n  });\r\n\r\n  describe('unregisterProvider', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should remove provider configuration and metrics', () => {\r\n      expect(service.getProviderConfig('test-provider')).toBeDefined();\r\n      expect(service.getProviderMetrics('test-provider')).toBeDefined();\r\n\r\n      service.unregisterProvider('test-provider');\r\n\r\n      expect(service.getProviderConfig('test-provider')).toBeNull();\r\n      expect(service.getProviderMetrics('test-provider')).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    it('should create exponential backoff config', () => {\r\n      const config = AIRetryService.createExponentialBackoffConfig(5, 500);\r\n\r\n      expect(config.maxAttempts).toBe(5);\r\n      expect(config.baseDelay).toBe(500);\r\n      expect(config.backoffMultiplier).toBe(2);\r\n      expect(config.jitter).toBe(true);\r\n      expect(config.retryableErrors).toContain('timeout');\r\n    });\r\n\r\n    it('should create linear backoff config', () => {\r\n      const config = AIRetryService.createLinearBackoffConfig(4, 300);\r\n\r\n      expect(config.maxAttempts).toBe(4);\r\n      expect(config.baseDelay).toBe(300);\r\n      expect(config.maxDelay).toBe(300);\r\n      expect(config.backoffMultiplier).toBe(1);\r\n      expect(config.jitter).toBe(false);\r\n    });\r\n\r\n    it('should create fixed delay config', () => {\r\n      const config = AIRetryService.createFixedDelayConfig(2, 1000);\r\n\r\n      expect(config.maxAttempts).toBe(2);\r\n      expect(config.baseDelay).toBe(1000);\r\n      expect(config.maxDelay).toBe(1000);\r\n      expect(config.backoffMultiplier).toBe(1);\r\n      expect(config.jitter).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('error classification', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', {\r\n        ...defaultConfig,\r\n        retryableErrors: ['custom-error', 'api-timeout'],\r\n      });\r\n    });\r\n\r\n    it('should retry on custom retryable errors', async () => {\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new Error('custom-error occurred'))\r\n        .mockResolvedValue('success');\r\n\r\n      const result = await service.executeWithRetry('test-provider', operation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should not retry on non-configured errors', async () => {\r\n      const operation = jest.fn().mockRejectedValue(new Error('validation error'));\r\n\r\n      await expect(\r\n        service.executeWithRetry('test-provider', operation)\r\n      ).rejects.toThrow('validation error');\r\n\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n    });\r\n  });\r\n});"], "version": 3}