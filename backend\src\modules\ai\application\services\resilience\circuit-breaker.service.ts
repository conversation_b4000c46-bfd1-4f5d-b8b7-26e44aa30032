import { Injectable, Logger } from '@nestjs/common';
import { CircuitBreaker, CircuitBreakerOptions, CircuitBreakerState, CircuitBreakerMetrics } from '@/shared-kernel/patterns/circuit-breaker';
import { ServiceUnavailableException } from '@/shared-kernel/exceptions/service-unavailable.exception';

export interface AICircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  halfOpenMaxCalls: number;
  enableFallback: boolean;
  fallbackResponse?: any;
}

export interface AIProviderCircuitBreakerMetrics extends CircuitBreakerMetrics {
  providerId: string;
  providerType: string;
  lastError?: string;
}

/**
 * AI-specific circuit breaker service for managing AI provider resilience
 */
@Injectable()
export class AICircuitBreakerService {
  private readonly logger = new Logger(AICircuitBreakerService.name);
  private readonly circuitBreakers = new Map<string, CircuitBreaker>();
  private readonly configs = new Map<string, AICircuitBreakerConfig>();

  /**
   * Register a circuit breaker for an AI provider
   */
  registerProvider(
    providerId: string,
    providerType: string,
    config: AICircuitBreakerConfig
  ): void {
    const options: CircuitBreakerOptions = {
      failureThreshold: config.failureThreshold,
      recoveryTimeout: config.recoveryTimeout,
      monitoringPeriod: config.monitoringPeriod,
      halfOpenMaxCalls: config.halfOpenMaxCalls,
      onStateChange: (state: CircuitBreakerState) => {
        this.logger.log(
          `Circuit breaker for ${providerType} provider ${providerId} changed to ${state}`
        );
      },
    };

    const circuitBreaker = new CircuitBreaker(options);
    this.circuitBreakers.set(providerId, circuitBreaker);
    this.configs.set(providerId, config);

    this.logger.log(
      `Registered circuit breaker for ${providerType} provider ${providerId}`
    );
  }

  /**
   * Execute an AI operation with circuit breaker protection
   */
  async executeWithProtection<T>(
    providerId: string,
    operation: () => Promise<T>,
    fallbackFn?: () => Promise<T>
  ): Promise<T> {
    const circuitBreaker = this.circuitBreakers.get(providerId);
    if (!circuitBreaker) {
      throw new Error(`Circuit breaker not registered for provider: ${providerId}`);
    }

    const config = this.configs.get(providerId);
    
    try {
      return await circuitBreaker.execute(operation);
    } catch (error) {
      this.logger.error(
        `Circuit breaker execution failed for provider ${providerId}`,
        error
      );

      // Try fallback if enabled and available
      if (config?.enableFallback && fallbackFn) {
        this.logger.warn(
          `Executing fallback for provider ${providerId} due to circuit breaker`
        );
        return await fallbackFn();
      }

      // Return cached fallback response if available
      if (config?.enableFallback && config.fallbackResponse) {
        this.logger.warn(
          `Returning cached fallback response for provider ${providerId}`
        );
        return config.fallbackResponse;
      }

      throw error;
    }
  }

  /**
   * Get circuit breaker metrics for a specific provider
   */
  getProviderMetrics(providerId: string): AIProviderCircuitBreakerMetrics | null {
    const circuitBreaker = this.circuitBreakers.get(providerId);
    if (!circuitBreaker) {
      return null;
    }

    const metrics = circuitBreaker.getMetrics();
    return {
      ...metrics,
      providerId,
      providerType: this.getProviderType(providerId),
    };
  }

  /**
   * Get metrics for all registered providers
   */
  getAllProviderMetrics(): AIProviderCircuitBreakerMetrics[] {
    const metrics: AIProviderCircuitBreakerMetrics[] = [];
    
    for (const [providerId, circuitBreaker] of this.circuitBreakers) {
      const providerMetrics = circuitBreaker.getMetrics();
      metrics.push({
        ...providerMetrics,
        providerId,
        providerType: this.getProviderType(providerId),
      });
    }

    return metrics;
  }

  /**
   * Check if a provider is available (circuit breaker is closed or half-open)
   */
  isProviderAvailable(providerId: string): boolean {
    const circuitBreaker = this.circuitBreakers.get(providerId);
    if (!circuitBreaker) {
      return false;
    }

    const metrics = circuitBreaker.getMetrics();
    return metrics.state !== CircuitBreakerState.OPEN;
  }

  /**
   * Get all available providers
   */
  getAvailableProviders(): string[] {
    const availableProviders: string[] = [];
    
    for (const [providerId] of this.circuitBreakers) {
      if (this.isProviderAvailable(providerId)) {
        availableProviders.push(providerId);
      }
    }

    return availableProviders;
  }

  /**
   * Reset circuit breaker for a specific provider
   */
  resetProvider(providerId: string): void {
    const circuitBreaker = this.circuitBreakers.get(providerId);
    if (circuitBreaker) {
      circuitBreaker.reset();
      this.logger.log(`Reset circuit breaker for provider ${providerId}`);
    }
  }

  /**
   * Force open circuit breaker for a specific provider
   */
  forceOpenProvider(providerId: string): void {
    const circuitBreaker = this.circuitBreakers.get(providerId);
    if (circuitBreaker) {
      circuitBreaker.forceOpen();
      this.logger.warn(`Forced open circuit breaker for provider ${providerId}`);
    }
  }

  /**
   * Reset all circuit breakers
   */
  resetAllProviders(): void {
    for (const [providerId, circuitBreaker] of this.circuitBreakers) {
      circuitBreaker.reset();
      this.logger.log(`Reset circuit breaker for provider ${providerId}`);
    }
  }

  /**
   * Update configuration for a provider
   */
  updateProviderConfig(providerId: string, config: Partial<AICircuitBreakerConfig>): void {
    const existingConfig = this.configs.get(providerId);
    if (existingConfig) {
      const updatedConfig = { ...existingConfig, ...config };
      this.configs.set(providerId, updatedConfig);
      
      // Re-register with new config
      const providerType = this.getProviderType(providerId);
      this.registerProvider(providerId, providerType, updatedConfig);
      
      this.logger.log(`Updated configuration for provider ${providerId}`);
    }
  }

  /**
   * Remove a provider's circuit breaker
   */
  unregisterProvider(providerId: string): void {
    this.circuitBreakers.delete(providerId);
    this.configs.delete(providerId);
    this.logger.log(`Unregistered circuit breaker for provider ${providerId}`);
  }

  /**
   * Get health status of all providers
   */
  getHealthStatus(): Record<string, { available: boolean; metrics: AIProviderCircuitBreakerMetrics }> {
    const status: Record<string, { available: boolean; metrics: AIProviderCircuitBreakerMetrics }> = {};
    
    for (const [providerId] of this.circuitBreakers) {
      const available = this.isProviderAvailable(providerId);
      const metrics = this.getProviderMetrics(providerId)!;
      
      status[providerId] = {
        available,
        metrics,
      };
    }

    return status;
  }

  private getProviderType(providerId: string): string {
    // Extract provider type from provider ID (e.g., "openai-gpt4" -> "openai")
    return providerId.split('-')[0] || 'unknown';
  }
}