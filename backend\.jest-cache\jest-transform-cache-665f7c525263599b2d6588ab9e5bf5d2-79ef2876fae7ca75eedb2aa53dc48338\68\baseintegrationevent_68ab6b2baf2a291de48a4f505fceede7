efe2bb144d738d891b8af6753950de91
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseIntegrationEvent = void 0;
const uuid_1 = require("uuid");
/**
 * Base Integration Event
 *
 * Abstract base class for all integration events in the system
 */
class BaseIntegrationEvent {
    constructor(eventType, eventData, options) {
        this.id = (0, uuid_1.v4)();
        this.timestamp = new Date();
        this.eventType = eventType;
        this.eventData = eventData;
        this.source = options?.source || 'sentinel-backend';
        this.version = options?.version || '1.0';
        this._correlationId = options?.correlationId;
        this._causationId = options?.causationId;
        this._metadata = options?.metadata || {};
    }
    /**
     * Get event ID
     */
    getId() {
        return this.id;
    }
    /**
     * Get event timestamp
     */
    getTimestamp() {
        return this.timestamp;
    }
    /**
     * Get event type
     */
    getEventType() {
        return this.eventType;
    }
    /**
     * Get event source
     */
    getSource() {
        return this.source;
    }
    /**
     * Get event version
     */
    getVersion() {
        return this.version;
    }
    /**
     * Get event data
     */
    getEventData() {
        return this.eventData;
    }
    /**
     * Convert event to JSON
     */
    toJSON() {
        return {
            id: this.id,
            timestamp: this.timestamp.toISOString(),
            eventType: this.eventType,
            source: this.source,
            version: this.version,
            data: this.eventData,
        };
    }
    /**
     * Get event metadata
     */
    getMetadata() {
        return {
            id: this.id,
            timestamp: this.timestamp,
            eventType: this.eventType,
            source: this.source,
            version: this.version,
        };
    }
    /**
     * Get event correlation ID for tracking related events
     */
    getCorrelationId() {
        return this._correlationId || this.eventData?.correlationId || null;
    }
    /**
     * Get event causation ID for tracking event chains
     */
    getCausationId() {
        return this._causationId || null;
    }
    /**
     * Get event metadata
     */
    getEventMetadata() {
        return { ...this._metadata };
    }
    /**
     * Get event tags for categorization
     */
    getTags() {
        return this.eventData?.tags || [];
    }
    /**
     * Check if event is retryable in case of processing failure
     */
    isRetryable() {
        return true; // Default to retryable
    }
    /**
     * Get maximum retry attempts
     */
    getMaxRetryAttempts() {
        return 3; // Default retry attempts
    }
    /**
     * Get retry delay in milliseconds
     */
    getRetryDelay() {
        return 1000; // Default 1 second delay
    }
}
exports.BaseIntegrationEvent = BaseIntegrationEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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