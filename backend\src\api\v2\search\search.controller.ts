import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ValidationPipe,
  UsePipes,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';

import { SearchService } from './search.service';
import { JwtAuthGuard } from '../../../infrastructure/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../infrastructure/auth/guards/roles.guard';
import { Roles } from '../../../infrastructure/auth/decorators/roles.decorator';

/**
 * Search scope enum
 */
export enum SearchScope {
  ALL = 'all',
  EVENTS = 'events',
  VULNERABILITIES = 'vulnerabilities',
  ASSETS = 'assets',
  THREATS = 'threats',
  INCIDENTS = 'incidents',
  USERS = 'users',
  COMPLIANCE = 'compliance',
}

/**
 * Search operator enum
 */
export enum SearchOperator {
  AND = 'and',
  OR = 'or',
  NOT = 'not',
  CONTAINS = 'contains',
  EQUALS = 'equals',
  GREATER_THAN = 'gt',
  LESS_THAN = 'lt',
  BETWEEN = 'between',
  IN = 'in',
  REGEX = 'regex',
}

/**
 * Sort direction enum
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Advanced Search and Filtering API Controller v2
 * Provides comprehensive search capabilities across all platform modules
 */
@ApiTags('Search v2')
@Controller('api/v2/search')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  /**
   * Global search across all modules
   */
  @Get('global')
  @Roles('user', 'analyst', 'admin')
  @ApiOperation({ summary: 'Perform global search across all platform modules' })
  @ApiQuery({ name: 'q', description: 'Search query string' })
  @ApiQuery({ name: 'scope', enum: SearchScope, required: false })
  @ApiQuery({ name: 'limit', type: 'number', required: false })
  @ApiQuery({ name: 'offset', type: 'number', required: false })
  @ApiQuery({ name: 'includeHighlights', type: 'boolean', required: false })
  @ApiQuery({ name: 'includeFacets', type: 'boolean', required: false })
  @ApiResponse({
    status: 200,
    description: 'Global search results retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            results: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  type: { type: 'string' },
                  module: { type: 'string' },
                  title: { type: 'string' },
                  description: { type: 'string' },
                  score: { type: 'number' },
                  highlights: { type: 'object' },
                  metadata: { type: 'object' },
                },
              },
            },
            facets: {
              type: 'object',
              properties: {
                modules: { type: 'object' },
                types: { type: 'object' },
                severity: { type: 'object' },
                status: { type: 'object' },
                dateRanges: { type: 'object' },
              },
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                limit: { type: 'number' },
                offset: { type: 'number' },
                hasMore: { type: 'boolean' },
              },
            },
            suggestions: {
              type: 'array',
              items: { type: 'string' },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            queryTime: { type: 'number' },
            searchId: { type: 'string' },
          },
        },
      },
    },
  })
  async globalSearch(
    @Query('q') query: string,
    @Request() req: any,
    @Query('scope') scope: SearchScope = SearchScope.ALL,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
    @Query('offset', new ParseIntPipe({ optional: true })) offset: number = 0,
    @Query('includeHighlights') includeHighlights: boolean = true,
    @Query('includeFacets') includeFacets: boolean = true,
  ): Promise<any> {
    const startTime = Date.now();
    
    const results = await this.searchService.globalSearch({
      query,
      scope,
      limit,
      offset,
      includeHighlights,
      includeFacets,
      userId: req.user.id,
      userRoles: req.user.roles,
    });

    return {
      success: true,
      data: results,
      metadata: {
        queryTime: Date.now() - startTime,
        searchId: this.searchService.generateSearchId(),
      },
    };
  }

  /**
   * Advanced search with complex filters
   */
  @Post('advanced')
  @Roles('analyst', 'admin')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Perform advanced search with complex filtering and aggregations' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        query: {
          type: 'object',
          properties: {
            text: { type: 'string' },
            fields: { type: 'array', items: { type: 'string' } },
            operator: { type: 'string', enum: Object.values(SearchOperator) },
          },
        },
        filters: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string' },
              operator: { type: 'string', enum: Object.values(SearchOperator) },
              value: { type: 'string' },
              values: { type: 'array', items: { type: 'string' } },
            },
          },
        },
        facets: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string' },
              size: { type: 'number' },
              sort: { type: 'string' },
            },
          },
        },
        aggregations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              type: { type: 'string', enum: ['terms', 'date_histogram', 'range', 'stats'] },
              field: { type: 'string' },
              size: { type: 'number' },
              interval: { type: 'string' },
              ranges: { type: 'array' },
            },
          },
        },
        sort: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string' },
              direction: { type: 'string', enum: Object.values(SortDirection) },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            limit: { type: 'number' },
            offset: { type: 'number' },
          },
        },
        scope: { type: 'string', enum: Object.values(SearchScope) },
        includeHighlights: { type: 'boolean' },
        includeExplanation: { type: 'boolean' },
      },
      required: ['query'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Advanced search completed successfully',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async advancedSearch(
    @Body() searchRequest: {
      query: {
        text: string;
        fields?: string[];
        operator?: SearchOperator;
      };
      filters?: Array<{
        field: string;
        operator: SearchOperator;
        value?: string;
        values?: string[];
      }>;
      facets?: Array<{
        field: string;
        size?: number;
        sort?: string;
      }>;
      aggregations?: Array<{
        name: string;
        type: 'terms' | 'date_histogram' | 'range' | 'stats';
        field: string;
        size?: number;
        interval?: string;
        ranges?: Array<{ from?: number; to?: number; key?: string }>;
      }>;
      sort?: Array<{
        field: string;
        direction: SortDirection;
      }>;
      pagination?: {
        limit?: number;
        offset?: number;
      };
      scope?: SearchScope;
      includeHighlights?: boolean;
      includeExplanation?: boolean;
    },
    @Request() req: any,
  ): Promise<any> {
    const startTime = Date.now();
    
    const results = await this.searchService.advancedSearch(searchRequest, {
      userId: req.user.id,
      userRoles: req.user.roles,
    });

    return {
      success: true,
      data: results,
      metadata: {
        queryTime: Date.now() - startTime,
        searchId: this.searchService.generateSearchId(),
        query: searchRequest.query,
      },
    };
  }

  /**
   * Full-text search with fuzzy matching
   */
  @Get('fulltext')
  @Roles('user', 'analyst', 'admin')
  @ApiOperation({ summary: 'Perform full-text search with fuzzy matching and auto-completion' })
  @ApiQuery({ name: 'q', description: 'Search query string' })
  @ApiQuery({ name: 'fuzziness', type: 'number', required: false, description: 'Fuzziness level (0-2)' })
  @ApiQuery({ name: 'boost', type: 'string', required: false, description: 'Field boost configuration' })
  @ApiQuery({ name: 'analyzer', type: 'string', required: false, description: 'Text analyzer to use' })
  @ApiQuery({ name: 'scope', enum: SearchScope, required: false })
  @ApiResponse({
    status: 200,
    description: 'Full-text search results retrieved successfully',
  })
  async fullTextSearch(
    @Query('q') query: string,
    @Request() req: any,
    @Query('fuzziness', new ParseIntPipe({ optional: true })) fuzziness: number = 1,
    @Query('boost') boost?: string,
    @Query('analyzer') analyzer?: string,
    @Query('scope') scope: SearchScope = SearchScope.ALL,
  ): Promise<any> {
    const results = await this.searchService.fullTextSearch({
      query,
      fuzziness,
      boost,
      analyzer,
      scope,
      userId: req.user.id,
    });

    return {
      success: true,
      data: results,
    };
  }

  /**
   * Faceted search with drill-down capabilities
   */
  @Get('faceted')
  @Roles('analyst', 'admin')
  @ApiOperation({ summary: 'Perform faceted search with drill-down navigation' })
  @ApiQuery({ name: 'q', required: false, description: 'Optional search query' })
  @ApiQuery({ name: 'facets', type: 'string', isArray: true, required: false })
  @ApiQuery({ name: 'filters', type: 'string', required: false, description: 'JSON-encoded filters' })
  @ApiQuery({ name: 'scope', enum: SearchScope, required: false })
  @ApiResponse({
    status: 200,
    description: 'Faceted search results retrieved successfully',
  })
  async facetedSearch(
    @Request() req: any,
    @Query('q') query?: string,
    @Query('facets') facets?: string[],
    @Query('filters') filters?: string,
    @Query('scope') scope: SearchScope = SearchScope.ALL,
  ): Promise<any> {
    const parsedFilters = filters ? JSON.parse(filters) : {};
    
    const results = await this.searchService.facetedSearch({
      query,
      facets: facets || ['module', 'type', 'severity', 'status'],
      filters: parsedFilters,
      scope,
      userId: req.user.id,
    });

    return {
      success: true,
      data: results,
    };
  }

  /**
   * Search suggestions and auto-completion
   */
  @Get('suggest')
  @Roles('user', 'analyst', 'admin')
  @ApiOperation({ summary: 'Get search suggestions and auto-completion' })
  @ApiQuery({ name: 'q', description: 'Partial search query' })
  @ApiQuery({ name: 'size', type: 'number', required: false })
  @ApiQuery({ name: 'scope', enum: SearchScope, required: false })
  @ApiResponse({
    status: 200,
    description: 'Search suggestions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            suggestions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  text: { type: 'string' },
                  score: { type: 'number' },
                  type: { type: 'string' },
                  context: { type: 'object' },
                },
              },
            },
            completions: {
              type: 'array',
              items: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async searchSuggestions(
    @Query('q') query: string,
    @Request() req: any,
    @Query('size', new ParseIntPipe({ optional: true })) size: number = 10,
    @Query('scope') scope: SearchScope = SearchScope.ALL,
  ): Promise<any> {
    const suggestions = await this.searchService.getSearchSuggestions({
      query,
      size,
      scope,
      userId: req.user.id,
    });

    return {
      success: true,
      data: suggestions,
    };
  }

  /**
   * Saved searches management
   */
  @Post('saved')
  @Roles('user', 'analyst', 'admin')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Save search query for future use' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string' },
        query: { type: 'object' },
        filters: { type: 'object' },
        scope: { type: 'string', enum: Object.values(SearchScope) },
        isPublic: { type: 'boolean' },
        tags: { type: 'array', items: { type: 'string' } },
      },
      required: ['name', 'query'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Search saved successfully',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async saveSearch(
    @Body() saveRequest: {
      name: string;
      description?: string;
      query: any;
      filters?: any;
      scope?: SearchScope;
      isPublic?: boolean;
      tags?: string[];
    },
    @Request() req: any,
  ): Promise<any> {
    const savedSearch = await this.searchService.saveSearch(saveRequest, req.user.id);

    return {
      success: true,
      data: savedSearch,
      message: 'Search saved successfully',
    };
  }

  /**
   * Get saved searches
   */
  @Get('saved')
  @Roles('user', 'analyst', 'admin')
  @ApiOperation({ summary: 'Get user saved searches' })
  @ApiQuery({ name: 'includePublic', type: 'boolean', required: false })
  @ApiResponse({
    status: 200,
    description: 'Saved searches retrieved successfully',
  })
  async getSavedSearches(
    @Request() req: any,
    @Query('includePublic') includePublic: boolean = false,
  ): Promise<any> {
    const savedSearches = await this.searchService.getSavedSearches(req.user.id, includePublic);

    return {
      success: true,
      data: savedSearches,
    };
  }

  /**
   * Search analytics and insights
   */
  @Get('analytics')
  @Roles('analyst', 'admin')
  @ApiOperation({ summary: 'Get search analytics and usage insights' })
  @ApiQuery({ name: 'timeRange', type: 'string', required: false })
  @ApiQuery({ name: 'includePopularQueries', type: 'boolean', required: false })
  @ApiResponse({
    status: 200,
    description: 'Search analytics retrieved successfully',
  })
  async getSearchAnalytics(
    @Query('timeRange') timeRange: string = '7d',
    @Query('includePopularQueries') includePopularQueries: boolean = true,
    @Request() req: any,
  ): Promise<any> {
    const analytics = await this.searchService.getSearchAnalytics({
      timeRange,
      includePopularQueries,
      userId: req.user.id,
    });

    return {
      success: true,
      data: analytics,
    };
  }

  /**
   * Export search results
   */
  @Post('export')
  @Roles('analyst', 'admin')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Export search results in various formats' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        searchQuery: { type: 'object' },
        format: { type: 'string', enum: ['csv', 'json', 'excel', 'pdf'] },
        includeMetadata: { type: 'boolean' },
        maxResults: { type: 'number' },
      },
      required: ['searchQuery', 'format'],
    },
  })
  @ApiResponse({
    status: 202,
    description: 'Search export initiated successfully',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async exportSearchResults(
    @Body() exportRequest: {
      searchQuery: any;
      format: 'csv' | 'json' | 'excel' | 'pdf';
      includeMetadata?: boolean;
      maxResults?: number;
    },
    @Request() req: any,
  ): Promise<any> {
    const jobId = await this.searchService.scheduleSearchExport(exportRequest, req.user.id);

    return {
      success: true,
      data: {
        jobId,
        status: 'initiated',
        estimatedCompletion: new Date(Date.now() + 3 * 60 * 1000), // 3 minutes
      },
      message: 'Search export initiated successfully',
    };
  }
}
