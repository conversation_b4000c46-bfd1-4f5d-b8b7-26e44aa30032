95eebd8babcd56539834832f6069b0de
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDomainEvent = void 0;
const unique_entity_id_value_object_1 = require("../value-objects/unique-entity-id.value-object");
/**
 * Base Domain Event
 *
 * Abstract base class for all domain events in the system.
 * Domain events represent something important that happened in the domain.
 *
 * Key characteristics:
 * - Immutable once created
 * - Contains all necessary information about what happened
 * - Can be used for event sourcing, integration events, and notifications
 * - Includes metadata for tracking and debugging
 *
 * @template T The event data type
 */
class BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        this._isDispatched = false;
        this._eventId = options?.eventId || unique_entity_id_value_object_1.UniqueEntityId.generate();
        this._occurredOn = options?.occurredOn || new Date();
        this._aggregateId = aggregateId;
        this._eventVersion = options?.eventVersion || 1;
        this._eventData = eventData;
        this._correlationId = options?.correlationId;
        this._causationId = options?.causationId;
        this._metadata = options?.metadata || {};
        // Freeze the event data and metadata to make them immutable
        Object.freeze(this._eventData);
        Object.freeze(this._metadata);
        // Note: We don't freeze the entire object because we need to modify dispatch status
    }
    /**
     * Unique identifier for this event
     */
    get eventId() {
        return this._eventId;
    }
    /**
     * When the event occurred
     */
    get occurredOn() {
        return this._occurredOn;
    }
    /**
     * The aggregate that generated this event
     */
    get aggregateId() {
        return this._aggregateId;
    }
    /**
     * Version of the event schema
     */
    get eventVersion() {
        return this._eventVersion;
    }
    /**
     * The event data payload
     */
    get eventData() {
        return this._eventData;
    }
    /**
     * Whether this event has been dispatched
     */
    get isDispatched() {
        return this._isDispatched;
    }
    /**
     * When the event was dispatched
     */
    get dispatchedAt() {
        return this._dispatchedAt;
    }
    /**
     * Correlation ID for tracking related events
     */
    get correlationId() {
        return this._correlationId;
    }
    /**
     * Causation ID for tracking event chains
     */
    get causationId() {
        return this._causationId;
    }
    /**
     * Additional metadata for the event
     */
    get metadata() {
        return { ...this._metadata };
    }
    /**
     * Get the event type name
     */
    get eventType() {
        return this.constructor.name;
    }
    /**
     * Get the event name for event emitter
     */
    get eventName() {
        return this.eventType;
    }
    /**
     * Mark the event as dispatched
     * This should only be called by the event dispatcher
     */
    markAsDispatched() {
        if (this._isDispatched) {
            throw new Error(`Event ${this._eventId.toString()} has already been dispatched`);
        }
        this._isDispatched = true;
        this._dispatchedAt = new Date();
    }
    /**
     * Get the age of the event in milliseconds
     */
    getAge() {
        return Date.now() - this._occurredOn.getTime();
    }
    /**
     * Get the age of the event in seconds
     */
    getAgeInSeconds() {
        return Math.floor(this.getAge() / 1000);
    }
    /**
     * Get the age of the event in minutes
     */
    getAgeInMinutes() {
        return Math.floor(this.getAge() / (1000 * 60));
    }
    /**
     * Check if the event is stale (older than specified time)
     *
     * @param maxAgeMs Maximum age in milliseconds
     */
    isStale(maxAgeMs) {
        return this.getAge() > maxAgeMs;
    }
    /**
     * Create a new event with updated metadata
     *
     * @param newMetadata Additional metadata to merge
     */
    withMetadata(newMetadata) {
        const constructor = this.constructor;
        return new constructor(this._aggregateId, this._eventData, {
            eventId: this._eventId,
            occurredOn: this._occurredOn,
            eventVersion: this._eventVersion,
            correlationId: this._correlationId,
            causationId: this._causationId,
            metadata: { ...this._metadata, ...newMetadata },
        });
    }
    /**
     * Create a new event with a correlation ID
     *
     * @param correlationId The correlation ID to set
     */
    withCorrelationId(correlationId) {
        const constructor = this.constructor;
        return new constructor(this._aggregateId, this._eventData, {
            eventId: this._eventId,
            occurredOn: this._occurredOn,
            eventVersion: this._eventVersion,
            correlationId,
            causationId: this._causationId,
            metadata: this._metadata,
        });
    }
    /**
     * Create a new event with a causation ID
     *
     * @param causationId The causation ID to set
     */
    withCausationId(causationId) {
        const constructor = this.constructor;
        return new constructor(this._aggregateId, this._eventData, {
            eventId: this._eventId,
            occurredOn: this._occurredOn,
            eventVersion: this._eventVersion,
            correlationId: this._correlationId,
            causationId,
            metadata: this._metadata,
        });
    }
    /**
     * Convert event to JSON representation
     */
    toJSON() {
        return {
            eventId: this._eventId.toString(),
            eventType: this.eventType,
            occurredOn: this._occurredOn.toISOString(),
            aggregateId: this._aggregateId.toString(),
            eventVersion: this._eventVersion,
            eventData: this._eventData,
            isDispatched: this._isDispatched,
            dispatchedAt: this._dispatchedAt?.toISOString(),
            correlationId: this._correlationId,
            causationId: this._causationId,
            metadata: this._metadata,
        };
    }
    /**
     * Create event from JSON representation
     */
    static fromJSON(json, eventClass) {
        return new eventClass(unique_entity_id_value_object_1.UniqueEntityId.fromString(json.aggregateId), json.eventData, {
            eventId: unique_entity_id_value_object_1.UniqueEntityId.fromString(json.eventId),
            occurredOn: new Date(json.occurredOn),
            eventVersion: json.eventVersion,
            correlationId: json.correlationId,
            causationId: json.causationId,
            metadata: json.metadata,
        });
    }
    /**
     * Compare events for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        return this._eventId.equals(other._eventId);
    }
    /**
     * Get a string representation of the event
     */
    toString() {
        return `${this.eventType}(${this._eventId.toString()})`;
    }
}
exports.BaseDomainEvent = BaseDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFxkb21haW5cXGJhc2UtZG9tYWluLWV2ZW50LnRzIiwibWFwcGluZ3MiOiI7OztBQUFBLGtHQUFnRjtBQUVoRjs7Ozs7Ozs7Ozs7OztHQWFHO0FBQ0gsTUFBc0IsZUFBZTtJQVluQyxZQUNFLFdBQTJCLEVBQzNCLFNBQVksRUFDWixPQU9DO1FBaEJLLGtCQUFhLEdBQVksS0FBSyxDQUFDO1FBa0JyQyxJQUFJLENBQUMsUUFBUSxHQUFHLE9BQU8sRUFBRSxPQUFPLElBQUksOENBQWMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztRQUM5RCxJQUFJLENBQUMsV0FBVyxHQUFHLE9BQU8sRUFBRSxVQUFVLElBQUksSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUNyRCxJQUFJLENBQUMsWUFBWSxHQUFHLFdBQVcsQ0FBQztRQUNoQyxJQUFJLENBQUMsYUFBYSxHQUFHLE9BQU8sRUFBRSxZQUFZLElBQUksQ0FBQyxDQUFDO1FBQ2hELElBQUksQ0FBQyxVQUFVLEdBQUcsU0FBUyxDQUFDO1FBQzVCLElBQUksQ0FBQyxjQUFjLEdBQUcsT0FBTyxFQUFFLGFBQWEsQ0FBQztRQUM3QyxJQUFJLENBQUMsWUFBWSxHQUFHLE9BQU8sRUFBRSxXQUFXLENBQUM7UUFDekMsSUFBSSxDQUFDLFNBQVMsR0FBRyxPQUFPLEVBQUUsUUFBUSxJQUFJLEVBQUUsQ0FBQztRQUV6Qyw0REFBNEQ7UUFDNUQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDL0IsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDOUIsb0ZBQW9GO0lBQ3RGLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksT0FBTztRQUNULE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQztJQUN2QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFVBQVU7UUFDWixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUM7SUFDMUIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxXQUFXO1FBQ2IsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO0lBQzNCLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksWUFBWTtRQUNkLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztJQUM1QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxVQUFVLENBQUM7SUFDekIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxZQUFZO1FBQ2QsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDO0lBQzVCLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksWUFBWTtRQUNkLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztJQUM1QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLGFBQWE7UUFDZixPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7SUFDN0IsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxXQUFXO1FBQ2IsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO0lBQzNCLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksUUFBUTtRQUNWLE9BQU8sRUFBRSxHQUFHLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQztJQUMvQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDO0lBQy9CLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksU0FBUztRQUNYLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQztJQUN4QixDQUFDO0lBRUQ7OztPQUdHO0lBQ0ksZ0JBQWdCO1FBQ3JCLElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ3ZCLE1BQU0sSUFBSSxLQUFLLENBQUMsU0FBUyxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsRUFBRSw4QkFBOEIsQ0FBQyxDQUFDO1FBQ25GLENBQUM7UUFFRCxJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQztRQUMxQixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7SUFDbEMsQ0FBQztJQUVEOztPQUVHO0lBQ0ksTUFBTTtRQUNYLE9BQU8sSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsT0FBTyxFQUFFLENBQUM7SUFDakQsQ0FBQztJQUVEOztPQUVHO0lBQ0ksZUFBZTtRQUNwQixPQUFPLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLElBQUksQ0FBQyxDQUFDO0lBQzFDLENBQUM7SUFFRDs7T0FFRztJQUNJLGVBQWU7UUFDcEIsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUUsR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ2pELENBQUM7SUFFRDs7OztPQUlHO0lBQ0ksT0FBTyxDQUFDLFFBQWdCO1FBQzdCLE9BQU8sSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLFFBQVEsQ0FBQztJQUNsQyxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNJLFlBQVksQ0FBQyxXQUFnQztRQUNsRCxNQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsV0FBMkMsQ0FBQztRQUNyRSxPQUFPLElBQUksV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUN6RCxPQUFPLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdEIsVUFBVSxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzVCLFlBQVksRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNoQyxhQUFhLEVBQUUsSUFBSSxDQUFDLGNBQWM7WUFDbEMsV0FBVyxFQUFFLElBQUksQ0FBQyxZQUFZO1lBQzlCLFFBQVEsRUFBRSxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsRUFBRSxHQUFHLFdBQVcsRUFBRTtTQUNoRCxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNJLGlCQUFpQixDQUFDLGFBQXFCO1FBQzVDLE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxXQUEyQyxDQUFDO1FBQ3JFLE9BQU8sSUFBSSxXQUFXLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ3pELE9BQU8sRUFBRSxJQUFJLENBQUMsUUFBUTtZQUN0QixVQUFVLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDNUIsWUFBWSxFQUFFLElBQUksQ0FBQyxhQUFhO1lBQ2hDLGFBQWE7WUFDYixXQUFXLEVBQUUsSUFBSSxDQUFDLFlBQVk7WUFDOUIsUUFBUSxFQUFFLElBQUksQ0FBQyxTQUFTO1NBQ3pCLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRDs7OztPQUlHO0lBQ0ksZUFBZSxDQUFDLFdBQW1CO1FBQ3hDLE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxXQUEyQyxDQUFDO1FBQ3JFLE9BQU8sSUFBSSxXQUFXLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ3pELE9BQU8sRUFBRSxJQUFJLENBQUMsUUFBUTtZQUN0QixVQUFVLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDNUIsWUFBWSxFQUFFLElBQUksQ0FBQyxhQUFhO1lBQ2hDLGFBQWEsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNsQyxXQUFXO1lBQ1gsUUFBUSxFQUFFLElBQUksQ0FBQyxTQUFTO1NBQ3pCLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLE1BQU07UUFDWCxPQUFPO1lBQ0wsT0FBTyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsUUFBUSxFQUFFO1lBQ2pDLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztZQUN6QixVQUFVLEVBQUUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxXQUFXLEVBQUU7WUFDMUMsV0FBVyxFQUFFLElBQUksQ0FBQyxZQUFZLENBQUMsUUFBUSxFQUFFO1lBQ3pDLFlBQVksRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNoQyxTQUFTLEVBQUUsSUFBSSxDQUFDLFVBQVU7WUFDMUIsWUFBWSxFQUFFLElBQUksQ0FBQyxhQUFhO1lBQ2hDLFlBQVksRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLFdBQVcsRUFBRTtZQUMvQyxhQUFhLEVBQUUsSUFBSSxDQUFDLGNBQWM7WUFDbEMsV0FBVyxFQUFFLElBQUksQ0FBQyxZQUFZO1lBQzlCLFFBQVEsRUFBRSxJQUFJLENBQUMsU0FBUztTQUN6QixDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0ksTUFBTSxDQUFDLFFBQVEsQ0FDcEIsSUFBeUIsRUFDekIsVUFBMEM7UUFFMUMsT0FBTyxJQUFJLFVBQVUsQ0FDbkIsOENBQWMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxFQUMzQyxJQUFJLENBQUMsU0FBUyxFQUNkO1lBQ0UsT0FBTyxFQUFFLDhDQUFjLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUM7WUFDaEQsVUFBVSxFQUFFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUM7WUFDckMsWUFBWSxFQUFFLElBQUksQ0FBQyxZQUFZO1lBQy9CLGFBQWEsRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNqQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO1NBQ3hCLENBQ0YsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNJLE1BQU0sQ0FBQyxLQUF1QjtRQUNuQyxJQUFJLENBQUMsS0FBSyxFQUFFLENBQUM7WUFDWCxPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7UUFFRCxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUM5QyxDQUFDO0lBRUQ7O09BRUc7SUFDSSxRQUFRO1FBQ2IsT0FBTyxHQUFHLElBQUksQ0FBQyxTQUFTLElBQUksSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLEVBQUUsR0FBRyxDQUFDO0lBQzFELENBQUM7Q0FDRjtBQWxSRCwwQ0FrUkMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFxkb21haW5cXGJhc2UtZG9tYWluLWV2ZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFVuaXF1ZUVudGl0eUlkIH0gZnJvbSAnLi4vdmFsdWUtb2JqZWN0cy91bmlxdWUtZW50aXR5LWlkLnZhbHVlLW9iamVjdCc7XHJcblxyXG4vKipcclxuICogQmFzZSBEb21haW4gRXZlbnRcclxuICogXHJcbiAqIEFic3RyYWN0IGJhc2UgY2xhc3MgZm9yIGFsbCBkb21haW4gZXZlbnRzIGluIHRoZSBzeXN0ZW0uXHJcbiAqIERvbWFpbiBldmVudHMgcmVwcmVzZW50IHNvbWV0aGluZyBpbXBvcnRhbnQgdGhhdCBoYXBwZW5lZCBpbiB0aGUgZG9tYWluLlxyXG4gKiBcclxuICogS2V5IGNoYXJhY3RlcmlzdGljczpcclxuICogLSBJbW11dGFibGUgb25jZSBjcmVhdGVkXHJcbiAqIC0gQ29udGFpbnMgYWxsIG5lY2Vzc2FyeSBpbmZvcm1hdGlvbiBhYm91dCB3aGF0IGhhcHBlbmVkXHJcbiAqIC0gQ2FuIGJlIHVzZWQgZm9yIGV2ZW50IHNvdXJjaW5nLCBpbnRlZ3JhdGlvbiBldmVudHMsIGFuZCBub3RpZmljYXRpb25zXHJcbiAqIC0gSW5jbHVkZXMgbWV0YWRhdGEgZm9yIHRyYWNraW5nIGFuZCBkZWJ1Z2dpbmdcclxuICogXHJcbiAqIEB0ZW1wbGF0ZSBUIFRoZSBldmVudCBkYXRhIHR5cGVcclxuICovXHJcbmV4cG9ydCBhYnN0cmFjdCBjbGFzcyBCYXNlRG9tYWluRXZlbnQ8VCA9IGFueT4ge1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgX2V2ZW50SWQ6IFVuaXF1ZUVudGl0eUlkO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgX29jY3VycmVkT246IERhdGU7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBfYWdncmVnYXRlSWQ6IFVuaXF1ZUVudGl0eUlkO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgX2V2ZW50VmVyc2lvbjogbnVtYmVyO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgX2V2ZW50RGF0YTogVDtcclxuICBwcml2YXRlIF9pc0Rpc3BhdGNoZWQ6IGJvb2xlYW4gPSBmYWxzZTtcclxuICBwcml2YXRlIF9kaXNwYXRjaGVkQXQ/OiBEYXRlO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgX2NvcnJlbGF0aW9uSWQ/OiBzdHJpbmc7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBfY2F1c2F0aW9uSWQ/OiBzdHJpbmc7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBfbWV0YWRhdGE6IFJlY29yZDxzdHJpbmcsIGFueT47XHJcblxyXG4gIGNvbnN0cnVjdG9yKFxyXG4gICAgYWdncmVnYXRlSWQ6IFVuaXF1ZUVudGl0eUlkLFxyXG4gICAgZXZlbnREYXRhOiBULFxyXG4gICAgb3B0aW9ucz86IHtcclxuICAgICAgZXZlbnRJZD86IFVuaXF1ZUVudGl0eUlkO1xyXG4gICAgICBvY2N1cnJlZE9uPzogRGF0ZTtcclxuICAgICAgZXZlbnRWZXJzaW9uPzogbnVtYmVyO1xyXG4gICAgICBjb3JyZWxhdGlvbklkPzogc3RyaW5nO1xyXG4gICAgICBjYXVzYXRpb25JZD86IHN0cmluZztcclxuICAgICAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG4gICAgfVxyXG4gICkge1xyXG4gICAgdGhpcy5fZXZlbnRJZCA9IG9wdGlvbnM/LmV2ZW50SWQgfHwgVW5pcXVlRW50aXR5SWQuZ2VuZXJhdGUoKTtcclxuICAgIHRoaXMuX29jY3VycmVkT24gPSBvcHRpb25zPy5vY2N1cnJlZE9uIHx8IG5ldyBEYXRlKCk7XHJcbiAgICB0aGlzLl9hZ2dyZWdhdGVJZCA9IGFnZ3JlZ2F0ZUlkO1xyXG4gICAgdGhpcy5fZXZlbnRWZXJzaW9uID0gb3B0aW9ucz8uZXZlbnRWZXJzaW9uIHx8IDE7XHJcbiAgICB0aGlzLl9ldmVudERhdGEgPSBldmVudERhdGE7XHJcbiAgICB0aGlzLl9jb3JyZWxhdGlvbklkID0gb3B0aW9ucz8uY29ycmVsYXRpb25JZDtcclxuICAgIHRoaXMuX2NhdXNhdGlvbklkID0gb3B0aW9ucz8uY2F1c2F0aW9uSWQ7XHJcbiAgICB0aGlzLl9tZXRhZGF0YSA9IG9wdGlvbnM/Lm1ldGFkYXRhIHx8IHt9O1xyXG5cclxuICAgIC8vIEZyZWV6ZSB0aGUgZXZlbnQgZGF0YSBhbmQgbWV0YWRhdGEgdG8gbWFrZSB0aGVtIGltbXV0YWJsZVxyXG4gICAgT2JqZWN0LmZyZWV6ZSh0aGlzLl9ldmVudERhdGEpO1xyXG4gICAgT2JqZWN0LmZyZWV6ZSh0aGlzLl9tZXRhZGF0YSk7XHJcbiAgICAvLyBOb3RlOiBXZSBkb24ndCBmcmVlemUgdGhlIGVudGlyZSBvYmplY3QgYmVjYXVzZSB3ZSBuZWVkIHRvIG1vZGlmeSBkaXNwYXRjaCBzdGF0dXNcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFVuaXF1ZSBpZGVudGlmaWVyIGZvciB0aGlzIGV2ZW50XHJcbiAgICovXHJcbiAgZ2V0IGV2ZW50SWQoKTogVW5pcXVlRW50aXR5SWQge1xyXG4gICAgcmV0dXJuIHRoaXMuX2V2ZW50SWQ7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBXaGVuIHRoZSBldmVudCBvY2N1cnJlZFxyXG4gICAqL1xyXG4gIGdldCBvY2N1cnJlZE9uKCk6IERhdGUge1xyXG4gICAgcmV0dXJuIHRoaXMuX29jY3VycmVkT247XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBUaGUgYWdncmVnYXRlIHRoYXQgZ2VuZXJhdGVkIHRoaXMgZXZlbnRcclxuICAgKi9cclxuICBnZXQgYWdncmVnYXRlSWQoKTogVW5pcXVlRW50aXR5SWQge1xyXG4gICAgcmV0dXJuIHRoaXMuX2FnZ3JlZ2F0ZUlkO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogVmVyc2lvbiBvZiB0aGUgZXZlbnQgc2NoZW1hXHJcbiAgICovXHJcbiAgZ2V0IGV2ZW50VmVyc2lvbigpOiBudW1iZXIge1xyXG4gICAgcmV0dXJuIHRoaXMuX2V2ZW50VmVyc2lvbjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFRoZSBldmVudCBkYXRhIHBheWxvYWRcclxuICAgKi9cclxuICBnZXQgZXZlbnREYXRhKCk6IFQge1xyXG4gICAgcmV0dXJuIHRoaXMuX2V2ZW50RGF0YTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFdoZXRoZXIgdGhpcyBldmVudCBoYXMgYmVlbiBkaXNwYXRjaGVkXHJcbiAgICovXHJcbiAgZ2V0IGlzRGlzcGF0Y2hlZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLl9pc0Rpc3BhdGNoZWQ7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBXaGVuIHRoZSBldmVudCB3YXMgZGlzcGF0Y2hlZFxyXG4gICAqL1xyXG4gIGdldCBkaXNwYXRjaGVkQXQoKTogRGF0ZSB8IHVuZGVmaW5lZCB7XHJcbiAgICByZXR1cm4gdGhpcy5fZGlzcGF0Y2hlZEF0O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ29ycmVsYXRpb24gSUQgZm9yIHRyYWNraW5nIHJlbGF0ZWQgZXZlbnRzXHJcbiAgICovXHJcbiAgZ2V0IGNvcnJlbGF0aW9uSWQoKTogc3RyaW5nIHwgdW5kZWZpbmVkIHtcclxuICAgIHJldHVybiB0aGlzLl9jb3JyZWxhdGlvbklkO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2F1c2F0aW9uIElEIGZvciB0cmFja2luZyBldmVudCBjaGFpbnNcclxuICAgKi9cclxuICBnZXQgY2F1c2F0aW9uSWQoKTogc3RyaW5nIHwgdW5kZWZpbmVkIHtcclxuICAgIHJldHVybiB0aGlzLl9jYXVzYXRpb25JZDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZGl0aW9uYWwgbWV0YWRhdGEgZm9yIHRoZSBldmVudFxyXG4gICAqL1xyXG4gIGdldCBtZXRhZGF0YSgpOiBSZWNvcmQ8c3RyaW5nLCBhbnk+IHtcclxuICAgIHJldHVybiB7IC4uLnRoaXMuX21ldGFkYXRhIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGV2ZW50IHR5cGUgbmFtZVxyXG4gICAqL1xyXG4gIGdldCBldmVudFR5cGUoKTogc3RyaW5nIHtcclxuICAgIHJldHVybiB0aGlzLmNvbnN0cnVjdG9yLm5hbWU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGV2ZW50IG5hbWUgZm9yIGV2ZW50IGVtaXR0ZXJcclxuICAgKi9cclxuICBnZXQgZXZlbnROYW1lKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudFR5cGU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBNYXJrIHRoZSBldmVudCBhcyBkaXNwYXRjaGVkXHJcbiAgICogVGhpcyBzaG91bGQgb25seSBiZSBjYWxsZWQgYnkgdGhlIGV2ZW50IGRpc3BhdGNoZXJcclxuICAgKi9cclxuICBwdWJsaWMgbWFya0FzRGlzcGF0Y2hlZCgpOiB2b2lkIHtcclxuICAgIGlmICh0aGlzLl9pc0Rpc3BhdGNoZWQpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBFdmVudCAke3RoaXMuX2V2ZW50SWQudG9TdHJpbmcoKX0gaGFzIGFscmVhZHkgYmVlbiBkaXNwYXRjaGVkYCk7XHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5faXNEaXNwYXRjaGVkID0gdHJ1ZTtcclxuICAgIHRoaXMuX2Rpc3BhdGNoZWRBdCA9IG5ldyBEYXRlKCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGFnZSBvZiB0aGUgZXZlbnQgaW4gbWlsbGlzZWNvbmRzXHJcbiAgICovXHJcbiAgcHVibGljIGdldEFnZSgpOiBudW1iZXIge1xyXG4gICAgcmV0dXJuIERhdGUubm93KCkgLSB0aGlzLl9vY2N1cnJlZE9uLmdldFRpbWUoKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgYWdlIG9mIHRoZSBldmVudCBpbiBzZWNvbmRzXHJcbiAgICovXHJcbiAgcHVibGljIGdldEFnZUluU2Vjb25kcygpOiBudW1iZXIge1xyXG4gICAgcmV0dXJuIE1hdGguZmxvb3IodGhpcy5nZXRBZ2UoKSAvIDEwMDApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSBhZ2Ugb2YgdGhlIGV2ZW50IGluIG1pbnV0ZXNcclxuICAgKi9cclxuICBwdWJsaWMgZ2V0QWdlSW5NaW51dGVzKCk6IG51bWJlciB7XHJcbiAgICByZXR1cm4gTWF0aC5mbG9vcih0aGlzLmdldEFnZSgpIC8gKDEwMDAgKiA2MCkpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgdGhlIGV2ZW50IGlzIHN0YWxlIChvbGRlciB0aGFuIHNwZWNpZmllZCB0aW1lKVxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBtYXhBZ2VNcyBNYXhpbXVtIGFnZSBpbiBtaWxsaXNlY29uZHNcclxuICAgKi9cclxuICBwdWJsaWMgaXNTdGFsZShtYXhBZ2VNczogbnVtYmVyKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5nZXRBZ2UoKSA+IG1heEFnZU1zO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIGEgbmV3IGV2ZW50IHdpdGggdXBkYXRlZCBtZXRhZGF0YVxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBuZXdNZXRhZGF0YSBBZGRpdGlvbmFsIG1ldGFkYXRhIHRvIG1lcmdlXHJcbiAgICovXHJcbiAgcHVibGljIHdpdGhNZXRhZGF0YShuZXdNZXRhZGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IHRoaXMge1xyXG4gICAgY29uc3QgY29uc3RydWN0b3IgPSB0aGlzLmNvbnN0cnVjdG9yIGFzIG5ldyAoLi4uYXJnczogYW55W10pID0+IHRoaXM7XHJcbiAgICByZXR1cm4gbmV3IGNvbnN0cnVjdG9yKHRoaXMuX2FnZ3JlZ2F0ZUlkLCB0aGlzLl9ldmVudERhdGEsIHtcclxuICAgICAgZXZlbnRJZDogdGhpcy5fZXZlbnRJZCxcclxuICAgICAgb2NjdXJyZWRPbjogdGhpcy5fb2NjdXJyZWRPbixcclxuICAgICAgZXZlbnRWZXJzaW9uOiB0aGlzLl9ldmVudFZlcnNpb24sXHJcbiAgICAgIGNvcnJlbGF0aW9uSWQ6IHRoaXMuX2NvcnJlbGF0aW9uSWQsXHJcbiAgICAgIGNhdXNhdGlvbklkOiB0aGlzLl9jYXVzYXRpb25JZCxcclxuICAgICAgbWV0YWRhdGE6IHsgLi4udGhpcy5fbWV0YWRhdGEsIC4uLm5ld01ldGFkYXRhIH0sXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBhIG5ldyBldmVudCB3aXRoIGEgY29ycmVsYXRpb24gSURcclxuICAgKiBcclxuICAgKiBAcGFyYW0gY29ycmVsYXRpb25JZCBUaGUgY29ycmVsYXRpb24gSUQgdG8gc2V0XHJcbiAgICovXHJcbiAgcHVibGljIHdpdGhDb3JyZWxhdGlvbklkKGNvcnJlbGF0aW9uSWQ6IHN0cmluZyk6IHRoaXMge1xyXG4gICAgY29uc3QgY29uc3RydWN0b3IgPSB0aGlzLmNvbnN0cnVjdG9yIGFzIG5ldyAoLi4uYXJnczogYW55W10pID0+IHRoaXM7XHJcbiAgICByZXR1cm4gbmV3IGNvbnN0cnVjdG9yKHRoaXMuX2FnZ3JlZ2F0ZUlkLCB0aGlzLl9ldmVudERhdGEsIHtcclxuICAgICAgZXZlbnRJZDogdGhpcy5fZXZlbnRJZCxcclxuICAgICAgb2NjdXJyZWRPbjogdGhpcy5fb2NjdXJyZWRPbixcclxuICAgICAgZXZlbnRWZXJzaW9uOiB0aGlzLl9ldmVudFZlcnNpb24sXHJcbiAgICAgIGNvcnJlbGF0aW9uSWQsXHJcbiAgICAgIGNhdXNhdGlvbklkOiB0aGlzLl9jYXVzYXRpb25JZCxcclxuICAgICAgbWV0YWRhdGE6IHRoaXMuX21ldGFkYXRhLFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgYSBuZXcgZXZlbnQgd2l0aCBhIGNhdXNhdGlvbiBJRFxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBjYXVzYXRpb25JZCBUaGUgY2F1c2F0aW9uIElEIHRvIHNldFxyXG4gICAqL1xyXG4gIHB1YmxpYyB3aXRoQ2F1c2F0aW9uSWQoY2F1c2F0aW9uSWQ6IHN0cmluZyk6IHRoaXMge1xyXG4gICAgY29uc3QgY29uc3RydWN0b3IgPSB0aGlzLmNvbnN0cnVjdG9yIGFzIG5ldyAoLi4uYXJnczogYW55W10pID0+IHRoaXM7XHJcbiAgICByZXR1cm4gbmV3IGNvbnN0cnVjdG9yKHRoaXMuX2FnZ3JlZ2F0ZUlkLCB0aGlzLl9ldmVudERhdGEsIHtcclxuICAgICAgZXZlbnRJZDogdGhpcy5fZXZlbnRJZCxcclxuICAgICAgb2NjdXJyZWRPbjogdGhpcy5fb2NjdXJyZWRPbixcclxuICAgICAgZXZlbnRWZXJzaW9uOiB0aGlzLl9ldmVudFZlcnNpb24sXHJcbiAgICAgIGNvcnJlbGF0aW9uSWQ6IHRoaXMuX2NvcnJlbGF0aW9uSWQsXHJcbiAgICAgIGNhdXNhdGlvbklkLFxyXG4gICAgICBtZXRhZGF0YTogdGhpcy5fbWV0YWRhdGEsXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENvbnZlcnQgZXZlbnQgdG8gSlNPTiByZXByZXNlbnRhdGlvblxyXG4gICAqL1xyXG4gIHB1YmxpYyB0b0pTT04oKTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBldmVudElkOiB0aGlzLl9ldmVudElkLnRvU3RyaW5nKCksXHJcbiAgICAgIGV2ZW50VHlwZTogdGhpcy5ldmVudFR5cGUsXHJcbiAgICAgIG9jY3VycmVkT246IHRoaXMuX29jY3VycmVkT24udG9JU09TdHJpbmcoKSxcclxuICAgICAgYWdncmVnYXRlSWQ6IHRoaXMuX2FnZ3JlZ2F0ZUlkLnRvU3RyaW5nKCksXHJcbiAgICAgIGV2ZW50VmVyc2lvbjogdGhpcy5fZXZlbnRWZXJzaW9uLFxyXG4gICAgICBldmVudERhdGE6IHRoaXMuX2V2ZW50RGF0YSxcclxuICAgICAgaXNEaXNwYXRjaGVkOiB0aGlzLl9pc0Rpc3BhdGNoZWQsXHJcbiAgICAgIGRpc3BhdGNoZWRBdDogdGhpcy5fZGlzcGF0Y2hlZEF0Py50b0lTT1N0cmluZygpLFxyXG4gICAgICBjb3JyZWxhdGlvbklkOiB0aGlzLl9jb3JyZWxhdGlvbklkLFxyXG4gICAgICBjYXVzYXRpb25JZDogdGhpcy5fY2F1c2F0aW9uSWQsXHJcbiAgICAgIG1ldGFkYXRhOiB0aGlzLl9tZXRhZGF0YSxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgZXZlbnQgZnJvbSBKU09OIHJlcHJlc2VudGF0aW9uXHJcbiAgICovXHJcbiAgcHVibGljIHN0YXRpYyBmcm9tSlNPTjxURXZlbnQgZXh0ZW5kcyBCYXNlRG9tYWluRXZlbnQ+KFxyXG4gICAganNvbjogUmVjb3JkPHN0cmluZywgYW55PixcclxuICAgIGV2ZW50Q2xhc3M6IG5ldyAoLi4uYXJnczogYW55W10pID0+IFRFdmVudFxyXG4gICk6IFRFdmVudCB7XHJcbiAgICByZXR1cm4gbmV3IGV2ZW50Q2xhc3MoXHJcbiAgICAgIFVuaXF1ZUVudGl0eUlkLmZyb21TdHJpbmcoanNvbi5hZ2dyZWdhdGVJZCksXHJcbiAgICAgIGpzb24uZXZlbnREYXRhLFxyXG4gICAgICB7XHJcbiAgICAgICAgZXZlbnRJZDogVW5pcXVlRW50aXR5SWQuZnJvbVN0cmluZyhqc29uLmV2ZW50SWQpLFxyXG4gICAgICAgIG9jY3VycmVkT246IG5ldyBEYXRlKGpzb24ub2NjdXJyZWRPbiksXHJcbiAgICAgICAgZXZlbnRWZXJzaW9uOiBqc29uLmV2ZW50VmVyc2lvbixcclxuICAgICAgICBjb3JyZWxhdGlvbklkOiBqc29uLmNvcnJlbGF0aW9uSWQsXHJcbiAgICAgICAgY2F1c2F0aW9uSWQ6IGpzb24uY2F1c2F0aW9uSWQsXHJcbiAgICAgICAgbWV0YWRhdGE6IGpzb24ubWV0YWRhdGEsXHJcbiAgICAgIH1cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDb21wYXJlIGV2ZW50cyBmb3IgZXF1YWxpdHlcclxuICAgKi9cclxuICBwdWJsaWMgZXF1YWxzKG90aGVyPzogQmFzZURvbWFpbkV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICBpZiAoIW90aGVyKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gdGhpcy5fZXZlbnRJZC5lcXVhbHMob3RoZXIuX2V2ZW50SWQpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGEgc3RyaW5nIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBldmVudFxyXG4gICAqL1xyXG4gIHB1YmxpYyB0b1N0cmluZygpOiBzdHJpbmcge1xyXG4gICAgcmV0dXJuIGAke3RoaXMuZXZlbnRUeXBlfSgke3RoaXMuX2V2ZW50SWQudG9TdHJpbmcoKX0pYDtcclxuICB9XHJcbn1cclxuIl0sInZlcnNpb24iOjN9