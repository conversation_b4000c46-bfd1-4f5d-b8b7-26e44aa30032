9f19a908dc3391eb218e56d8b93d1e9e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const circuit_breaker_service_1 = require("../circuit-breaker.service");
const circuit_breaker_1 = require("@/shared-kernel/patterns/circuit-breaker");
const service_unavailable_exception_1 = require("@/shared-kernel/exceptions/service-unavailable.exception");
describe('AICircuitBreakerService', () => {
    let service;
    const defaultConfig = {
        failureThreshold: 3,
        recoveryTimeout: 5000,
        monitoringPeriod: 10000,
        halfOpenMaxCalls: 2,
        enableFallback: false,
    };
    const fallbackConfig = {
        failureThreshold: 3,
        recoveryTimeout: 5000,
        monitoringPeriod: 10000,
        halfOpenMaxCalls: 2,
        enableFallback: true,
        fallbackResponse: { message: 'fallback response' },
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [circuit_breaker_service_1.AICircuitBreakerService],
        }).compile();
        service = module.get(circuit_breaker_service_1.AICircuitBreakerService);
    });
    afterEach(() => {
        // Clean up all registered providers
        service.resetAllProviders();
    });
    describe('registerProvider', () => {
        it('should register a new provider with circuit breaker', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            expect(service.isProviderAvailable(providerId)).toBe(true);
        });
        it('should register multiple providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            expect(service.isProviderAvailable('provider1')).toBe(true);
            expect(service.isProviderAvailable('provider2')).toBe(true);
            expect(service.getAvailableProviders()).toHaveLength(2);
        });
    });
    describe('executeWithProtection', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should execute successful operation', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toBe('success');
            expect(operation).toHaveBeenCalledTimes(1);
        });
        it('should handle single failure and continue', async () => {
            const operation = jest.fn()
                .mockRejectedValueOnce(new Error('failure'))
                .mockResolvedValue('success');
            // First call should fail
            await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            // Second call should succeed
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toBe('success');
        });
        it('should open circuit breaker after threshold failures', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail 3 times to reach threshold
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            // Circuit should now be open
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            // Next call should fail with circuit breaker exception
            await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
        });
        it('should use fallback function when circuit is open', async () => {
            // Register provider with fallback enabled
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', fallbackConfig);
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            const fallback = jest.fn().mockResolvedValue('fallback result');
            // Fail enough times to open circuit - with fallback enabled, these should return fallback responses
            for (let i = 0; i < 3; i++) {
                const result = await service.executeWithProtection('test-provider', operation);
                expect(result).toEqual({ message: 'fallback response' });
            }
            // Execute with fallback function - should use fallback function when circuit is open
            const result = await service.executeWithProtection('test-provider', operation, fallback);
            expect(result).toBe('fallback result');
            expect(fallback).toHaveBeenCalledTimes(1);
        });
        it('should use cached fallback response when available', async () => {
            // Register provider with fallback enabled
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', fallbackConfig);
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail enough times to open circuit - with fallback enabled, these should return fallback responses
            for (let i = 0; i < 3; i++) {
                const result = await service.executeWithProtection('test-provider', operation);
                expect(result).toEqual({ message: 'fallback response' });
            }
            // Circuit should now be open, and subsequent calls should still return fallback
            const result = await service.executeWithProtection('test-provider', operation);
            expect(result).toEqual({ message: 'fallback response' });
        });
        it('should throw error for unregistered provider', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await expect(service.executeWithProtection('unknown-provider', operation)).rejects.toThrow('Circuit breaker not registered for provider: unknown-provider');
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
            expect(metrics.providerType).toBe('test');
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(metrics.failureCount).toBe(0);
            expect(metrics.successCount).toBe(0);
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after operations', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await service.executeWithProtection('test-provider', operation);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.successCount).toBe(1);
            expect(metrics.totalCalls).toBe(1);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('isProviderAvailable', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return true for available provider', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
        });
        it('should return false for unregistered provider', () => {
            expect(service.isProviderAvailable('unknown-provider')).toBe(false);
        });
        it('should return false when circuit is open', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Fail enough times to open circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            expect(service.isProviderAvailable('test-provider')).toBe(false);
        });
    });
    describe('getAvailableProviders', () => {
        it('should return empty array when no providers available', () => {
            expect(service.getAvailableProviders()).toEqual([]);
        });
        it('should return all available providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const available = service.getAvailableProviders();
            expect(available).toHaveLength(2);
            expect(available).toContain('provider1');
            expect(available).toContain('provider2');
        });
        it('should exclude providers with open circuits', async () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            // Open circuit for provider1
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('provider1', operation)).rejects.toThrow('failure');
            }
            const available = service.getAvailableProviders();
            expect(available).toHaveLength(1);
            expect(available).toContain('provider2');
            expect(available).not.toContain('provider1');
        });
    });
    describe('resetProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should reset circuit breaker state', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('failure'));
            // Open the circuit
            for (let i = 0; i < 3; i++) {
                await expect(service.executeWithProtection('test-provider', operation)).rejects.toThrow('failure');
            }
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            // Reset the circuit
            service.resetProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.failureCount).toBe(0);
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
        });
    });
    describe('forceOpenProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should force circuit breaker to open state', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            service.forceOpenProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { failureThreshold: 5 };
            service.updateProviderConfig('test-provider', newConfig);
            // Verify the provider is still registered and available
            expect(service.isProviderAvailable('test-provider')).toBe(true);
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider circuit breaker', () => {
            expect(service.isProviderAvailable('test-provider')).toBe(true);
            service.unregisterProvider('test-provider');
            expect(service.isProviderAvailable('test-provider')).toBe(false);
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
    });
    describe('getHealthStatus', () => {
        it('should return empty object when no providers registered', () => {
            const status = service.getHealthStatus();
            expect(status).toEqual({});
        });
        it('should return health status for all providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const status = service.getHealthStatus();
            expect(Object.keys(status)).toHaveLength(2);
            expect(status['provider1']).toBeDefined();
            expect(status['provider1'].available).toBe(true);
            expect(status['provider1'].metrics).toBeDefined();
            expect(status['provider2']).toBeDefined();
            expect(status['provider2'].available).toBe(true);
            expect(status['provider2'].metrics).toBeDefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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