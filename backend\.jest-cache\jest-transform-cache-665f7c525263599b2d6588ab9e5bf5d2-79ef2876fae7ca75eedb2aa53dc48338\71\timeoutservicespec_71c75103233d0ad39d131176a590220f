940ffeeee8019c2efedb969fb7ec317a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const timeout_service_1 = require("../timeout.service");
describe('AITimeoutService', () => {
    let service;
    const defaultConfig = {
        defaultTimeout: 1000, // Use smaller timeouts for faster tests
        operationTimeouts: {
            'quick-operation': 500,
            'slow-operation': 2000,
        },
        escalationTimeouts: [1000, 1500, 2000],
        enableEscalation: true,
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [timeout_service_1.AITimeoutService],
        }).compile();
        service = module.get(timeout_service_1.AITimeoutService);
    });
    afterEach(() => {
        // Clean up all registered providers and cancel operations
        service.cancelAllOperations();
        service.resetAllMetrics();
    });
    describe('registerProvider', () => {
        it('should register a new provider with timeout configuration', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const config = service.getProviderConfig(providerId);
            expect(config).toEqual(defaultConfig);
        });
        it('should initialize metrics for registered provider', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const metrics = service.getProviderMetrics(providerId);
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe(providerId);
            expect(metrics.totalOperations).toBe(0);
            expect(metrics.timeoutCount).toBe(0);
        });
    });
    describe('executeWithTimeout', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should execute successful operation within timeout', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            const result = await service.executeWithTimeout('test-provider', operation);
            expect(result).toBe('success');
            expect(operation).toHaveBeenCalledTimes(1);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.timeoutCount).toBe(0);
        });
        it('should timeout operation that exceeds timeout limit', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1500));
                return 'success';
            });
            await expect(service.executeWithTimeout('test-provider', operation)).rejects.toThrow('Operation timed out after 1000ms');
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.timeoutCount).toBe(1);
            expect(metrics.timeoutRate).toBe(1);
        });
        it('should use operation-specific timeout', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 700));
                return 'success';
            });
            // Should timeout with quick-operation timeout (500ms)
            await expect(service.executeWithTimeout('test-provider', operation, 'quick-operation')).rejects.toThrow('Operation timed out after 500ms');
        });
        it('should use custom timeout when provided', async () => {
            // First test successful operation with custom timeout
            const quickOperation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 200));
                return 'success';
            });
            const result = await service.executeWithTimeout('test-provider', quickOperation, undefined, 500);
            expect(result).toBe('success');
            // Then test timeout with custom timeout (300ms)
            const slowOperation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 400));
                return 'success';
            });
            await expect(service.executeWithTimeout('test-provider', slowOperation, undefined, 300)).rejects.toThrow('Operation timed out after 300ms');
        });
        it('should throw error for unregistered provider', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            await expect(service.executeWithTimeout('unknown-provider', operation)).rejects.toThrow('Timeout configuration not registered for provider: unknown-provider');
        });
        it('should handle operation errors properly', async () => {
            const operation = jest.fn().mockRejectedValue(new Error('operation failed'));
            await expect(service.executeWithTimeout('test-provider', operation)).rejects.toThrow('operation failed');
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.timeoutCount).toBe(0); // Not a timeout error
        });
        it('should support abort signal in operation', async () => {
            const operation = jest.fn().mockImplementation(async (signal) => {
                return new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => resolve('success'), 500);
                    if (signal) {
                        signal.addEventListener('abort', () => {
                            clearTimeout(timeout);
                            reject(new Error('Operation aborted'));
                        });
                    }
                });
            });
            // Start operation and cancel it
            const promise = service.executeWithTimeout('test-provider', operation);
            // Cancel after a short delay
            setTimeout(() => {
                service.cancelProviderOperations('test-provider');
            }, 100);
            await expect(promise).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('executeWithTimeoutDetails', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return detailed timeout information for successful operation', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            const result = await service.executeWithTimeoutDetails('test-provider', operation);
            expect(result.result).toBe('success');
            expect(result.timedOut).toBe(false);
            expect(result.executionTime).toBeGreaterThan(90);
            expect(result.executionTime).toBeLessThan(200);
        });
        it('should return detailed timeout information for timed out operation', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1500));
                return 'success';
            });
            const result = await service.executeWithTimeoutDetails('test-provider', operation);
            expect(result.timedOut).toBe(true);
            expect(result.executionTime).toBeGreaterThan(900);
        });
    });
    describe('executeWithEscalation', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should use escalating timeouts for different attempts', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1200));
                return 'success';
            });
            // First attempt should timeout with 1000ms
            await expect(service.executeWithEscalation('test-provider', operation, 1)).rejects.toThrow('Operation timed out after 1000ms');
            // Second attempt should timeout with 1500ms (should succeed)
            const operation2 = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 1200));
                return 'success';
            });
            const result = await service.executeWithEscalation('test-provider', operation2, 2);
            expect(result).toBe('success');
        });
        it('should handle escalation when disabled', async () => {
            const configWithoutEscalation = {
                ...defaultConfig,
                enableEscalation: false,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithoutEscalation);
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            const result = await service.executeWithEscalation('test-provider', operation, 3);
            expect(result).toBe('success');
        });
    });
    describe('cancelProviderOperations', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should cancel active operations for a provider', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 2000));
                return 'success';
            });
            // Start multiple operations
            const promise1 = service.executeWithTimeout('test-provider', operation);
            const promise2 = service.executeWithTimeout('test-provider', operation);
            // Wait a bit to ensure operations are active
            await new Promise(resolve => setTimeout(resolve, 50));
            expect(service.getActiveOperationCount('test-provider')).toBe(2);
            const cancelledCount = service.cancelProviderOperations('test-provider');
            expect(cancelledCount).toBe(2);
            await expect(promise1).rejects.toThrow('Operation was cancelled');
            await expect(promise2).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('cancelAllOperations', () => {
        beforeEach(() => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
        });
        it('should cancel all active operations', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 2000));
                return 'success';
            });
            // Start operations for different providers
            const promise1 = service.executeWithTimeout('provider1', operation);
            const promise2 = service.executeWithTimeout('provider2', operation);
            // Wait a bit to ensure operations are active
            await new Promise(resolve => setTimeout(resolve, 50));
            expect(service.getTotalActiveOperationCount()).toBe(2);
            const cancelledCount = service.cancelAllOperations();
            expect(cancelledCount).toBe(2);
            await expect(promise1).rejects.toThrow('Operation was cancelled');
            await expect(promise2).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after operations', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            await service.executeWithTimeout('test-provider', operation);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            expect(metrics.averageExecutionTime).toBeGreaterThan(90);
            expect(metrics.maxExecutionTime).toBeGreaterThan(90);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('resetProviderMetrics', () => {
        beforeEach(async () => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
            // Generate some metrics
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'success';
            });
            await service.executeWithTimeout('test-provider', operation);
        });
        it('should reset metrics for specific provider', () => {
            // Verify metrics exist
            let metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(1);
            // Reset metrics
            service.resetProviderMetrics('test-provider');
            // Verify metrics are reset
            metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalOperations).toBe(0);
            expect(metrics.averageExecutionTime).toBe(0);
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { defaultTimeout: 5000 };
            service.updateProviderConfig('test-provider', newConfig);
            const config = service.getProviderConfig('test-provider');
            expect(config.defaultTimeout).toBe(5000);
            expect(config.enableEscalation).toBe(true); // Should keep existing values
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider configuration and metrics', () => {
            expect(service.getProviderConfig('test-provider')).toBeDefined();
            expect(service.getProviderMetrics('test-provider')).toBeDefined();
            service.unregisterProvider('test-provider');
            expect(service.getProviderConfig('test-provider')).toBeNull();
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
        it('should cancel active operations when unregistering', async () => {
            const operation = jest.fn().mockImplementation(async () => {
                await new Promise(resolve => setTimeout(resolve, 2000));
                return 'success';
            });
            const promise = service.executeWithTimeout('test-provider', operation);
            // Wait a bit to ensure operation is active
            await new Promise(resolve => setTimeout(resolve, 50));
            service.unregisterProvider('test-provider');
            await expect(promise).rejects.toThrow('Operation was cancelled');
        });
    });
    describe('static factory methods', () => {
        it('should create default timeout config', () => {
            const config = timeout_service_1.AITimeoutService.createDefaultConfig(60000);
            expect(config.defaultTimeout).toBe(60000);
            expect(config.enableEscalation).toBe(true);
            expect(config.escalationTimeouts).toHaveLength(3);
        });
        it('should create quick timeout config', () => {
            const config = timeout_service_1.AITimeoutService.createQuickConfig(3000);
            expect(config.defaultTimeout).toBe(3000);
            expect(config.operationTimeouts['quick-analysis']).toBe(2000);
            expect(config.enableEscalation).toBe(true);
        });
        it('should create long running timeout config', () => {
            const config = timeout_service_1.AITimeoutService.createLongRunningConfig(180000);
            expect(config.defaultTimeout).toBe(180000);
            expect(config.operationTimeouts['model-training']).toBe(300000);
            expect(config.escalationTimeouts).toHaveLength(4);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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